name: Backend CI

on:
  push:
    branches:
      - main
      - stage
      - ci/cd
    paths-ignore:
      - 'client/**'
      - '**.md'
      - '.gitignore'

jobs:
  build-backend:
    runs-on: ubuntu-latest
    env:
        IMAGE_NAME: afwtech/stage-chatwithkb-backend
    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Set Docker repository based on branch
      run: |
        if [[ "${GITHUB_REF_NAME}" == "main" ]]; then
          echo "IMAGE_NAME=afwtech/prod-chatwithkb-backend" >> $GITHUB_ENV
        else
          echo "IMAGE_NAME=afwtech/stage-chatwithkb-backend" >> $GITHUB_ENV
        fi
      shell: bash

    # Set up Docker Buildx
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      

    - name: Set Image Version
      id: image
      run: |
        DATE_TAG=$(date +'%Y%m%d-%H%M%S')
        SHORT_SHA=$(git rev-parse --short HEAD)
        IMAGE_VERSION="${DATE_TAG}-${SHORT_SHA}"
        echo "IMAGE_VERSION=${IMAGE_VERSION}" >> $GITHUB_ENV
        echo "::set-output name=version::${IMAGE_VERSION}"

    # Log in to DockerHub
    - name: Log in to DockerHub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_EMAIL }}
        password: ${{ secrets.DOCKERHUB_PASSWORD }}

    # Build the Docker image without using cache
    - name: Build Docker image
      run: |
        docker build -f Backend.Dockerfile -t ${{ env.IMAGE_NAME }}:${{ env.IMAGE_VERSION }} .

    # Push the Docker image to DockerHub
    - name: Push Docker image
      run: |
        docker push ${{ env.IMAGE_NAME }}:${{ env.IMAGE_VERSION }}