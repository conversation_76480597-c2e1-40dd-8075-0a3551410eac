# Poetry files
poetry.lock
poetry.toml

check_index_name_in_weaviate.py
# prefect.yaml
prefect/prod-prefect.yaml
prefect/stage-prefect.yaml
prefect/test-s3.py
retrieval_from_index_name_with_metadata.py
#folder
/storage


*venv

# Python cache files
*.pyc
*.pyo
*.pyd
__pycache__/

# IDE and editor files
.vscode/
.idea/
*.iml
*.ipr
*.iws

# Log files
*.log
logs/

# Temporary files
*.tmp
*.temp

# Byte-compiled Python files
*.pyc
*.pyo

# PyCharm files
.idea/
*.iml
*.ipr
*.iws
.idea/modules.xml
.idea/*.xml

# Pytest files
pytest.ini
.tox/

# Miscellaneous
*.env
.DS_Store
Thumbs.db
ehthumbs.db
ehthumbs_vista.db
*.bak
*.orig
*.rej
*.swp
*.swo
*.swn
*.tmp
*.tmp~
*.txt~


*venv
check_index_name_in_weaviate.py
/prefect.yaml
prefect/data_ingestion_v3/venv
prefect/data_ingestion_with_ocr/venv/
prefect/prod-prefect.yaml
prefect/stage-prefect.yaml
prefect/test-s3.py
retrieval_from_index_name_with_metadata.py

*venv
