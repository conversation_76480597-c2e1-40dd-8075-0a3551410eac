FROM python:3.11-slim

WORKDIR /app
RUN apt-get update && apt-get install -y gcc && rm -rf /var/lib/apt/lists/*
RUN pip install poetry uvicorn
COPY pyproject.toml poetry.lock* /app/
RUN poetry config virtualenvs.create false && poetry install --no-interaction --no-ansi --no-root
COPY . /app
ENV PYTHONPATH=/app
EXPOSE 3003
# CMD sh -c "uvicorn main:app --host 0.0.0.0 --port 8000 --workers 8 --ws-ping-interval 5 --ws-ping-timeout 300"
CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:3003", "--timeout", "600", "main:app", "--log-level", "debug"]
