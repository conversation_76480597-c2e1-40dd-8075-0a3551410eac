# Build Stage
FROM node:18-alpine AS build
WORKDIR /app
COPY client/package*.json ./
RUN npm install
COPY client/ ./
# Copy .env dynamically during build
ARG ENV=production
COPY ./configurations/.env.$ENV .env
RUN npm run build

# Serve Stage
FROM node:18-alpine
WORKDIR /app
COPY --from=build /app/dist ./dist
RUN npm install -g serve
COPY --from=build /app/.env ./dist/.env
EXPOSE 3002
CMD ["serve", "-s", "dist", "-l", "3002"]