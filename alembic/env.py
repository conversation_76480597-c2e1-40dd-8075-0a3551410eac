from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
import os

from alembic import context
from app.core.config import settings

# Import your Base and models
from app.database.utils import Base

# Import all models to ensure they are loaded
from app.database.models.user import User
from app.database.models.files import File
from app.database.models.folder import Folder
from app.database.models.agentbot import AgentBot
from app.database.models.upload_config import UploadConfig
from app.database.models.application_limits import ApplicationLimit
from app.database.models.chats import ChatSession, ChatMessage
from app.database.models.ratelimit import RateLimitRequest
from app.database.models.document_summary import DocumentSummary
from app.database.models.document_summary_file import DocumentSummaryFile
from app.database.models.user_model import UserModel


# Import any other models you have

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Explicitly set the target metadata
target_metadata = Base.metadata

# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)
# Load environment variables

# Database URL
DATABASE_URL = settings.DATABASE_URL
config.set_main_option("sqlalchemy.url", DATABASE_URL)


def run_migrations_offline():
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


# Run migrations based on the mode
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
