"""Create chat session and message tables

Revision ID: 288e2bf7be1c
Revises: e59617669f68
Create Date: 2024-12-19 11:55:18.026055

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSON
import uuid


# revision identifiers, used by Alembic.
revision: str = "288e2bf7be1c"
down_revision: Union[str, None] = "e59617669f68"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, "files", ["id"])
    op.create_unique_constraint(None, "folder", ["id"])
    op.create_unique_constraint(None, "users", ["id"])
    op.create_table(
        "chat_session",
        sa.Column(
            "id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True
        ),
        sa.Column("userid", sa.UUID(as_uuid=True), sa.<PERSON>ey("users.id"), nullable=False),
        sa.Column(
            "created_at", sa.DateTime, nullable=False, server_default=sa.text("now()")
        ),
        sa.Column("name", sa.String(), nullable=True),
    )

    # Create chat_message table
    op.create_table(
        "chat_message",
        sa.Column(
            "id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True
        ),
        sa.Column(
            "session_id",
            UUID(as_uuid=True),
            sa.ForeignKey("chat_session.id"),
            nullable=False,
        ),
        sa.Column( "type", sa.String(), nullable=False),
        sa.Column("content", JSON, nullable=False),
        sa.Column("metadata_text", JSON , nullable=False),
        sa.Column(
            "created_at", sa.DateTime, nullable=False, server_default=sa.text("now()")
        ),

    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "users", type_="unique")
    op.drop_constraint(None, "folder", type_="unique")
    op.drop_constraint(None, "files", type_="unique")
    # Drop chat_message table
    op.drop_table("chat_message")

    # Drop chat_session table
    op.drop_table("chat_session")
    # ### end Alembic commands ###
