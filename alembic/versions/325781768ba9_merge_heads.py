"""Merge Heads

Revision ID: 325781768ba9
Revises: 63c5013f7904, ea9065c97180
Create Date: 2025-01-13 20:08:40.184266

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '325781768ba9'
down_revision: Union[str, None] = ('63c5013f7904', 'ea9065c97180')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
