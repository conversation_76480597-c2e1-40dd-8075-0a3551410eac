"""merge ea81101c4df7 and 942f2961b7e2

Revision ID: 3267da8bfc70
Revises: 942f2961b7e2, ea81101c4df7
Create Date: 2025-02-18 18:39:43.403280

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3267da8bfc70'
down_revision: Union[str, None] = ('942f2961b7e2', 'ea81101c4df7')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
