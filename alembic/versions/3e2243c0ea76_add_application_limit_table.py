"""Add application limit table

Revision ID: 3e2243c0ea76
Revises: a32cdbcf10a3
Create Date: 2025-01-17 13:25:36.674791

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime


# revision identifiers, used by Alembic.
revision: str = '3e2243c0ea76'
down_revision: Union[str, None] = 'a32cdbcf10a3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the application_limit table
    op.create_table(
        'application_limit',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, nullable=False, unique=True, default=sa.text("uuid_generate_v4()")),
        sa.Column('userid', UUID(as_uuid=True), sa.<PERSON>ey('users.id'), nullable=False),
        sa.Column('is_paid', sa.Boolean, default=False, nullable=True),
        sa.Column('max_folder', sa.Integer, nullable=False, default=3),
        sa.Column('max_file', sa.Integer, nullable=False, default=3),
        sa.Column('max_tokens', sa.Integer, nullable=False, default=10000),  # NOT NULL column
        sa.Column('tokens_left', sa.Integer, default=10000),
        sa.Column('last_token_reset', sa.DateTime, default=datetime.utcnow),
    )

    # Insert missing entries for users into application_limit
    op.execute("""
        INSERT INTO application_limit (id, userid, is_paid, max_folder, max_file, max_tokens, tokens_left, last_token_reset)
        SELECT uuid_generate_v4(), id, 
               COALESCE(is_paid, FALSE), 
               COALESCE(max_folder, 3), 
               COALESCE(3, 3), 
               COALESCE(10000, 10000), 
               COALESCE(tokens_left, 10000), 
               COALESCE(last_token_reset, now())
        FROM users
        WHERE id NOT IN (SELECT userid FROM application_limit)
    """)

    # Remove columns from the 'users' table
    with op.batch_alter_table('users') as batch_op:
        batch_op.drop_column('is_paid')
        batch_op.drop_column('max_folder')
        batch_op.drop_column('tokens_left')
        batch_op.drop_column('last_token_reset')



def downgrade() -> None:
    # Add columns back to the 'users' table
    with op.batch_alter_table('users') as batch_op:
        batch_op.add_column(sa.Column('is_paid', sa.Boolean, default=False, nullable=True))
        batch_op.add_column(sa.Column('max_folder', sa.Integer, nullable=False, default=3))
        batch_op.add_column(sa.Column('tokens_left', sa.Integer, default=1000))
        batch_op.add_column(sa.Column('last_token_reset', sa.DateTime,nullable=True))

    # Migrate data back from application_limit to users
    op.execute("""
        UPDATE users
        SET is_paid = application_limit.is_paid,
            max_folder = application_limit.max_folder,
            tokens_left = application_limit.tokens_left,
            last_token_reset = application_limit.last_token_reset
        FROM application_limit
        WHERE users.id = application_limit.userid
    """)

    # Drop the application_limit table
    op.drop_table('application_limit')