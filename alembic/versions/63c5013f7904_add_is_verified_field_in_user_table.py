"""Add is_verified field in User table

Revision ID: 63c5013f7904
Revises: 288e2bf7be1c
Create Date: 2025-01-09 13:25:39.732382

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '63c5013f7904'
down_revision: Union[str, None] = '288e2bf7be1c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    This upgrade now ONLY adds the 'is_verified' column and handles
    index changes for 'users' table. The lines dropping 'chat_session'
    and 'chat_message' are removed/commented out.
    """
    # --- Removed/Commented Lines ---
    # op.drop_table('chat_session')
    # op.drop_table('chat_message')

    # --- Add the new 'is_verified' column ---
    op.add_column('users', sa.Column('is_verified', sa.<PERSON>(), nullable=True))


def downgrade() -> None:
    """
    Reverse the above operations: remove 'is_verified' and
    restore any altered indices or constraints.
    """
    # Since we removed the table drops in 'upgrade', do NOT recreate them here.
    # (That means we also remove the chat_session/chat_message re-creation lines.)

    op.drop_constraint(None, 'users', type_='unique')
    op.drop_index(op.f('ix_users_username'), table_name='users')

    op.create_index('ix_users_username', 'users', ['username'], unique=False)
    op.create_index(
        'unique_username_active',
        'users',
        ['username'],
        unique=True,
        postgresql_where='(is_deleted = false)'
    )
    op.create_index(
        'unique_email_active',
        'users',
        ['email'],
        unique=True,
        postgresql_where='(is_deleted = false)'
    )
    op.alter_column(
        'users',
        'tokens_left',
        existing_type=sa.INTEGER(),
        nullable=False
    )
    op.drop_column('users', 'is_verified')