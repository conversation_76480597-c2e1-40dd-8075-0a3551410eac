"""Add retriever table

Revision ID: 690bb8340dd1
Revises: 288e2bf7be1c
Create Date: 2025-01-03 19:01:21.348848

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '690bb8340dd1'
down_revision: Union[str, None] = '288e2bf7be1c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "upload_config",
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()'), unique=True),
        sa.Column("name", sa.String(length=64), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column('client_id', sa.UUID(as_uuid=True), nullable=True),
        sa.Column("is_default", sa.Boolean(), nullable=True),
        sa.Column("embedding_config", sa.JSON(), nullable=True),
        sa.Column("chunking_config", sa.JSON(), nullable=True),
        sa.Column("retriever_config", sa.JSON(), nullable=True),

        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("id"),
    )

    op.add_column('files', sa.Column('status', sa.String(length=50), nullable=False, server_default='Queue'))
    op.add_column('files', sa.Column('message', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'status')
    op.drop_column('files','message')
    op.drop_table("upload_config")

