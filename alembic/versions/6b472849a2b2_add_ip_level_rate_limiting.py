"""Add IP level rate limiting

Revision ID: 6b472849a2b2
Revises: 3e2243c0ea76
Create Date: 2025-01-24 14:47:06.002159

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "6b472849a2b2"
down_revision: Union[str, None] = "3e2243c0ea76"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "ratelimit_requests",
        sa.Column("id", sa.Integer(), primary_key=True, index=True),
        sa.Column("auth_id", sa.String(), nullable=False),  # <-- Added 'auth_id'
        sa.Column("ip_address", sa.String(), nullable=False),
        sa.Column("agent_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("request_time", sa.DateTime(), nullable=False),
    )
    op.create_index(
        "ix_ratelimit_requests_ip_address", "ratelimit_requests", ["ip_address"]
    )
    op.create_index(
        "ix_ratelimit_requests_agent_id", "ratelimit_requests", ["agent_id"]
    )
    op.create_index(
        "ix_ratelimit_requests_auth_id",
        "ratelimit_requests",
        ["auth_id"],
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # Drop indices created in the upgrade function
    op.drop_index("ix_ratelimit_requests_auth_id", table_name="ratelimit_requests")
    op.drop_index("ix_ratelimit_requests_ip_address", table_name="ratelimit_requests")
    op.drop_index("ix_ratelimit_requests_agent_id", table_name="ratelimit_requests")

    # Drop the table created in the upgrade function
    op.drop_table("ratelimit_requests")

    # ### end Alembic commands ###
