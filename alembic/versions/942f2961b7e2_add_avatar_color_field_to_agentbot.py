"""Add avatar color field to agentbot

Revision ID: 942f2961b7e2
Revises: 6b472849a2b2
Create Date: 2025-02-14 15:15:55.684079

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "942f2961b7e2"
down_revision: Union[str, None] = "6b472849a2b2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agentbot", sa.Column("avatar_color", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agentbot", "avatar_color")
    # ### end Alembic commands ###
