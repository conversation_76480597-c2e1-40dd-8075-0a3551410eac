"""Merge 325781768ba9 and 690bb8340dd1

Revision ID: a32cdbcf10a3
Revises: 690bb8340dd1
Create Date: 2025-01-17 12:33:24.875420

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a32cdbcf10a3'
down_revision = ('325781768ba9', '690bb8340dd1')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
