"""Add csv stored data field to file

Revision ID: b7ec142ddc0a
Revises: 3267da8bfc70
Create Date: 2025-03-12 21:33:03.675687

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB
from datetime import datetime

# revision identifiers, used by Alembic.
revision: str = "b7ec142ddc0a"
down_revision: Union[str, None] = "3267da8bfc70"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Add 'csv_data' column with JSONB type
    op.add_column("files", sa.Column("csv_data", JSONB, nullable=True))

    # Add 'updated_at' column with timezone-aware datetime and automatic update
    op.add_column(
        "files",
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            nullable=True,
            server_default=sa.text("CURRENT_TIMESTAMP"),
            onupdate=datetime.utcnow,
        ),
    )

    # Update existing rows where updated_at is NULL
    op.execute(
        "UPDATE files SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL"
    )


def downgrade():
    # Drop 'csv_data' column
    op.drop_column("files", "csv_data")

    # Drop 'updated_at' column
    op.drop_column("files", "updated_at")
