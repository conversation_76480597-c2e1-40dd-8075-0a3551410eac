"""Re: Add foreign keys to folder and files
Revision ID: e59617669f68
Revises: a13cce3f6c00
Create Date: 2024-12-18 10:57:06.014538
"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'e59617669f68'
down_revision: Union[str, None] = 'fbb03beb4e6b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade():
    # Enable uuid-ossp extension
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')
    
    # Create the `folder` table
    op.create_table(
        'folder',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()'), unique=True),
        sa.Column('name', sa.String(), index=True, nullable=False),
        sa.Column('userid', sa.UUID(as_uuid=True), sa.<PERSON>ey('users.id', name='fk_folder_userid'), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('is_deleted', sa.Boolean(), default=False),
        sa.UniqueConstraint('name', 'userid', 'is_deleted', name='uq_folder_folder_name_user_id_is_deleted'),
        info={'if_not_exists': True}
    )
    
    # Create the `files` table
    op.create_table(
        'files',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()'), unique=True),
        sa.Column('name', sa.String(), index=True, nullable=False),
        sa.Column('type', sa.String(), nullable=False),
        sa.Column('size', sa.Integer(), nullable=True),
        sa.Column('folderid', sa.UUID(as_uuid=True), sa.ForeignKey('folder.id', name='fk_files_folderid'), nullable=False),
        sa.Column('meta_data', sa.String(), nullable=True),
        sa.Column('is_deleted', sa.Boolean(), default=False),
        sa.Column('s3_url', sa.String(), nullable=True),
        info={'if_not_exists': True}
    )

def downgrade():
    # Drop tables in reverse order of creation
    op.drop_table('files')
    op.drop_table('folder')