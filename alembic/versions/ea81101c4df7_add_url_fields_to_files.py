"""add_url_fields_to_files

Revision ID: ea81101c4df7
Revises: 6b472849a2b2
Create Date: 2025-02-10 18:25:00.162727

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "ea81101c4df7"
down_revision: Union[str, None] = "6b472849a2b2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Change meta_data column type from String to JSON
    op.alter_column(
        "files",
        "meta_data",
        type_=postgresql.JSON,
        existing_type=sa.String(),
        postgresql_using="meta_data::json",
    )

    # Rename s3_url column to url
    op.alter_column(
        "files",
        "s3_url",
        new_column_name="url",
        existing_type=sa.String(),
        existing_nullable=True,
    )


def downgrade() -> None:
    # Change meta_data column type back to String
    op.alter_column(
        "files",
        "meta_data",
        type_=sa.String(),
        existing_type=postgresql.JSON,
        postgresql_using="meta_data::text",
    )

    # Rename url column back to s3_url
    op.alter_column(
        "files",
        "url",
        new_column_name="s3_url",
        existing_type=sa.String(),
        existing_nullable=True,
    )
