"""
create_agentbot_table

Revision ID: ea9065c97180
Revises: 288e2bf7be1c
Create Date: 2025-01-08 21:42:43.380128
"""

from alembic import op
import sqlalchemy as sa

# Revision identifiers, used by Alembic
revision = 'ea9065c97180'
down_revision = '288e2bf7be1c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Upgrade database schema to create the 'agentbot' table.
    """
    op.create_table(
        'agentbot',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), sa.<PERSON>ey('users.id', ondelete="CASCADE"), nullable=False),
        sa.Column('agent_name', sa.String(32), nullable=False),
        sa.Column('folder_id', sa.UUID(), sa.ForeignKey('folder.id', ondelete="CASCADE"), nullable=False),
        sa.Column('agent_avatar', sa.String(), nullable=True),
        sa.Column('authorized_domain', sa.String(), nullable=True),
        sa.Column('color_theme', sa.String(), nullable=True),
        sa.Column('is_exposed', sa.Boolean(), nullable=False, default=False),
        sa.Column('instructions', sa.String(), nullable=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('agent_name', 'user_id', 'is_deleted', name='uq_agentbot_agent_name_user_id_is_deleted')
    )
    op.create_index('ix_agentbot_agent_name', 'agentbot', ['agent_name'], unique=False)

    op.add_column('chat_session', sa.Column('agentid', sa.UUID(),sa.ForeignKey('agentbot.id', ondelete="CASCADE"), nullable=False))



def downgrade() -> None:
    """
    Downgrade database schema by dropping the 'agentbot' table.
    """
    op.drop_index('ix_agentbot_agent_name', table_name='agentbot')
    op.drop_table('agentbot')
    op.drop_column('chat_session', 'agentid')
