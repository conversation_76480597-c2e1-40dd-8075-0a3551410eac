"""add_document_summarizer_tables

Revision ID: eecdf9e0f58a
Revises: 2aab4e1b2ac7
Create Date: 2025-07-20 04:15:06.243226

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'eecdf9e0f58a'
down_revision: Union[str, None] = '2aab4e1b2ac7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document_summaries',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('prompt', sa.String(), nullable=True),
    sa.Column('summary', sa.String(), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_index(op.f('ix_document_summaries_name'), 'document_summaries', ['name'], unique=False)
    op.create_table('document_summary_files',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('size', sa.Integer(), nullable=True),
    sa.Column('path', sa.String(), nullable=False),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('document_summary_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['document_summary_id'], ['document_summaries.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_summary_files_name'), 'document_summary_files', ['name'], unique=False)
    op.drop_index(op.f('ix_ratelimit_requests_agent_id'), table_name='ratelimit_requests')
    op.drop_index(op.f('ix_ratelimit_requests_auth_id'), table_name='ratelimit_requests')
    op.drop_index(op.f('ix_ratelimit_requests_id'), table_name='ratelimit_requests')
    op.drop_index(op.f('ix_ratelimit_requests_ip_address'), table_name='ratelimit_requests')
    op.drop_table('ratelimit_requests')
    op.create_index(op.f('ix_agentbot_id'), 'agentbot', ['id'], unique=False)
    op.create_unique_constraint(None, 'agentbot', ['folder_id'])
    op.create_unique_constraint(None, 'application_limit', ['id'])
    op.alter_column('chat_message', 'content',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('chat_message', 'metadata_text',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('chat_message', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.create_unique_constraint(None, 'chat_message', ['id'])
    op.drop_column('chat_message', 'plots')
    op.alter_column('chat_plot', 'title',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_unique_constraint(None, 'chat_plot', ['id'])
    op.drop_constraint(op.f('chat_plot_message_id_fkey'), 'chat_plot', type_='foreignkey')
    op.create_foreign_key(None, 'chat_plot', 'chat_message', ['message_id'], ['id'])
    op.alter_column('chat_session', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.create_unique_constraint(None, 'chat_session', ['id'])
    op.drop_constraint(op.f('chat_session_agentid_fkey'), 'chat_session', type_='foreignkey')
    op.create_foreign_key(None, 'chat_session', 'agentbot', ['agentid'], ['id'])
    op.alter_column('files', 'csv_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_constraint(op.f('uq_folder_folder_name_user_id_is_deleted'), 'folder', type_='unique')
    op.alter_column('upload_config', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('upload_config', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.create_index(op.f('ix_upload_config_client_id'), 'upload_config', ['client_id'], unique=False)
    op.create_unique_constraint(None, 'upload_config', ['id'])
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('unique_email_active'), table_name='users', postgresql_where='(is_deleted = false)')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('unique_email_active'), 'users', ['email'], unique=True, postgresql_where='(is_deleted = false)')
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)
    op.drop_constraint(None, 'upload_config', type_='unique')
    op.drop_index(op.f('ix_upload_config_client_id'), table_name='upload_config')
    op.alter_column('upload_config', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('upload_config', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_unique_constraint(op.f('uq_folder_folder_name_user_id_is_deleted'), 'folder', ['name', 'userid', 'is_deleted'], postgresql_nulls_not_distinct=False)
    op.alter_column('files', 'csv_data',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.drop_constraint(None, 'chat_session', type_='foreignkey')
    op.create_foreign_key(op.f('chat_session_agentid_fkey'), 'chat_session', 'agentbot', ['agentid'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'chat_session', type_='unique')
    op.alter_column('chat_session', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.drop_constraint(None, 'chat_plot', type_='foreignkey')
    op.create_foreign_key(op.f('chat_plot_message_id_fkey'), 'chat_plot', 'chat_message', ['message_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'chat_plot', type_='unique')
    op.alter_column('chat_plot', 'title',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.add_column('chat_message', sa.Column('plots', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'chat_message', type_='unique')
    op.alter_column('chat_message', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('chat_message', 'metadata_text',
               existing_type=sa.Text(),
               type_=postgresql.JSON(astext_type=sa.Text()),
               existing_nullable=False)
    op.alter_column('chat_message', 'content',
               existing_type=sa.Text(),
               type_=postgresql.JSON(astext_type=sa.Text()),
               existing_nullable=False)
    op.drop_constraint(None, 'application_limit', type_='unique')
    op.drop_constraint(None, 'agentbot', type_='unique')
    op.drop_index(op.f('ix_agentbot_id'), table_name='agentbot')
    op.create_table('ratelimit_requests',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('auth_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('ip_address', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('agent_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('request_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('ratelimit_requests_pkey'))
    )
    op.create_index(op.f('ix_ratelimit_requests_ip_address'), 'ratelimit_requests', ['ip_address'], unique=False)
    op.create_index(op.f('ix_ratelimit_requests_id'), 'ratelimit_requests', ['id'], unique=False)
    op.create_index(op.f('ix_ratelimit_requests_auth_id'), 'ratelimit_requests', ['auth_id'], unique=False)
    op.create_index(op.f('ix_ratelimit_requests_agent_id'), 'ratelimit_requests', ['agent_id'], unique=False)
    op.drop_index(op.f('ix_document_summary_files_name'), table_name='document_summary_files')
    op.drop_table('document_summary_files')
    op.drop_index(op.f('ix_document_summaries_name'), table_name='document_summaries')
    op.drop_table('document_summaries')
    # ### end Alembic commands ###
