"""add_user_model_table

Revision ID: f6ee79b37ba9
Revises: eecdf9e0f58a
Create Date: 2025-07-23 17:03:09.187996

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = 'f6ee79b37ba9'
down_revision: Union[str, None] = 'eecdf9e0f58a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the user_model table
    op.create_table(
        'user_model',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, nullable=False, unique=True, default=sa.text("uuid_generate_v4()")),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('model_id', sa.String, nullable=False),
    )

    # Insert default model for all existing users
    op.execute("""
        INSERT INTO user_model (id, user_id, model_id)
        SELECT uuid_generate_v4(), id, 'mistral.mistral-large-2402-v1:0'
        FROM users
        WHERE id NOT IN (SELECT user_id FROM user_model)
    """)


def downgrade() -> None:
    # Drop the user_model table
    op.drop_table('user_model')
