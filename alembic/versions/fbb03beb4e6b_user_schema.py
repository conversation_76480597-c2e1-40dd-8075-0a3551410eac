from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'fbb03beb4e6b'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # Create the users table
    op.create_table(
        'users',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('username', sa.String(), nullable=False),
        sa.Column('password', sa.String(), nullable=False),
        sa.Column('profile_image', sa.String(), nullable=True),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_login_at', sa.DateTime(), nullable=True),
        sa.Column('is_paid', sa.Boolean(), nullable=True),
        sa.Column('max_folder', sa.Integer(), nullable=False, default=3),
        sa.Column('tokens_left', sa.Integer(), nullable=False, default=1000),
        sa.Column('last_token_reset', sa.DateTime(), nullable=True),
        sa.Column('is_deleted', sa.Boolean(), default=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email', name='uq_users_email')
    )

    # Add unique index for username (unique only for non-deleted users)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)  # Non-unique index for all users

    # Create a partial unique constraint on email (only for active users)
    op.execute("""
    CREATE UNIQUE INDEX unique_email_active 
    ON users (email) 
    WHERE is_deleted = FALSE;
    """)

   
def downgrade() -> None:
    # Drop the partial unique index on email and username
    op.execute("DROP INDEX IF EXISTS unique_email_active")

    # Drop the users table and the non-unique username index
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')
