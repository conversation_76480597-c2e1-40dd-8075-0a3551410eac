<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Reset styles for email clients */
        body {{
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            line-height: 1.6;
            background-color: #f4f4f4;
        }}
        .container {{
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }}
        .header {{
            text-align: center;
            padding: 20px 0;
            background-color: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }}
        .content {{
            padding: 30px 20px;
            text-align: center;
        }}
        .button {{
            display: inline-block;
            padding: 12px 24px;
            margin: 20px 0;
            background-color: #007bff;
            color: #ffffff;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
        }}
        .button:hover {{
            background-color: #0056b3;
        }}
        .fallback-url {{
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            word-break: break-all;
            font-family: monospace;
        }}
        .expiry-notice {{
            margin-top: 20px;
            padding: 10px;
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            border-radius: 4px;
            color: #856404;
        }}
        .countdown {{
            font-weight: bold;
            color: #dc3545;
        }}
        .footer {{
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="../constants/ai_planet_logo.png" alt="AI Planet" />
        </div>
        <div class="content">
            <h2>Verify Your Email Address</h2>
            <p>Thank you for signing up! Please click the button below to verify your email address:</p>
            
            <!-- Primary CTA Button -->
            <a href="{verification_link}" class="button" target="_blank">Verify Email</a>
            
            <!-- Fallback URL section -->
            <p>If the button doesn't work, copy and paste this URL into your browser:</p>
            <div class="fallback-url">
                {verification_link}
            </div>
            
            <!-- Expiry Notice with Countdown -->
            <div class="expiry-notice">
                This verification link will expire in 
                <span id="static-countdown" class="countdown">{self_token_expire_minutes}</span> minutes
                <span id="dynamic-countdown" class="countdown" style="display: none;"></span>
            </div>

            <script>
                function startCountdown() {{
                    const minutes = {self_token_expire_minutes};
                    const endTime = new Date().getTime() + (minutes * 60 * 1000);
                    
                    document.getElementById('static-countdown').style.display = 'none';
                    document.getElementById('dynamic-countdown').style.display = 'inline';

                    const timer = setInterval(function() {{
                        const now = new Date().getTime();
                        const distance = endTime - now;
                        
                        const minutesLeft = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        const secondsLeft = Math.floor((distance % (1000 * 60)) / 1000);
                        
                        document.getElementById('dynamic-countdown').innerHTML = 
                            minutesLeft + ' minutes ' + secondsLeft + ' seconds';
                        
                        if (distance < 0) {{
                            clearInterval(timer);
                            document.getElementById('dynamic-countdown').innerHTML = 'EXPIRED';
                        }}
                    }}, 1000);
                }}

                if (document.readyState === 'loading') {{
                    document.addEventListener('DOMContentLoaded', startCountdown);
                }} else {{
                    startCountdown();
                }}
            </script>
            
            <div class="footer">
                <p>If you didn't request this verification, please ignore this email.</p>
                <p>© 2025 AI Planet. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>