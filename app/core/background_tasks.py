import asyncio
import logging
from typing import Dict, Any, Optional
from uuid import UUID
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from datetime import datetime, timedelta

from app.database.utils import get_db
from services.document_summarizer_service import document_summarizer_service

logger = logging.getLogger(__name__)


class BackgroundTaskManager:
    """Manages background tasks for document processing"""

    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_metadata: Dict[str, Dict[str, Any]] = {}

    async def process_document_summary_background(
        self, summary_id: UUID, custom_prompt: Optional[str] = None
    ):
        """
        Process a document summary in the background

        Args:
            summary_id: ID of the document summary to process
            custom_prompt: Optional custom prompt for summarization
        """
        task_id = f"summary_{summary_id}"

        try:
            # Update task metadata
            self.task_metadata[task_id] = {
                "status": "running",
                "started_at": datetime.utcnow(),
                "summary_id": str(summary_id),
                "message": "Processing started",
            }

            # Create a new database session for the background task
            db = next(get_db())

            logger.info(f"Starting background processing for summary {summary_id}")

            # Process the summary
            result = await document_summarizer_service.process_document_summary(
                summary_id=summary_id, db=db, custom_prompt=custom_prompt
            )

            # Update task metadata with success
            self.task_metadata[task_id] = {
                "status": "completed",
                "started_at": self.task_metadata[task_id]["started_at"],
                "completed_at": datetime.utcnow(),
                "summary_id": str(summary_id),
                "message": "Processing completed successfully",
                "result": result,
            }

            logger.info(
                f"Completed background processing for summary {summary_id}: {result['status']}"
            )

        except Exception as e:
            logger.error(
                f"Error in background processing for summary {summary_id}: {e}"
            )

            # Update task metadata with error
            self.task_metadata[task_id] = {
                "status": "failed",
                "started_at": self.task_metadata.get(task_id, {}).get(
                    "started_at", datetime.utcnow()
                ),
                "completed_at": datetime.utcnow(),
                "summary_id": str(summary_id),
                "message": f"Processing failed: {str(e)}",
                "error": str(e),
            }
        finally:
            # Clean up task reference but keep metadata for a while
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    def start_background_processing(
        self, summary_id: UUID, custom_prompt: Optional[str] = None
    ) -> str:
        """
        Start background processing for a document summary

        Args:
            summary_id: ID of the document summary to process
            custom_prompt: Optional custom prompt for summarization

        Returns:
            Task ID for tracking
        """
        task_id = f"summary_{summary_id}"

        # Check if task is already running
        if task_id in self.running_tasks and not self.running_tasks[task_id].done():
            return task_id

        # Create and start the background task
        task = asyncio.create_task(
            self.process_document_summary_background(summary_id, custom_prompt)
        )
        self.running_tasks[task_id] = task

        logger.info(f"Started background task {task_id} for summary {summary_id}")
        return task_id

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of a background task

        Args:
            task_id: ID of the task to check

        Returns:
            Dict containing task status information
        """
        # Check if task is currently running
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]

            if task.done():
                if task.exception():
                    return {
                        "task_id": task_id,
                        "status": "failed",
                        "message": str(task.exception()),
                        "error": str(task.exception()),
                    }
                else:
                    return {
                        "task_id": task_id,
                        "status": "completed",
                        "message": "Task completed successfully",
                    }
            else:
                return {
                    "task_id": task_id,
                    "status": "running",
                    "message": "Task is currently running",
                }

        # Check if we have metadata for this task (completed/failed tasks)
        if task_id in self.task_metadata:
            metadata = self.task_metadata[task_id]
            return {
                "task_id": task_id,
                "status": metadata["status"],
                "message": metadata["message"],
                "started_at": metadata.get("started_at"),
                "completed_at": metadata.get("completed_at"),
                "error": metadata.get("error"),
            }

        # Task not found - check if it might be a completed task that was cleaned up
        # For summary tasks, we can check the database status
        if task_id.startswith("summary_"):
            try:
                summary_id = task_id.replace("summary_", "")
                db = next(get_db())
                from app.database.models.document_summary import DocumentSummary

                summary = (
                    db.query(DocumentSummary)
                    .filter(DocumentSummary.id == summary_id)
                    .first()
                )

                if summary:
                    if summary.status == "completed":
                        return {
                            "task_id": task_id,
                            "status": "completed",
                            "message": "Summary processing completed",
                            "summary_status": summary.status,
                        }
                    elif summary.status == "failed":
                        return {
                            "task_id": task_id,
                            "status": "failed",
                            "message": "Summary processing failed",
                            "summary_status": summary.status,
                            "error": (
                                summary.meta_data.get("error")
                                if summary.meta_data
                                else "Unknown error"
                            ),
                        }
                    elif summary.status == "in_progress":
                        return {
                            "task_id": task_id,
                            "status": "running",
                            "message": "Summary processing in progress",
                            "summary_status": summary.status,
                        }
            except Exception as e:
                logger.error(f"Error checking database status for task {task_id}: {e}")

        return {
            "task_id": task_id,
            "status": "not_found",
            "message": "Task not found or has been cleaned up",
        }

    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a running background task

        Args:
            task_id: ID of the task to cancel

        Returns:
            True if task was cancelled, False otherwise
        """
        if task_id not in self.running_tasks:
            return False

        task = self.running_tasks[task_id]

        if not task.done():
            task.cancel()
            logger.info(f"Cancelled background task {task_id}")
            return True

        return False

    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all running tasks

        Returns:
            Dict mapping task IDs to their status
        """
        return {
            task_id: self.get_task_status(task_id)
            for task_id in self.running_tasks.keys()
        }

    def cleanup_old_metadata(self, max_age_hours: int = 24):
        """
        Clean up old task metadata to prevent memory leaks

        Args:
            max_age_hours: Maximum age in hours to keep metadata
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)

        tasks_to_remove = []
        for task_id, metadata in self.task_metadata.items():
            completed_at = metadata.get("completed_at")
            if completed_at and completed_at < cutoff_time:
                tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.task_metadata[task_id]

        if tasks_to_remove:
            logger.info(f"Cleaned up {len(tasks_to_remove)} old task metadata entries")


# Global background task manager instance
background_task_manager = BackgroundTaskManager()


def add_background_processing_task(
    background_tasks: BackgroundTasks,
    summary_id: UUID,
    custom_prompt: Optional[str] = None,
):
    """
    Add a background processing task to FastAPI's background tasks

    Args:
        background_tasks: FastAPI BackgroundTasks instance
        summary_id: ID of the document summary to process
        custom_prompt: Optional custom prompt for summarization
    """

    def process_task():
        # Create event loop for the background task
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Run the background processing
        loop.run_until_complete(
            background_task_manager.process_document_summary_background(
                summary_id, custom_prompt
            )
        )

    background_tasks.add_task(process_task)
