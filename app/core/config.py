from typing import Dict, List
from pydantic_settings import BaseSettings
from pathlib import Path
from pydantic import Field, field_validator
import json


class Settings(BaseSettings):
    DATABASE_URL: str
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 4250

    PROTOCOL: str = "https"
    HOST: str = "127.0.0.1"
    PORT: int = 7860
    RELOAD: bool = True

    AWS_ACCESS_KEY_ID: str = ''
    AWS_SECRET_ACCESS_KEY: str = ''
    AWS_DEFAULT_REGION: str= ''
    AWS_BUCKET_NAME: str = ''
    UPLOAD_FOLDER: str
    AZURE_EMBEDDING_API_KEY: str = ''
    AZURE_EMBEDDING_API_URL: str = ''
    MODEL_NAME: str = ''
    WEAVIATE_URL: str = ''
    WEAVIATE_API_KEY: str= ''
    LLAMAINDEX_API: str= ''
    AZUREOPENAI_API_KEY: str= ''
    GOOGLE_API_KEY: str = ''
    SMTP_SERVER: str = ''
    SMTP_USERNAME: str = ''
    SMTP_PASSWORD: str = ''
    GOOGLE_SECRET_KEY: str = ''
    PREFECT_API_URL: str = ''
    PREFECT_API_KEY: str = ''
    GOOGLE_CLIENT_ID: str = ''
    COHERE_API_KEY: str = ''
    BEDROCK_ACCESS_KEY: str
    BEDROCK_SECRET_KEY: str
    BEDROCK_REGION: str
    TOP_K: int = 20
    ALPHA: float = 0.5
    USE_AZURE_ONLY: bool = False
    GEMINI_MODEL: str = "gemini-2.0-flash-lite-preview-02-05"
    SENDER_EMAIL: str = "<EMAIL>"
    ALLOWED_MAX_TOKENS: int = 10000
    PREFECT_DEPLOYMENT_NAME_FILE_UPLOAD: str = "chat_with_kb_v6"
    PREFECT_DEPLOYMENT_NAME_URL_PROCESSING: str = "chat_with_kb_url_hybrid"
    MAX_REQUESTS: int = 100
    DB_OVERFLOW: int = 40
    AZURE_OPENAI_ENDPOINT: str = ''
    IS_BEDROCK: bool = False

    # ALLOWED_ORIGINS: List[str] = Field(default=[])
    # @field_validator("ALLOWED_ORIGINS", mode="before")
    # def parse_allowed_origins(cls, value):
    #     if isinstance(value, str):
    #         try:
    #             return json.loads(value)
    #         except json.JSONDecodeError:
    #             # If not JSON, assume it is a comma-separated string
    #             return [origin.strip() for origin in value.split(",")]
    #     elif isinstance(value, list):
    #         return value
    #     elif not value:  # Handle empty or None values
    #         return []
    #     raise ValueError("ALLOWED_ORIGINS must be a list, JSON array, or a comma-separated string.")

    class Config:
        env_file = Path(__file__).parent.parent.parent / ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "allow"


# Initialize settings
settings = Settings()
