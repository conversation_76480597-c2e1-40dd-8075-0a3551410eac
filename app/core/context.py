from contextvars import Context<PERSON><PERSON>
from typing import Optional

# Context variable to store the current user's model ID
current_user_model: ContextVar[Optional[str]] = ContextVar('current_user_model', default=None)

# Context variable to store the current user ID
current_user_id: ContextVar[Optional[str]] = ContextVar('current_user_id', default=None)

# Context variable to store the current request type
current_request_type: ContextVar[Optional[str]] = ContextVar('current_request_type', default=None)


def set_user_model(model_id: str) -> None:
    """Set the current user's model ID in context"""
    current_user_model.set(model_id)


def get_user_model() -> Optional[str]:
    """Get the current user's model ID from context"""
    return current_user_model.get()


def set_user_id(user_id: str) -> None:
    """Set the current user ID in context"""
    current_user_id.set(user_id)


def get_user_id() -> Optional[str]:
    """Get the current user ID from context"""
    return current_user_id.get()


def set_request_type(request_type: str) -> None:
    """Set the current request type in context"""
    current_request_type.set(request_type)


def get_request_type() -> Optional[str]:
    """Get the current request type from context"""
    return current_request_type.get()


def clear_context() -> None:
    """Clear all context variables"""
    current_user_model.set(None)
    current_user_id.set(None)
    current_request_type.set(None)
