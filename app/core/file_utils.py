import os
import shutil
import tempfile
from pathlib import Path
from typing import Optional
from fastapi import UploadFile
import uuid
from datetime import datetime


class FileUploadManager:
    """Manages file uploads and temporary storage for document processing"""
    
    def __init__(self, temp_dir: Optional[str] = None):
        self.temp_dir = temp_dir or os.path.join(tempfile.gettempdir(), "document_summarizer")
        self._ensure_temp_dir()
    
    def _ensure_temp_dir(self):
        """Ensure the temporary directory exists"""
        Path(self.temp_dir).mkdir(parents=True, exist_ok=True)
    
    async def save_uploaded_file(self, file: UploadFile, subdirectory: Optional[str] = None) -> dict:
        """
        Save an uploaded file to temporary storage
        
        Args:
            file: The uploaded file
            subdirectory: Optional subdirectory within temp_dir
            
        Returns:
            dict: File information including path, size, etc.
        """
        # Create unique filename to avoid conflicts
        file_extension = Path(file.filename).suffix if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        
        # Create subdirectory if specified
        if subdirectory:
            file_dir = Path(self.temp_dir) / subdirectory
            file_dir.mkdir(parents=True, exist_ok=True)
        else:
            file_dir = Path(self.temp_dir)
        
        file_path = file_dir / unique_filename
        
        # Save the file
        try:
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Get file size
            file_size = file_path.stat().st_size
            
            return {
                "original_filename": file.filename,
                "saved_filename": unique_filename,
                "file_path": str(file_path),
                "file_size": file_size,
                "content_type": file.content_type,
                "uploaded_at": datetime.utcnow()
            }
            
        except Exception as e:
            # Clean up if save fails
            if file_path.exists():
                file_path.unlink()
            raise e
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from temporary storage
        
        Args:
            file_path: Path to the file to delete
            
        Returns:
            bool: True if file was deleted, False otherwise
        """
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                return True
            return False
        except Exception:
            return False
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old files from temporary storage
        
        Args:
            max_age_hours: Maximum age of files in hours before deletion
            
        Returns:
            int: Number of files deleted
        """
        deleted_count = 0
        cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)
        
        try:
            for file_path in Path(self.temp_dir).rglob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        deleted_count += 1
        except Exception:
            pass
        
        return deleted_count
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        Get information about a file
        
        Args:
            file_path: Path to the file
            
        Returns:
            dict: File information or None if file doesn't exist
        """
        try:
            path = Path(file_path)
            if path.exists():
                stat = path.stat()
                return {
                    "filename": path.name,
                    "file_path": str(path),
                    "file_size": stat.st_size,
                    "created_at": datetime.fromtimestamp(stat.st_ctime),
                    "modified_at": datetime.fromtimestamp(stat.st_mtime)
                }
        except Exception:
            pass
        
        return None


# Global instance
file_upload_manager = FileUploadManager() 