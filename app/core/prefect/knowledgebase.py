import os
import re
from uuid import UUID
import uuid
from slugify import slugify
from sqlalchemy.orm import Session
from fastapi import Request
from app.database.models.upload_config import UploadConfig
from .prefect_client import get_deployment_by_name, create_flow_run_by_deployment
from app.database.models.files import File
from services.pdf_parser.utils import sanitize_class_name
from typing import Optional
from app.core.config import settings


FLOW_NAME = "execute-data-pipeline"
DEPLOYMENT_NAME_FILE_UPLOAD = settings.PREFECT_DEPLOYMENT_NAME_FILE_UPLOAD
DEPLOYMENT_NAME_URL_PROCESSING = settings.PREFECT_DEPLOYMENT_NAME_URL_PROCESSING


def get_retriever_config(session: Session, client_id: Optional[UUID] = None):
    upload_config = (
        session.query(UploadConfig)
        .filter(UploadConfig.client_id == client_id)
        .one_or_none()
    )

    if not upload_config:
        upload_config = (
            session.query(UploadConfig).filter(UploadConfig.is_default == True).first()
        )

    return upload_config


def trigger_prefect_flow_run(file: File, request: Request, session: Session):

    upload_config = get_retriever_config(session=session)
    auth_header = request.headers.get("Authorization")
    base_url = str(request.base_url).replace("http://", "https://")
    parameters = {
        "chunking_config": upload_config.chunking_config,
        "embedding_config": upload_config.embedding_config,
        "retriever_config": upload_config.retriever_config,
        "sources": [
            {
                "type": "aws_object",
                "source_name": f"uploads/{file.folderid}/{file.name}",
                "kwargs": {"file_id": str(file.id)},
            }
        ],
        "webhook_config": {
            "callback_url": f"{base_url}api/v1/folder/file/update-status",
            "api_key": auth_header,
        },
    }
    print(parameters)
    parameters["retriever_config"]["kwargs"]["index_name"] = sanitize_class_name(
        str(file.folderid)
    )
    # parameters["object_name"] = f"uploads/{file.folderid}/{file.name}"

    flow_run_name = f"{slugify(file.name)}_{str(file.id)[:8]}"
    deployment_id = get_deployment_by_name(
        flow_name=FLOW_NAME, deployment_name=DEPLOYMENT_NAME_FILE_UPLOAD
    )["id"]

    flow_run_metadata = create_flow_run_by_deployment(
        deployment_id=deployment_id,
        name=flow_run_name,
        parameters=parameters,
    )


def trigger_prefect_flow_run_url(file: File, request: Request, session: Session):
    """
    Trigger a Prefect flow run for processing a URL.

    Args:
        file (File): File object that actually contains the url and relevant metadata
        request (Request): FastAPI request object
        session (Session): Database session
    """
    upload_config = get_retriever_config(session=session)
    auth_header = request.headers.get("Authorization")
    base_url = str(request.base_url).replace("http://", "https://")

    # Configure URL source
    source = {
        "type": "url",
        "source_name": file.url,
        "kwargs": {
            "crawler_type": file.meta_data.get("crawler_type"),
            "max_requests": file.meta_data.get("max_requests"),
            "crawl_links": file.meta_data.get("crawl_links"),
            "enqueue_strategy": file.meta_data.get("enqueue_strategy"),
            "file_id": str(file.id),  # Use file ID for tracking
        },
    }

    parameters = {
        "chunking_config": {
            "type": "RecursiveCharacterTextSplitter",
            "kwargs": {
                "chunk_size": 4000,
                "chunk_overlap": 200
            }
        },
        "embedding_config": upload_config.embedding_config,
        "retriever_config": upload_config.retriever_config,
        "sources": [source],
        "webhook_config": {
            "callback_url": f"{base_url}api/v1/folder/file/update-status",
            "api_key": auth_header,
        },
    }

    # Use sanitized folder ID as index name
    parameters["retriever_config"]["kwargs"]["index_name"] = sanitize_class_name(
        str(file.folderid)
    )

    # Create flow run with URL-based name
    flow_run_name = f"{slugify(file.name)}_{str(file.id)[:8]}"
    deployment_id = get_deployment_by_name(
        flow_name=FLOW_NAME, deployment_name=DEPLOYMENT_NAME_URL_PROCESSING
    )["id"]

    flow_run_metadata = create_flow_run_by_deployment(
        deployment_id=deployment_id,
        name=flow_run_name,
        parameters=parameters,
    )

    return str(file.id)  # Return the file ID for tracking
