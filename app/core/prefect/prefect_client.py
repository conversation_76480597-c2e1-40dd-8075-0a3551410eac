import os
import requests
from typing import Dict, Optional
from app.core.config import settings

DEFAULT_EMPIRICAL_POLICY = {"retries": None, "retry_delay": None, "pause_keys": [], "resuming": None}
DEFAULT_STATE = {
    "type": "SCHEDULED",
    "message": "",
    "state_details": {"scheduled_time": None, "cache_expiration": None},
}
DEFAULT_WORK_QUEUE_NAME = "chat-with-kb-queue"


def create_flow_run_by_deployment(
    deployment_id: str,
    name: str,
    parameters: dict,
    state: Optional[Dict] = DEFAULT_STATE,
    work_queue_name: Optional[Dict] = DEFAULT_WORK_QUEUE_NAME,
    empirical_policy: Optional[Dict] = DEFAULT_EMPIRICAL_POLICY,
):
    payload = {
        "name": name,
        "parameters": parameters,
        "state": state,
        "work_queue_name": work_queue_name,
        "empirical_policy": empirical_policy,
    }
    
    
    url = os.path.join(settings.PREFECT_API_URL, f"api/deployments/{deployment_id}/create_flow_run")
    print(url)
    print(settings.PREFECT_API_KEY)
    headers = {
        "Authorization": f"Bearer {settings.PREFECT_API_KEY}"
    }

    response = requests.post(url,headers=headers, json=payload)
    print(response)
    return response.json()


def get_deployment_by_name(flow_name: str, deployment_name: str):
    url = os.path.join(settings.PREFECT_API_URL, f"api/deployments/name/{flow_name}/{deployment_name}")
    print(url)
    print(settings.PREFECT_API_KEY)
    headers = {
        "Authorization": f"Bearer {settings.PREFECT_API_KEY}"
    }
    response = requests.get(url, headers=headers)
    print(response)
    return response.json()
