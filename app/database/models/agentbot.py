from sqlalchemy import Column, <PERSON>, <PERSON><PERSON>an, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from app.database.utils import Base  # Ensure this is your declarative base


class AgentBot(Base):
    __tablename__ = "agentbot"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    agent_name = Column(String(32), nullable=False, index=True)  # Max length enforced
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    folder_id = Column(
        UUID(as_uuid=True),
        ForeignKey("folder.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )
    agent_avatar = Column(String, nullable=True)
    avatar_color = Column(String, nullable=True)
    authorized_domain = Column(String, nullable=True)
    color_theme = Column(String, nullable=True)
    is_exposed = Column(Boolean, nullable=False, default=False)
    instructions = Column(String, nullable=True)
    is_deleted = Column(Boolean, nullable=False, default=False)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    folder = relationship("Folder", back_populates="agentbot")
    user = relationship("User", back_populates="agentbots")
    chat_sessions = relationship("ChatSession", back_populates="agentbot")

    # Unique Constraint (Agent Name + User ID + is_deleted must be unique)
    __table_args__ = (
        UniqueConstraint(
            "agent_name",
            "user_id",
            "is_deleted",
            name="uq_agentbot_agent_name_user_id_is_deleted",
        ),
    )
