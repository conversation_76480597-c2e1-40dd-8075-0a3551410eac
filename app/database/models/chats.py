from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Foreign<PERSON><PERSON>, Enum, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from app.database.utils import Base
import enum
from app.database.models.user import User
from app.database.models.agentbot import AgentBot
from app.database.models.folder import Folder


# class MessageType(enum.Enum):
#    SYSTEM = "system"
#    USER = "user"
#


class ChatSession(Base):
    __tablename__ = "chat_session"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    userid = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    agentid = Column(UUID(as_uuid=True), ForeignKey("agentbot.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    name = Column(String, nullable=True)

    user = relationship("User", back_populates="chat_sessions")
    agentbot = relationship("AgentBot", back_populates="chat_sessions")
    messages = relationship(
        "ChatMessage", back_populates="session", cascade="all, delete-orphan"
    )


class ChatMessage(Base):
    __tablename__ = "chat_message"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    session_id = Column(
        UUID(as_uuid=True), ForeignKey("chat_session.id"), nullable=False
    )
    type = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    metadata_text = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    plots = Column(JSON, nullable=True)

    plots = relationship(
        "ChatPlot", back_populates="message", cascade="all, delete-orphan"
    )
    session = relationship("ChatSession", back_populates="messages")


class ChatPlot(Base):
    __tablename__ = "chat_plot"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    message_id = Column(
        UUID(as_uuid=True), ForeignKey("chat_message.id"), nullable=False
    )
    url = Column(String, nullable=False)
    title = Column(String, nullable=False)

    message = relationship("ChatMessage", back_populates="plots")


User.chat_sessions = relationship(
    "ChatSession", back_populates="user", cascade="all, delete-orphan"
)
