from sqlalchemy import (
    Column,
    String,
    Boolean,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa
from sqlalchemy.orm import relationship
import uuid
from app.database.utils import Base  # Import Base from utils.py
from enum import Enum as PyEnum
from datetime import datetime


class SummaryStatus(PyEnum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class DocumentSummary(Base):
    __tablename__ = "document_summaries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    name = Column(String, index=True, nullable=False)
    meta_data = Column(JSON, nullable=True)
    is_deleted = Column(Boolean, default=False)
    status = Column(
        String(length=50), nullable=False, default=SummaryStatus.PENDING.value
    )
    prompt = Column(String, nullable=True)
    summary = Column(String, nullable=True)

    updated_at = Column(
        sa.DateTime(timezone=True),
        nullable=True,
        default=datetime.utcnow,
    )

    user_id = Column(UUID(as_uuid=True), sa.ForeignKey("users.id"), nullable=False)

    # Relationship with Folder
    files = relationship("DocumentSummaryFile", back_populates="document_summary")
    user = relationship("User", back_populates="document_summaries")
