from sqlalchemy import (
    Column,
    String,
    <PERSON><PERSON>an,
    DateT<PERSON>,
    Integer,
    ForeignKey,
    Enum,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa
from sqlalchemy.orm import relationship
import uuid
from app.database.utils import Base  # Import Base from utils.py
from enum import Enum as PyEnum
from datetime import datetime


class DocumentSummaryFileStatus(PyEnum):
    QUEUE = "Queue"
    FAILED = "Failed"
    PROCESSING = "Processing"
    SUCCESS = "Success"


class DocumentSummaryFile(Base):
    __tablename__ = "document_summary_files"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, index=True, nullable=False)
    type = Column(String, nullable=False)
    size = Column(Integer, nullable=True)
    path = Column(String, nullable=False)

    meta_data = Column(JSON, nullable=True)
    is_deleted = Column(<PERSON>ole<PERSON>, default=False)
    url = Column(String, nullable=True)
    status = Column(
        String(length=50), nullable=False, default=DocumentSummaryFileStatus.QUEUE.value
    )

    updated_at = Column(
        sa.DateTime(timezone=True),
        nullable=True,
        default=datetime.utcnow,
    )

    document_summary_id = Column(
        UUID(as_uuid=True), ForeignKey("document_summaries.id"), nullable=False
    )  # Foreign key to DocumentSummary

    # Relationship with DocumentSummary
    document_summary = relationship("DocumentSummary", back_populates="files")
