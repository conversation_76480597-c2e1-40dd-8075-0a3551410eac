from sqlalchemy import (
    <PERSON>um<PERSON>,
    <PERSON>,
    <PERSON>ole<PERSON>,
    Date<PERSON><PERSON>,
    Integer,
    ForeignKey,
    Enum,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa
from sqlalchemy.orm import relationship
import uuid
from app.database.utils import Base  # Import Base from utils.py
from enum import Enum as PyEnum
from datetime import datetime


class FileStatus(PyEnum):
    QUEUE = "Queue"
    FAILED = "Failed"
    PROCESSING = "Processing"
    SUCCESS = "Success"


class File(Base):
    __tablename__ = "files"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    name = Column(String, index=True, nullable=False)
    type = Column(String, nullable=False)
    size = Column(Integer, nullable=True)
    folderid = Column(
        UUID(as_uuid=True), ForeignKey("folder.id"), nullable=False
    )  # Foreign key to Folder
    meta_data = Column(JSON, nullable=True)
    is_deleted = Column(<PERSON>ole<PERSON>, default=False)
    url = Column(String, nullable=True)
    status = Column(String(length=50), nullable=False, default=FileStatus.QUEUE.value)
    message = Column(String, nullable=True)

    updated_at = Column(
        sa.DateTime(timezone=True),
        nullable=True,
        default=datetime.utcnow,
    )
    csv_data = Column(JSON, nullable=True)

    # Relationship with Folder
    folder = relationship("Folder", back_populates="files")
