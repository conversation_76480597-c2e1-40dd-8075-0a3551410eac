from sqlalchemy import Column, String, <PERSON><PERSON>an, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from app.database.utils import Base

class Folder(Base):
    __tablename__ = "folder"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    name = Column(String, index=True, nullable=False)
    userid = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)  # FK to User.id
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)

    # Relationship with User
    user = relationship("User", back_populates="folders")

    # Back-reference to files
    files = relationship("File", back_populates="folder")
    #Back-reference to folder
    agentbot = relationship("AgentBot", back_populates="folder")
