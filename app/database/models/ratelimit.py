from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()


class RateLimitRequest(Base):
    __tablename__ = "ratelimit_requests"

    id = Column(Integer, primary_key=True, index=True)
    auth_id = Column(String, index=True, nullable=False)
    ip_address = Column(String, index=True, nullable=False)
    agent_id = Column(UUID(as_uuid=True), index=True, nullable=False)
    request_time = Column(DateTime, nullable=False, default=datetime.utcnow)
