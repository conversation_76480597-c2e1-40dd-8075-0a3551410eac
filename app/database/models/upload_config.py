from uuid import UUID, uuid4
from datetime import datetime
from pydantic import BaseModel
from typing import Optional
from sqlalchemy import Column, String, Boolean, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID as SA_UUID  # Correct way to import UUID
from app.database.utils import Base

class UploadConfig(Base):
    __tablename__ = "upload_config"

    # Use the correct UUID type from SQLAlchemy
    id: UUID = Column(SA_UUID(as_uuid=True), default=uuid4, primary_key=True, unique=True)
    name: str = Column(String(64))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    client_id: UUID = Column(SA_UUID(as_uuid=True), index=True, nullable=True)
    is_default: bool = Column(Boolean, default=False)
    embedding_config: Optional[dict] = Column(JSON, nullable=True)
    chunking_config: Optional[dict] = Column(JSON, nullable=True)
    retriever_config: Optional[dict] = Column(JSON, nullable=True)
