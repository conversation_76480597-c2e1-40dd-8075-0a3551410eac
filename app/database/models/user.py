from sqlalchemy import Column, String, <PERSON><PERSON>an, DateTime, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from app.database.utils import Base


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    username = Column(String, unique=False, nullable=False)
    password = Column(String, nullable=False)
    profile_image = Column(String, nullable=True)
    email = Column(String, unique=True, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = Column(DateTime, nullable=True)
    is_deleted = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)

    document_summaries = relationship("DocumentSummary", back_populates="user")
    agentbots = relationship("AgentBot", back_populates="user")
    # Back-reference to folders
    folders = relationship("Folder", back_populates="user")
    application_limit = relationship(
        "ApplicationLimit",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan",
    )
