from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid
from app.database.utils import Base


class UserModel(Base):
    __tablename__ = "user_model"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    model_id = Column(String, nullable=False)
