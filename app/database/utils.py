import logging
import contextlib
from typing import Any, Iterator

from sqlalchemy import create_engine, Column, Integer, String, event
from sqlalchemy.orm import DeclarativeBase, Session, sessionmaker
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

DB_URL = settings.DATABASE_URL
logger.info(f"DATABASE URL: {DB_URL}")


def on_connect(dbapi_con, connection_record):
    logger.info("Database connection established")


def on_checkout(dbapi_con, connection_record, connection_proxy):
    logger.debug("Database connection checked out from pool")


def on_checkin(dbapi_con, connection_record):
    logger.debug("Database connection returned to pool")


class Base(DeclarativeBase):
    pass


class DatabaseSessionManager:
    def __init__(self, host: str, engine_kwargs: dict[str, Any] = {}):
        self._engine = create_engine(host, **engine_kwargs)
        self._sessionmaker = sessionmaker(autocommit=False, bind=self._engine)

        # Attach event listeners
        event.listen(self._engine, "connect", on_connect)
        event.listen(self._engine.pool, "checkout", on_checkout)
        event.listen(self._engine.pool, "checkin", on_checkin)

    def close(self):
        if self._engine is None:
            raise Exception("DatabaseSessionManager is not initialized")
        self._engine.dispose()
        self._engine = None
        self._sessionmaker = None

    @contextlib.contextmanager
    def session(self) -> Iterator[Session]:
        if self._sessionmaker is None:
            raise Exception("DatabaseSessionManager is not initialized")
        session = self._sessionmaker()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    @property
    def engine(self):
        return self._engine


sessionmanager = DatabaseSessionManager(
    DB_URL,
    dict(
        echo=True,
        pool_size=10,
        max_overflow=20,
        pool_recycle=15,
        pool_timeout=15,
    ),
)


def get_db() -> Iterator[Session]:
    with sessionmanager.session() as db:
        yield db
