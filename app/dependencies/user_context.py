from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import select
from app.database.utils import get_db
from app.database.models.user_model import UserModel
from app.middlewares.utils import get_user_from_request
from app.core.context import set_user_model, set_user_id, set_request_type, get_user_model
from typing import Optional
import logging

logger = logging.getLogger(__name__)


async def set_user_model_context(
    user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db),
    request_type: str = "general"
) -> str:
    """
    Dependency to set user model context and return the model ID
    Use this in endpoints where you need the user model context
    """
    try:
        user_id = user.get('id')
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not authenticated"
            )
        
        # Set user ID in context
        set_user_id(str(user_id))
        
        # Set request type
        set_request_type(request_type)
        
        # Get user model from database
        stmt = select(UserModel).where(UserModel.user_id == user_id)
        user_model = db.execute(stmt).scalar_one_or_none()
        
        if user_model:
            model_id = user_model.model_id
        else:
            # Use default model if no user model found
            model_id = "mistral.mistral-large-2402-v1:0"
            logger.warning(f"No user model found for user {user_id}, using default: {model_id}")
        
        # Set model in context
        set_user_model(model_id)
        
        logger.info(f"Set user model context: {model_id} for user: {user_id}")
        return model_id
        
    except Exception as e:
        logger.error(f"Error setting user model context: {e}")
        # Set default model as fallback
        default_model = "mistral.mistral-large-2402-v1:0"
        set_user_model(default_model)
        return default_model


async def set_process_context(
    user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db)
) -> str:
    """Dependency specifically for /process endpoints"""
    return await set_user_model_context(user, db, "process")


async def set_query_context(
    user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db)
) -> str:
    """Dependency specifically for /query endpoints"""
    return await set_user_model_context(user, db, "query")


def get_current_user_model() -> Optional[str]:
    """
    Get the current user model from context
    This can be used in any file/function
    """
    return get_user_model()
