from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from starlette.responses import JSONResponse
from services.auth.auth import decode_access_token

class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: AS<PERSON>App):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        open_routes = ["/api/v1/register", "/api/v1/login", "/docs"]  # Open routes that don't require auth
        if request.method in ["POST", "GET"] and request.url.path in open_routes:
            return await call_next(request)

        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                {"error": "Authorization header missing or invalid"}, 
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        token = auth_header.split("Bearer ")[1]
        try:
            user_data = decode_access_token(token)
            if not user_data:
                return JSONResponse(
                    {"error": "Invalid or expired token"}, 
                    status_code=status.HTTP_401_UNAUTHORIZED
                )
            request.state.user = user_data  # Attach user info to request state
        except Exception:
            return JSONResponse(
                {"error": "Unauthorized access"}, 
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        return await call_next(request)
