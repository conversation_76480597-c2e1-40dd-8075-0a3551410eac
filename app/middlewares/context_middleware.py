from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from app.core.context import set_user_model, set_user_id, set_request_type, clear_context
from app.database.utils import get_db
from app.database.models.user_model import UserModel
from app.middlewares.utils import get_user_from_request
from sqlalchemy.orm import Session
from sqlalchemy import select
import logging

logger = logging.getLogger(__name__)


class UserModelContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware to automatically set user model context for specific endpoints
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # Define endpoints that should have user model context
        self.target_endpoints = [
            "/api/v1/document_summariser",  # For /process endpoint
            "/api/v1/agent",  # For /query endpoint
        ]
    
    async def dispatch(self, request: Request, call_next):
        # Clear context at the start of each request
        clear_context()
        
        # Check if this is a target endpoint
        should_set_context = any(
            request.url.path.startswith(endpoint) 
            for endpoint in self.target_endpoints
        )
        
        if should_set_context:
            try:
                await self._set_user_context(request)
            except Exception as e:
                logger.warning(f"Failed to set user context: {e}")
        
        response = await call_next(request)
        
        # Clear context after request
        clear_context()
        
        return response
    
    async def _set_user_context(self, request: Request):
        """Set user model context from request"""
        try:
            # Get user from request (you might need to adapt this based on your auth)
            # This is a simplified version - you'll need to adapt based on your auth middleware
            
            # For now, let's try to get user from headers or cookies
            # You might need to modify this based on your authentication system
            
            # Option 1: If you have user info in request state (set by auth middleware)
            if hasattr(request.state, 'user'):
                user = request.state.user
                user_id = user.get('id') if isinstance(user, dict) else getattr(user, 'id', None)
            else:
                # Option 2: Try to get from authorization header
                auth_header = request.headers.get('authorization')
                if auth_header:
                    # You'll need to implement token validation here
                    # This is just a placeholder
                    user_id = await self._get_user_from_token(auth_header)
                else:
                    return
            
            if user_id:
                # Set user ID in context
                set_user_id(str(user_id))
                
                # Get user model from database
                db_gen = get_db()
                db = next(db_gen)
                try:
                    stmt = select(UserModel).where(UserModel.user_id == user_id)
                    user_model = db.execute(stmt).scalar_one_or_none()
                    
                    if user_model:
                        set_user_model(user_model.model_id)
                        logger.info(f"Set user model context: {user_model.model_id} for user: {user_id}")
                    else:
                        # Set default model if no user model found
                        set_user_model("mistral.mistral-large-2402-v1:0")
                        logger.info(f"Set default model context for user: {user_id}")
                    
                    # Set request type based on endpoint
                    if "/process" in request.url.path:
                        set_request_type("process")
                    elif "/query" in request.url.path:
                        set_request_type("query")
                        
                finally:
                    db.close()
                    
        except Exception as e:
            logger.error(f"Error setting user context: {e}")
    
    async def _get_user_from_token(self, auth_header: str) -> str:
        """
        Extract user ID from authorization token
        You'll need to implement this based on your auth system
        """
        # This is a placeholder - implement based on your JWT/token validation
        # For now, return None
        return None
