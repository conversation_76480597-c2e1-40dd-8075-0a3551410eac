from fastapi import Request, HTTPException, Depends, status
from sqlalchemy.orm import Session
from app.database.utils import get_db
from app.database.models.user import User
from app.database.models.application_limits import ApplicationLimit
from services.auth.auth import decode_access_token
from sqlalchemy.orm import joinedload



# middleware to authenticate request
async def authenticate_request(request: Request, db: Session = Depends(get_db)):
    """
    Middleware to authenticate requests using JWT from Authorization header.

    Args:
        request (Request): The FastAPI Request object.
        db (Session): The database session dependency.

    Returns:
        dict: User details from the database if authentication is successful.

    Raises:
        HTTPException: If authentication fails due to missing/invalid token or user not found.
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Authorization header missing or invalid",
        )

    token = auth_header.split("Bearer ")[1]
    try:
        # Decode the token
        user_data = decode_access_token(token)
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid token",
            )

        
        email = user_data["sub"]
        if not email:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid token payload",
            )

        # Fetch the user from the database
        user = (
            db.query(User)
            .options(joinedload(User.application_limit))  # Load ApplicationLimit relationship
            .filter(User.email == email)
            .first()
        )

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )
        return {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "profile_image": user.profile_image,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "last_login_at": user.last_login_at,
            "is_paid": user.application_limit.is_paid,
            "max_folder": user.application_limit.max_folder,
            "max_file": user.application_limit.max_file,
            "tokens_left": user.application_limit.tokens_left,
            "max_tokens": user.application_limit.max_tokens,
            "is_verified":user.is_verified
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Unauthorized access",
        )
