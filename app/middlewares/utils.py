from fastapi import Depends, HTTPException, Request, Body, Path
from fastapi.security import OA<PERSON>2Pass<PERSON><PERSON>earer
from app.middlewares.middleware import authenticate_request
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.orm import Session
from app.database.utils import get_db
from app.database.models.ratelimit import RateLimitRequest
from schemas.agentbot import QueryRequest
from app.core.config import settings


# Dependency to get current active user from authenticated request
async def get_user_from_request(
    request: Request, user_data: dict = Depends(authenticate_request)
):
    """
    Function to get the current active user from the authenticated request.

    Args:
        request (Request): The FastAPI Request object.
        user_data (dict): The decoded user data obtained from JWT authentication.

    Returns:
        dict: The user data if user is active.

    Raises:
        HTTPException: If the user is inactive.
    """

    return user_data


# Max requests per window minute
MAX_REQUESTS = settings.MAX_REQUESTS
WINDOW_MINUTES = 1


async def rate_limit_dependency(
    agent_id: str = Path(...),
    data: QueryRequest = Body(...),
    request: Request = None,
    db: Session = Depends(get_db),
) -> QueryRequest:
    ip_address = request.client.host if request else "UNKNOWN"
    now = datetime.utcnow()
    cutoff_time = now - timedelta(minutes=WINDOW_MINUTES)

    # Validate folder_id as UUID
    try:
        agent_id = UUID(agent_id)
    except ValueError:
        raise HTTPException(status_code=422, detail="Invalid folder ID")

    # Remove requests older than cutoff
    db.query(RateLimitRequest).filter(
        RateLimitRequest.auth_id == data.auth_id,
        RateLimitRequest.ip_address == ip_address,
        RateLimitRequest.agent_id == agent_id,
        RateLimitRequest.request_time < cutoff_time,
    ).delete()
    db.commit()

    # Count how many requests in the last WINDOW_MINUTES
    # (sliding window approach for example)
    recent_requests_count = (
        db.query(RateLimitRequest)
        .filter(RateLimitRequest.auth_id == data.auth_id)
        .filter(RateLimitRequest.ip_address == ip_address)
        .filter(RateLimitRequest.agent_id == agent_id)
        .filter(RateLimitRequest.request_time >= cutoff_time)
        .count()
    )
    if recent_requests_count >= MAX_REQUESTS:
        raise HTTPException(status_code=429, detail="Rate limit exceeded")

    new_row = RateLimitRequest(
        auth_id=data.auth_id,
        ip_address=ip_address,
        agent_id=agent_id,
        request_time=now,
    )
    db.add(new_row)
    db.commit()

    return data
