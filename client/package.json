{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --config vite.config.app.ts", "build": "npm run build:app && npm run build:widget", "build:app": "vite build --config vite.config.app.ts", "build:widget": "vite build --config vite.config.widget.ts", "lint": "eslint .", "preview": "vite preview --config vite.config.app.ts", "prettier": "prettier . --write"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.16", "@thumbmarkjs/thumbmarkjs": "^0.16.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dayjs": "^1.11.13", "emoji-mart": "^5.6.0", "lucide-react": "^0.468.0", "motion": "^11.15.0", "next-themes": "^0.4.4", "react": "^18.3.1", "react-cookie": "^7.2.2", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-markdown": "^9.0.3", "react-pdf": "^9.2.1", "react-router": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "terser": "^5.37.0", "use-debounce": "^10.0.4", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "vite-plugin-svgr": "^4.3.0"}}