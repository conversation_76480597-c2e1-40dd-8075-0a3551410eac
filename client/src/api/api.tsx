import axios, { AxiosInstance } from "axios";
import { useEffect } from "react";
import { useCookies } from "react-cookie";
import { useNavigate } from "react-router";
import { useAuthStore } from "../store/authStore";

const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_SERVER_API_URL,
});

const publicRoutes = ["/login", "/signup"];

function isPublicRoute(url: string): boolean {
  return publicRoutes.some((route) => {
    // Allow exact matches for public routes
    const routeRegex = new RegExp(`^${route}$`);
    return routeRegex.test(url);
  });
}

function ApiInterceptor() {
  const [cookies] = useCookies(["access_token"]);
  const navigate = useNavigate();
  const getToken = useAuthStore((state) => state.getToken);

  useEffect(() => {
    // Request interceptor to add access token for protected routes
    const requestInterceptor = api.interceptors.request.use(
      (config) => {
        // Get token using the auth store method (checks localStorage and cookies)
        const accessToken = getToken();

        // Check if the route is public
        if (config.url && !isPublicRoute(config.url)) {
          // Add the access token to the Authorization header for protected routes
          if (accessToken) {
            if (config.headers) {
              config.headers.set("Authorization", `Bearer ${accessToken}`);
            } else {
              navigate("/login");
            }
          }
        }

        return config;
      },
      (error) => Promise.reject(error),
    );

    return () => {
      api.interceptors.request.eject(requestInterceptor);
    };
  }, [cookies, navigate, getToken]); // monitor all cookies and token changes

  return null;
}

export { ApiInterceptor, api };
