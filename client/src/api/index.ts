import axios from "axios";
import { Folder } from "../components/KnowledgeBaseExplorerComponent";
import { api } from "./api";

const BASE_URL = import.meta.env.VITE_SERVER_API_URL;

export type ChatMessage = {
  type: string;
  content: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata?: any;
  created_at?: string;
};

export const loginUser = async (email: string, password: string) => {
  return await axios.post(`${BASE_URL}/api/v1/login`, {
    email,
    password,
  });
};

export const googleLogin = async (credential: string) => {
  return await axios.post(`${BASE_URL}/api/v1/google/login`, {
    credential: credential,
  });
};
export const forgotPassword = async (email: string) => {
  return await axios.post(`${BASE_URL}/api/v1/forgot-password`, {
    email,
  });
};

export const resetPassword = async (token: string, new_password: string) => {
  return await axios.post(`${BASE_URL}/api/v1/reset-password`, {
    token,
    new_password,
  });
};

export const updatePassword = async (password: string) => {
  return await api.patch(`${BASE_URL}/api/v1/user`, {
    password,
  });
};

export const sendVerificationEmail = async () => {
  return await api.post(`${BASE_URL}/api/v1/send-verification`);
};

export const verifyUserEmail = async (token: string) => {
  return await api.get(`${BASE_URL}/api/v1/verify-email`, {
    params: { token },
  });
};

export const registerUser = async (
  username: string,
  email: string,
  password: string,
) => {
  return await axios.post(`${BASE_URL}/api/v1/register`, {
    username,
    email,
    password,
  });
};

export const deleteUser = async (password: string) => {
  return api.delete(`${BASE_URL}/api/v1/user`, {
    data: { password },
  });
};

export const getFolders = async (queryParams = {}) => {
  const response = await api.get(`${BASE_URL}/api/v1/folder`, {
    params: queryParams,
  });
  return response.data;
};

export const getUser = async () => {
  const response = await api.get(`${BASE_URL}/api/v1/user/me`);
  return response.data;
};

export const createFolder = async (
  newKnowledgeBase: string,
  description: string,
) => {
  const response = await api.post(`${BASE_URL}/api/v1/folder`, {
    name: newKnowledgeBase,
    description,
    headers: { "Content-Type": "application/json" },
  });
  return response.data;
};

export const updateFolder = async (
  folderId: string,
  folderData: Partial<Folder>,
) => {
  await api.patch(`${BASE_URL}/api/v1/folder/${folderId}`, folderData, {
    headers: { "Content-Type": "application/json" },
  });
};

export const deleteFolder = async (folderId: string) => {
  const response = await api.delete(`${BASE_URL}/api/v1/folder/${folderId}`);
  return response.data;
};

export const createFile = async (
  folderId: string,
  name: string,
  uploadedFile: File,
) => {
  const formData = new FormData();

  // Append form data fields
  formData.append("name", name);
  formData.append("uploaded_file", uploadedFile);

  // Make the API request
  const response = await api.post(
    `${BASE_URL}/api/v1/folder/${folderId}/file`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data", // Indicate the form-data content type
      },
    },
  );

  return response.data;
};

export const getNotDeletedFiles = async (
  folderId: string,
  queryParams = {},
) => {
  const response = await api.get(`${BASE_URL}/api/v1/folder/file/${folderId}`, {
    params: queryParams,
  });
  return response;
};

export const getPresignedURLByFileName = async (
  folderId: string,
  fileName: string,
) => {
  const response = await api.get(
    `${BASE_URL}/api/v1/folder/get-url-by-name/${folderId}/${fileName}`,
  );
  return response;
};

export const deleteFile = async (folderId: string, fileId: string) => {
  const response = await api.delete(
    `${BASE_URL}/api/v1/folder/${folderId}/file/${fileId}`,
  );
  return response.data;
};

export const getStatus = async (folderId: string, fileId: string) => {
  const response = await api.get(
    `${BASE_URL}/api/v1/folder/${folderId}/file/${fileId}`,
  );
  return response.data;
};

export const generatePresignedUrl = async () => {
  const response = await api.post(
    `${BASE_URL}/api/v1/folder/generatePresignedURL`,
  );
  return response.data;
};

export const uploadFileToS3 = async (presignedUrl: string, file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await api.post(presignedUrl, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const getPresignedURL = async (folderId: string, fileId: string) => {
  const response = await api.get(
    `${BASE_URL}/api/v1/folder/get-url/${folderId}/${fileId}`,
  );
  return response;
};

export const getAllChatsForUser = async (agentId: string, queryParams = {}) => {
  const response = await api.get(
    `${BASE_URL}/api/v1/agent/${agentId}/session`,
    {
      params: queryParams,
    },
  );
  return response.data;
};

export const getPreviousMessages = async (
  agentId: string,
  sessionId: string,
) => {
  const response = await api.get(
    `${BASE_URL}/api/v1/agent/${agentId}/session/${sessionId}`,
  );
  return response.data;
};

export const createNewChatSession = async (agentId: string) => {
  const response = await api.post(
    `${BASE_URL}/api/v1/agent/${agentId}/session`,
    {
      agent_id: agentId,
      query: "create new chat session",
    },
  );
  return response.data;
};

export const queryChat = async (
  query: string,
  agentId: string,
  sessionId?: string,
) => {
  const response = await api.post(
    `${BASE_URL}/api/v1/agent/${agentId}/session/${sessionId}/query`,
    {
      query,
      session_id: sessionId,
    },
  );
  return response.data;
};

export const queryDataAnalysisChat = async (
  query: string,
  agentId: string,
  sessionId: string,
) => {
  const response = await api.post(
    `/api/v1/agent/${agentId}/session/${sessionId}/da/query`,
    { query },
  );
  return response.data;
};

export const deleteChat = async (sessionId: string): Promise<void> => {
  await api.delete(`${BASE_URL}/api/v1/agent/${sessionId}`);
};

// ChatBot Embed

export const getChatBotConfig = async (agentId: string) => {
  const response = api.get(`${BASE_URL}/api/v1/agent/${agentId}`);
  return response;
};

export const createChatBot = async (folderId: string, formData: FormData) => {
  const response = api.post(`${BASE_URL}/api/v1/agent/${folderId}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response;
};

export const getPublicChatBotConfig = async (agentId: string) => {
  const response = axios.get(`${BASE_URL}/api/v1/public/agent/${agentId}`);
  return response;
};

export const updateChatBot = async (agentId: string, formData: FormData) => {
  const response = api.patch(`${BASE_URL}/api/v1/agent/${agentId}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response;
};

export const queryPublicChatBot = async (
  agentId: string,
  query: string,
  authId: string,
) => {
  const response = axios.post(
    `${BASE_URL}/api/v1/public/agent/${agentId}/query`,
    {
      query,
      auth_id: authId,
    },
  );
  return response;
};

export const createAgent = async (
  folderId: string,
  agentName: string,
  agent_avatar: string,
  avatar_color: string,
  colorTheme?: string,
  instructions?: string,
) => {
  const formData = new FormData();
  formData.append("agent_name", agentName);
  formData.append("agent_avatar", agent_avatar);
  formData.append("folder_id", folderId);
  formData.append("avatar_color", avatar_color);
  if (colorTheme) formData.append("color_theme", colorTheme);
  if (instructions) formData.append("instructions", instructions);

  const response = await api.post(`${BASE_URL}/api/v1/agent/`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response;
};

export const getAllAgents = async (queryParams = {}) => {
  const response = await api.get(`${BASE_URL}/api/v1/agent/all`, {
    params: queryParams,
  });
  return response.data;
};

export const createUrl = async (
  folderId: string,
  urlData: {
    url: string;
    meta_data: {
      crawler_type: string;
      max_requests: number;
      crawl_links: boolean;
      enqueue_strategy: string;
    };
  },
) => {
  const response = await api.post(
    `${BASE_URL}/api/v1/folder/${folderId}/url`,
    urlData,
    {
      headers: {
        "Content-Type": "application/json",
      },
    },
  );
  return response;
};

// Document Summary Types
export interface HyperlinkData {
  hyperlink_id: string;
  source_ref: {
    file_id: string;
    file_name: string;
    chunk_id: string;
    page_number?: number;
    section_title?: string;
    start_char?: number;
    end_char?: number;
    content_preview?: string;
  };
  anchor_text: string;
  context: string;
}

export interface DocumentSummary {
  id: string;
  name: string;
  status: string;
  prompt?: string;
  summary?: string;
  updated_at?: string;
  is_deleted: boolean;
  meta_data?: any;
  hyperlinks?: Record<string, HyperlinkData>;
}

export interface DocumentSummaryFile {
  id: string;
  name: string;
  type: string;
  size?: number;
  path: string;
  status: string;
  url?: string;
  meta_data?: any;
  updated_at?: string;
  is_deleted: boolean;
}

export interface DocumentSummaryListResponse {
  results: DocumentSummary[];
  count: number;
  prev?: string;
  next?: string;
}

// Document Summary API Functions
export const getSummaries = async (queryParams = {}) => {
  const response = await api.get(`${BASE_URL}/api/v1/document_summariser`, {
    params: queryParams,
  });
  return response.data;
};

export const getSummaryById = async (summaryId: string) => {
  const response = await api.get(`${BASE_URL}/api/v1/document_summariser/${summaryId}`);
  return response.data;
};

export const createSummary = async (data: {
  name: string;
  prompt?: string;
  meta_data?: any;
}) => {
  const response = await api.post(`${BASE_URL}/api/v1/document_summariser`, data, {
    headers: { "Content-Type": "application/json" },
  });
  return response.data;
};

export const updateSummary = async (
  id: string,
  data: Partial<DocumentSummary>,
) => {
  const response = await api.patch(`${BASE_URL}/api/v1/document_summariser/${id}`, data, {
    headers: { "Content-Type": "application/json" },
  });
  return response.data;
};

export const deleteSummary = async (id: string) => {
  const response = await api.delete(`${BASE_URL}/api/v1/document_summariser/${id}`);
  return response.data;
};

// File Management
export const uploadFile = async (summaryId: string, file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await api.post(
    `${BASE_URL}/api/v1/document_summariser/${summaryId}/files/upload`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

export const getSummaryFiles = async (summaryId: string) => {
  const response = await api.get(`${BASE_URL}/api/v1/document_summariser/${summaryId}/files`);
  return response.data;
};

export const getSummaryFile = async (summaryId: string, fileId: string) => {
  const response = await api.get(`${BASE_URL}/api/v1/document_summariser/${summaryId}/files/${fileId}`);
  return response.data;
};

export const deleteSummaryFile = async (summaryId: string, fileId: string) => {
  const response = await api.delete(`${BASE_URL}/api/v1/document_summariser/${summaryId}/files/${fileId}`);
  return response.data;
};

// Processing
export const processSummary = async (summaryId: string, customPrompt?: string, importantKeywords?: string) => {
  const formData = new FormData();
  if (customPrompt) {
    formData.append("custom_prompt", customPrompt);
  }
  if (importantKeywords) {
    formData.append("important_keywords", importantKeywords);
  }

  const response = await api.post(`${BASE_URL}/api/v1/document_summariser/${summaryId}/process`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

// User Model API Functions
export const getUserModel = async (userId: string) => {
  const response = await api.get(`${BASE_URL}/user_model/${userId}`);
  return response.data;
};

export const updateUserModel = async (userId: string, modelId: string) => {
  const response = await api.put(`${BASE_URL}/user_model/${userId}`, {
    model_id: modelId,
  });
  return response.data;
};
