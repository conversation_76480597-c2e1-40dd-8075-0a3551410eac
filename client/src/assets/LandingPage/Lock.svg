<svg width="232" height="232" viewBox="0 0 232 232" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_114_207)">
<rect x="64.6639" y="64.6088" width="102.672" height="102.672" rx="32" fill="url(#paint0_linear_114_207)"/>
<rect x="65.1639" y="65.1088" width="101.672" height="101.672" rx="31.5" stroke="#9292AF"/>
<path d="M116 125.102C116.973 125.102 117.905 124.715 118.593 124.028C119.28 123.34 119.667 122.407 119.667 121.435C119.667 120.462 119.28 119.53 118.593 118.842C117.905 118.154 116.973 117.768 116 117.768C115.028 117.768 114.095 118.154 113.407 118.842C112.72 119.53 112.333 120.462 112.333 121.435C112.333 122.407 112.72 123.34 113.407 124.028C114.095 124.715 115.028 125.102 116 125.102ZM127 108.602C127.973 108.602 128.905 108.988 129.593 109.675C130.28 110.363 130.667 111.296 130.667 112.268V130.602C130.667 131.574 130.28 132.507 129.593 133.194C128.905 133.882 127.973 134.268 127 134.268H105C104.028 134.268 103.095 133.882 102.407 133.194C101.72 132.507 101.333 131.574 101.333 130.602V112.268C101.333 111.296 101.72 110.363 102.407 109.675C103.095 108.988 104.028 108.602 105 108.602H106.833V104.935C106.833 102.504 107.799 100.172 109.518 98.453C111.237 96.734 113.569 95.7682 116 95.7682C117.204 95.7682 118.396 96.0053 119.508 96.466C120.62 96.9266 121.631 97.6018 122.482 98.453C123.333 99.3042 124.008 100.315 124.469 101.427C124.93 102.539 125.167 103.731 125.167 104.935V108.602H127ZM116 99.4349C114.541 99.4349 113.142 100.014 112.111 101.046C111.08 102.077 110.5 103.476 110.5 104.935V108.602H121.5V104.935C121.5 103.476 120.921 102.077 119.889 101.046C118.858 100.014 117.459 99.4349 116 99.4349Z" fill="white"/>
</g>
<rect opacity="0.1" x="0.993896" y="0.938721" width="230.012" height="230.012" rx="111.5" stroke="#EA4335"/>
<rect opacity="0.3" x="17.0364" y="16.9812" width="197.927" height="197.927" rx="91.5" stroke="#EA4335"/>
<rect opacity="0.6" x="33.0789" y="33.0237" width="165.842" height="165.842" rx="71.5" stroke="#EA4335"/>
<rect x="49.1215" y="49.0663" width="133.757" height="133.757" rx="51.5" stroke="#EA4335"/>
<defs>
<filter id="filter0_dii_114_207" x="45.6639" y="63.6088" width="140.672" height="147.672" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect1_dropShadow_114_207"/>
<feOffset dy="25"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_114_207"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_114_207" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect2_innerShadow_114_207"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8 0 0 0 0 0.780392 0 0 0 0 0.780392 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_114_207"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect3_innerShadow_114_207"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8 0 0 0 0 0.780392 0 0 0 0 0.780392 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_114_207" result="effect3_innerShadow_114_207"/>
</filter>
<linearGradient id="paint0_linear_114_207" x1="64.6639" y1="64.6088" x2="146.682" y2="181.743" gradientUnits="userSpaceOnUse">
<stop stop-color="#F25959"/>
<stop offset="1" stop-color="#EC1F22"/>
</linearGradient>
</defs>
</svg>
