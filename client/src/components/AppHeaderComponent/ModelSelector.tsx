import { useState, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { useUserStore } from "../../store/userStore";
import { getUserModel, updateUserModel } from "../../api";
import { useToast } from "../../hooks/use-toast";

const MODEL_OPTIONS = [
  {
    id: "mistral.mistral-large-2402-v1:0",
    name: "Mistral Large",
    description: "mistral.mistral-large-2402-v1:0"
  },
  {
    id: "meta.llama3-70b-instruct-v1:0",
    name: "Llama 3 70B",
    description: "meta.llama3-70b-instruct-v1:0"
  }
];

const ModelSelector = () => {
  const [selectedModel, setSelectedModel] = useState<string>("mistral.mistral-large-2402-v1:0");
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useUserStore();
  const { toast } = useToast();

  // Fetch current user model on component mount
  useEffect(() => {
    const fetchUserModel = async () => {
      if (!user?.id) return;
      
      try {
        const userModel = await getUserModel(user.id);
        setSelectedModel(userModel.model_id);
      } catch (error) {
        // If no model found, use default
        console.log("No user model found, using default");
      }
    };

    fetchUserModel();
  }, [user?.id]);

  const handleModelChange = async (modelId: string) => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      await updateUserModel(user.id, modelId);
      setSelectedModel(modelId);
      setIsOpen(false);
      
      const selectedModelName = MODEL_OPTIONS.find(m => m.id === modelId)?.name;
      toast({
        title: "Model Updated",
        description: `Successfully switched to ${selectedModelName}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update model. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const selectedModelInfo = MODEL_OPTIONS.find(m => m.id === selectedModel);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50"
      >
        <span className="truncate max-w-32">
          {selectedModelInfo?.name || "Select Model"}
        </span>
        <ChevronDown 
          className={`h-4 w-4 transition-transform ${isOpen ? "rotate-180" : ""}`} 
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 top-full z-50 mt-1 w-64 rounded-lg border border-gray-200 bg-white shadow-lg">
          <div className="p-1">
            {MODEL_OPTIONS.map((model) => (
              <button
                key={model.id}
                onClick={() => handleModelChange(model.id)}
                disabled={isLoading}
                className={`w-full rounded-md px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none disabled:opacity-50 ${
                  selectedModel === model.id ? "bg-blue-50 text-blue-700" : "text-gray-700"
                }`}
              >
                <div className="font-medium">{model.name}</div>
                <div className="text-xs text-gray-500 truncate">{model.description}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ModelSelector;
