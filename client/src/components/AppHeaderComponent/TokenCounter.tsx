import { useEffect } from "react";
import { useUserStore } from "../../store/userStore";
import { getUser } from "../../api";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "../ui/tooltip";

const TokenCounter = () => {
  const getTokens = async () => {
    const userData = await getUser();
    const tokens = userData.tokens_left;
    useUserStore.getState().setTokenLeft(tokens);
  };

  useEffect(() => {
    getTokens();
  }, []);

  const tokenLeft = useUserStore((state) => state.tokenleft);
  const MAX_TOKENS = useUserStore((state) => state.user).max_tokens;

  return null;
};

export default TokenCounter;
