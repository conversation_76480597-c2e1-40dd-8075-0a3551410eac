import { Link } from "react-router";
import <PERSON><PERSON><PERSON><PERSON> from "../../assets/bling_logo.svg";
import AccountChip from "../SidebarComponent/AccountChip";
import TokenCounter from "./TokenCounter";
import ModelSelector from "./ModelSelector";

const AppHeader = () => {
  return (
    <header className="sticky top-0 z-50 flex w-full items-center justify-between border-2 bg-white px-4 py-2">
      <Link to="/" className="flex w-fit gap-2">
        <img src={BuddhiLogo} alt="Psybe Labs powered by AI Planet" className="h-6 w-6" />
        <span className="text-xl font-bold">
          Psybe Labs <span className="text-blue-600">powered by AI Planet</span>
        </span>
      </Link>
      <div className="flex items-center gap-4 pr-2">
        <TokenCounter />
        <div className="h-10 w-[1px] bg-gray-300" />
        <ModelSelector />
        <div className="h-10 w-[1px] bg-gray-300" />
        <AccountChip />
      </div>
    </header>
  );
};

export default AppHeader;
