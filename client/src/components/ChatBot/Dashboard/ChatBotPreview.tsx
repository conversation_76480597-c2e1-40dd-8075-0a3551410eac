import { MoveUp, X } from "lucide-react";
import { motion } from "motion/react";
import AppIcon from "../../EmojiPicker/AppIcon";

export default function ChatBotPreview({ chatBotConfigurations, onClose }) {
  return (
    <section className="flex h-full w-full items-center justify-center">
      <div className="mx-auto max-w-sm overflow-hidden rounded-2xl bg-white shadow-lg">
        {/* Chat Header */}
        <div className="flex items-center gap-3 border-b p-4">
          <div
            className="flex items-center justify-center rounded-full w-10 h-10"
            style={{ backgroundColor: chatBotConfigurations.avatar.bg }}
          >
            <AppIcon
              icon={chatBotConfigurations.avatar.icon}
              className="text-xl"
            />
          </div>
          <span className="font-medium">{chatBotConfigurations.botName}</span>
          <button
            className="ml-auto transition-transform hover:-rotate-45"
            onClick={onClose}
          >
            <X size={18} />
          </button>
        </div>

        {/* Chat Messages */}
        <div className="h-[350px] overflow-y-auto bg-gray-50 p-4">
          <div className="flex flex-col gap-5">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="w-[80%] self-end rounded-lg p-3 text-white shadow-sm"
              style={{ backgroundColor: chatBotConfigurations.selectedColor }}
            >
              <p className="text-sm">
                Help me customize a one-week fitness and diet weight loss plan.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="w-[80%] rounded-lg bg-white p-3 shadow-sm"
            >
              <p className="text-sm">
                Some of the popular tourist destinations in the United States
                are as follows:
              </p>
              <p className="mt-2 text-sm">
                New York City is a must-visit, with its renowned Times Square,
                the Statue of Liberty standing tall in the harbor, and the vast
                and beautiful Central Park.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Chat Input */}
        <div className="border-t bg-white p-4">
          <div className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-2">
            <input
              disabled
              type="text"
              placeholder="Send message..."
              className="flex-1 border-none bg-transparent text-sm focus:outline-none"
            />
            <button
              className="flex h-6 w-6 items-center justify-center rounded-full"
              style={{ backgroundColor: chatBotConfigurations.selectedColor }}
            >
              <MoveUp size={14} className="text-white" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
