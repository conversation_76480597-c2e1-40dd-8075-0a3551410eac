import { useState } from "react";
import { AnimatePresence, motion } from "motion/react";

import ChatBotPreview from "./ChatBotPreview";
import { cn } from "../../../lib/utils";

export default function ChatBotPreviewPanel({ chatBotConfigurations }) {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div
      className={cn(
        "relative flex flex-1 items-center justify-center bg-[#F5F5F7] py-5",
      )}
    >
      {!isOpen && (
        <>
          <motion.button
            className="absolute bottom-4 right-6 flex h-14 w-14 cursor-pointer items-center justify-center rounded-full text-2xl text-white shadow-lg"
            onClick={() => setIsOpen(true)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
            style={{ backgroundColor: chatBotConfigurations.selectedColor }}
          >
            💬
          </motion.button>
          <h1 className="text-black/50 font-medium text-2xl">Your Website</h1>
        </>
      )}
      <AnimatePresence mode="popLayout">
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <ChatBotPreview
              chatBotConfigurations={chatBotConfigurations}
              onClose={() => setIsOpen(false)}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
