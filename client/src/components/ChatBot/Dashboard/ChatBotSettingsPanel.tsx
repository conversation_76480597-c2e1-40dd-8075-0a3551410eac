import { useState } from "react";
import { motion } from "motion/react";

import {
  cn,
  defaultChatBotColors,
  isChatBotFormDisabled,
  isFormDisabled,
} from "../../../lib/utils";
import { Button } from "../../ui/button";
import { Loader2 } from "lucide-react";
import EmojiPicker from "../../EmojiPicker";
import AppIcon from "../../EmojiPicker/AppIcon";
import { Switch } from "../../ui/switch";
import InfoTooltip from "../../shared/InfoTooltip";

export default function ChatBotSettingsPanel({
  chatBotConfigurations,
  handleTextChange,
  handleColorSelect,
  handleAvatarUpload,
  handleExposedToggle,
  onSubmit,
  loading,
}) {
  const [showAvatarPicker, setShowAvatarPicker] = useState(false);
  console.log({ chatBotConfigurations });
  return (
    <>
      <form
        className="flex w-full flex-col gap-5 px-7 py-6 text-[#0B0B0B] lg:w-[40%]"
        onSubmit={onSubmit}
      >
        {/* Bot Name Input */}
        <div className="flex flex-col gap-3.5 border-b border-b-[#00000014] pb-5">
          <label className="font-semibold">Chat Bot Name</label>
          <input
            type="text"
            value={chatBotConfigurations.botName}
            onChange={handleTextChange}
            className="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500"
            placeholder="Enter bot name"
            name="botName"
            required
          />
        </div>

        {/* Domain Input */}
        <div className="flex flex-col gap-3.5 border-b border-b-[#00000014] pb-5">
          <label className="flex items-center gap-2 font-semibold">
            <span>Website Domain</span>
            <InfoTooltip className="ml-10 w-3/4">
              <p>
                If the site where you want to embed the bot is
                https://www.demo.com/ then enter https://www.demo.com without a trailing slash (/)
              </p>
            </InfoTooltip>
          </label>
          <input
            type="text"
            value={chatBotConfigurations.domain}
            onChange={handleTextChange}
            className="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500"
            placeholder="Enter domain (e.g., example.com)"
            name="domain"
          />
        </div>

        {/* Avatar Section */}
        <div className="flex flex-col gap-3.5 border-b border-b-[#00000014] pb-5">
          <label className="font-semibold">Avatar</label>
          <div className="flex items-center gap-9">
            <div className="relative h-16 w-16">
              <div
                className="flex h-full w-full items-center justify-center rounded-full"
                style={{ backgroundColor: chatBotConfigurations.avatar.bg }}
              >
                <AppIcon
                  icon={chatBotConfigurations.avatar.icon}
                  className="text-3xl"
                />
              </div>
            </div>
            <div className="flex flex-col gap-3.5">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-fit rounded-md border border-[#00000080] px-4 py-1.5 text-sm font-medium"
                onClick={() => setShowAvatarPicker(true)}
                type="button"
              >
                Replace
              </motion.button>
              <p className="text-xs text-[#00000080]">
                Suggested size is 100 × 100
              </p>
            </div>
          </div>
        </div>

        {/* Color Selector */}
        <div className="flex flex-col gap-3.5 border-b border-b-[#00000014] pb-5">
          <label className="font-semibold">
            Select theme color for chatbot
          </label>
          <div className="flex gap-5">
            {defaultChatBotColors.map((color) => (
              <motion.button
                key={color}
                type="button"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => handleColorSelect(color)}
                className={`h-8 w-8 rounded-full ${
                  chatBotConfigurations.selectedColor === color
                    ? "ring-2 ring-gray-300 ring-offset-2"
                    : ""
                }`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
        </div>
        <div className="flex justify-between gap-3.5 pb-5">
          <label className="flex items-center gap-2 font-semibold">
            <span>Enable chatbot for your website</span>
            <InfoTooltip className="ml-10 w-1/2">
              <p>
                If you want to use the chatbot in your site, you must enable
                this and save interface. Website domain is required if you want to enable this setting.
              </p>
            </InfoTooltip>
          </label>

          <Switch
            checked={chatBotConfigurations.isExposed}
            onCheckedChange={handleExposedToggle}
            style={{ backgroundColor: chatBotConfigurations.isExposed ? "#0FA958" : "#b5b5b5" }}
          />
        </div>

        <Button
          className="mt-auto flex items-center justify-center bg-primary text-sm"
          type="submit"
          disabled={loading || isChatBotFormDisabled(chatBotConfigurations)}
        >
          {loading ? <Loader2 className="animate-spin" /> : "Save Interface"}
        </Button>
      </form>
      {showAvatarPicker && (
        <EmojiPicker
          open={showAvatarPicker}
          onClose={() => setShowAvatarPicker(false)}
          onSelect={(icon, bg) => {
            handleAvatarUpload(icon, bg);
            setShowAvatarPicker(false);
          }}
        />
      )}
    </>
  );
}
