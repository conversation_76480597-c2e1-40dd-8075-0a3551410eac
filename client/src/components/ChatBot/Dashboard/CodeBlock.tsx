import { Check, Copy } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";

export default function CodeBlock({
  code,
  title,
  copyState,
  onCopy,
}: {
  code: string;
  title: string;
  copyState: "READY" | "SUCCESS" | Error;
  onCopy: () => void;
}) {
  return (
    <div className="rounded-lg bg-gray-300 p-3">
      <div className="mb-1 flex items-center justify-between">
        <span className="text-gray-60 text-sm">{title}</span>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="flex h-8 w-8 items-center justify-center rounded-md hover:bg-gray-200"
          onClick={onCopy}
        >
          <AnimatePresence mode="wait">
            {copyState === "SUCCESS" ? (
              <motion.div
                key="check"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <Check className="h-4 w-4 text-green-600" />
              </motion.div>
            ) : (
              <motion.div
                key="copy"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <Copy className="h-4 w-4 text-gray-600" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.button>
      </div>
      <pre className="overflow-x-auto whitespace-pre-wrap break-words text-sm font-medium text-gray-800">
        {code}
      </pre>
    </div>
  );
}
