import { motion } from "motion/react";

import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "../../ui/dialog";

import CodeBlock from "./CodeBlock";

import { useClipboard } from "../../../hooks/use-clipboard";

import { X } from "lucide-react";

interface EmbedCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  scriptCode: string;
  webComponentCode: string;
}

export default function EmbedCodeDialog({
  open,
  onOpenChange,
  scriptCode,
  webComponentCode,
}: EmbedCodeDialogProps) {
  const scriptClipboard = useClipboard();
  const webComponentClipboard = useClipboard();
  return (
    <div className="px-20">
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-sm sm:rounded-[20px] md:max-w-3xl [&>button:last-child]:hidden">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="flex items-center gap-2 text-base font-medium">
              <span className="text-xl font-bold">Embed Code</span>
            </DialogTitle>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onOpenChange(false)}
              className="rounded-full p-1.5 hover:bg-gray-100"
            >
              <X className="h-6 w-6 text-gray-500 transition-transform hover:-rotate-45" />
            </motion.button>
          </DialogHeader>

          <p className="mb-2 text-sm font-medium text-black md:mb-3">
            Follow these steps to integrate the chatbot in your own website
          </p>

          <div className="mb-2 flex flex-col gap-6 md:mb-3">
            <div className="flex flex-col gap-2">
              <p className="font-medium">Step 1: Locate the HTML File</p>
              <p className="text-sm text-gray-600">
                Open your project and find the <code>index.html</code> file. It
                is usually located inside the <code>public/</code> or{" "}
                <code>src/</code> folder, depending on your framework.
              </p>
            </div>
            <div className="flex flex-col gap-2">
              <p className="font-medium">Step 2: Add the Web Component</p>
              <p className="text-sm text-gray-600">
                Paste the following code inside the <code>&lt;body&gt;</code>{" "}
                tag, preferably before the closing <code>&lt;/body&gt;</code>{" "}
                tag.
              </p>
              <CodeBlock
                title="Web Component"
                code={webComponentCode}
                copyState={webComponentClipboard.state}
                onCopy={() => webComponentClipboard.copy(webComponentCode)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <p className="font-medium">Step 3: Add the JavaScript Script</p>
              <p className="text-sm text-gray-600">
                Below the web component, insert the following script to
                initialize the chatbot.
              </p>
              <CodeBlock
                title="JavaScript"
                code={scriptCode}
                copyState={scriptClipboard.state}
                onCopy={() => scriptClipboard.copy(scriptCode)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <p className="font-medium">Step 4: Save and Deploy</p>
              <p className="text-sm text-gray-600">
                Save the file and reload your website to verify the chatbot is
                working. If you're deploying the site, make sure to push the
                changes and test the integration.
              </p>
            </div>
          </div>

          <p className="pb-4 text-sm text-black">
            If you have issues setting this up, reach out to{" "}
            <a
              href="mailto:<EMAIL>"
              target="_blank"
              className="text-primary hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </DialogContent>
      </Dialog>
    </div>
  );
}
