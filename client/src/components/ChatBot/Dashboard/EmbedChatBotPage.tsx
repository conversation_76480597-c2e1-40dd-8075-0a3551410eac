import { useRef, useState } from "react";
import { motion } from "motion/react";
import { But<PERSON> } from "../../ui/button";

import ChatBotSettingsPanel from "./ChatBotSettingsPanel";
import ChatBotPreviewPanel from "./ChatBotPreviewPanel";
import EmbedCodeDialog from "./EmbedChatBotDialog";

import { updateChatBot } from "../../../api";
import { useToast } from "../../../hooks/use-toast";

import { ChatBotConfig } from "../../../pages/ChatBot";

import { Code, MessageSquareCode, MoveLeft } from "lucide-react";
import { Link } from "react-router";

export default function EmbedChatBotPage({
  botConfig,
  agentId,
}: {
  botConfig: ChatBotConfig;
  agentId: string;
}) {
  const [chatBotConfigurations, setChatBotConfigurations] = useState(botConfig);
  const [loading, setLoading] = useState(false); // For tracking loading state
  const [showEmbedCodeButton, setShowEmbedCodeButton] = useState(
    botConfig.isExposed,
  );
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isExposedToggleRef = useRef(false)
  const { toast } = useToast();

  // Utility function to update configuration state
  const updateConfigurations = (
    key: string,
    value: string | null | {} | boolean,
  ): void => {
    setChatBotConfigurations((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };

  // Generic change handler for text inputs
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateConfigurations(name, value);
  };

  // Handler for color selection
  const handleColorSelect = (color: string) => {
    updateConfigurations("selectedColor", color);
  };

  // Handler for file upload
  const handleAvatarUpload = (icon: string, bg: string) => {
    updateConfigurations("avatar", {
      icon,
      bg,
    });
  };
  const handleExposedToggle = (isExposed: boolean) => {
    updateConfigurations("isExposed", isExposed);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (loading) return;

    const formData = new FormData();
    formData.append("agent_name", chatBotConfigurations.botName);
    formData.append("authorized_domain", chatBotConfigurations.domain);
    formData.append("color_theme", chatBotConfigurations.selectedColor);
    formData.append("agent_avatar", chatBotConfigurations.avatar.icon);
    formData.append("agent_color", chatBotConfigurations.avatar.bg);
    formData.append(
      "is_exposed",
      JSON.stringify(chatBotConfigurations.isExposed),
    );

    try {
      setLoading(true);
      const updatedChatBot = await updateChatBot(agentId, formData);
      toast({
        description: `Successfully updated the chatbot`,
      });
      setShowEmbedCodeButton(updatedChatBot.data.is_exposed);
      // Check if from previous render, isExposed: false -> true
      if(updatedChatBot.data.is_exposed && !isExposedToggleRef.current){
        setIsDialogOpen(true)
      }
      
      isExposedToggleRef.current = updatedChatBot.data.is_exposed

    } catch (err) {
      toast({
        variant: "destructive",
        description: err?.message || `Failed to update the chatbot`,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="flex min-h-screen flex-col bg-white">
        <nav className="flex items-center justify-between border-b border-b-[#E3E6EA] px-7 py-4">
          <div className="flex items-center gap-2.5">
            <Link to="/" className="mr-3">
              <MoveLeft size={24} />
            </Link>
            <MessageSquareCode size={18} />
            <h1 className="text-base font-semibold">
              Integrate chatbot on website
            </h1>
          </div>
          <motion.div className="flex items-center gap-3.5" layout>
            {
              <Button
                className="flex items-center justify-center gap-2 text-sm disabled:cursor-not-allowed"
                variant="outline"
                onClick={() => setIsDialogOpen(true)}
                disabled={!showEmbedCodeButton}
              >
                <Code size={14} />
                <span>Embed Code</span>
              </Button>
            }
          </motion.div>
        </nav>

        <motion.main
          className="flex flex-1 flex-col-reverse lg:flex-row"
          layout
        >
          {/* Left Panel - Configuration */}
          <ChatBotSettingsPanel
            chatBotConfigurations={chatBotConfigurations}
            handleTextChange={handleTextChange}
            handleColorSelect={handleColorSelect}
            handleAvatarUpload={handleAvatarUpload}
            handleExposedToggle={handleExposedToggle}
            onSubmit={handleSubmit}
            loading={loading}
          />

          {/* Right Panel - Preview */}
          <ChatBotPreviewPanel chatBotConfigurations={chatBotConfigurations} />
        </motion.main>
      </div>

      <EmbedCodeDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        webComponentCode={`<chat-bot-widget agent-id="${agentId}"></chat-bot-widget>`}
        scriptCode={`<script src="${window.location.origin}/chatbot/widget.umd.js"></script>`}
      />
    </>
  );
}
