import { motion } from "motion/react";
import <PERSON><PERSON><PERSON><PERSON> from "../../../../assets/bling_logo.svg";

interface BaseLayoutProps {
  children: React.ReactNode;
}

const BaseLayout = ({ children }: BaseLayoutProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="flex min-h-screen w-full items-center justify-center bg-[#F5F5F7]"
    >
      <motion.div
        className="w-full max-w-[600px] rounded-[30px] bg-white p-8 shadow-lg lg:p-12"
        initial={{ scale: 0.95 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="mx-auto w-fit flex items-center gap-2 mb-8">
          <img src={BuddhiLogo} alt="BuddhiAI" className="h-6 w-6" />
          <span className="text-lg font-bold">
            Buddhi<span className="text-blue-600">AI</span>
          </span>
        </div>
        {children}
      </motion.div>
    </motion.div>
  );
};

export default BaseLayout;
