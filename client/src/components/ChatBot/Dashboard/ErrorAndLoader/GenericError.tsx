import { motion } from "motion/react";
import { useNavigate } from "react-router";
import BaseLayout from "./BaseLayout";

export default function GenericError({ to }) {
  const navigate = useNavigate();
  return (
    <BaseLayout>
      <motion.div
        className="flex flex-col items-center gap-6 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <h1 className="text-xl font-semibold text-gray-900">
          Oops! Something Went Wrong
        </h1>
        <p className="w-[80%] text-[#666] -mt-3 mb-3">
          An unexpected error occurred. This could be due to a temporary issue
          or an unanticipated condition in the system. Please refresh the page
          or try again later. If the issue persists, contact support for
          assistance.
        </p>
        <div className="flex w-full flex-col gap-3 lg:w-[80%]">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full rounded-lg bg-blue-600 py-3 text-white font-semibold hover:bg-blue-700 transition-colors"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full rounded-lg bg-transparent py-3 text-primary font-semibold hover:bg-zinc-100 transition-colors border border-primary"
            onClick={() => navigate(to, { replace: true })}
          >
            Go Back
          </motion.button>
        </div>
      </motion.div>
    </BaseLayout>
  );
}
