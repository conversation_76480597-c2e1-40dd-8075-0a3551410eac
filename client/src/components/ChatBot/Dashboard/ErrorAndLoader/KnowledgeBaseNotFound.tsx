import { motion } from "motion/react";
import { useNavigate } from "react-router";
import BaseLayout from "./BaseLayout";

export default function KnowledgeBaseNotFound({ to }) {
  const navigate = useNavigate();
  return (
    <BaseLayout>
      <motion.div
        className="flex flex-col items-center gap-6 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <h1 className="text-xl font-semibold text-gray-900">
          Knowledge Base Doesn't Exist
        </h1>
        <p className="text-gray-600">
          The requested knowledge base could not be found. It seems like it has
          not been added or created yet. Please create a new knowledge base to
          proceed.
        </p>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full rounded-lg bg-transparent py-3 text-primary font-semibold hover:bg-zinc-100 transition-colors border border-primary"
          onClick={() => navigate(to, { replace: true })}
        >
          Go Back
        </motion.button>
      </motion.div>
    </BaseLayout>
  );
}
