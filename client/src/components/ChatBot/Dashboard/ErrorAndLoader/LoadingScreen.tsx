import { motion } from "motion/react";
import BaseLayout from "./BaseLayout";

const LoadingSpinner = () => (
  <motion.div
    className="h-12 w-12 rounded-full border-[6.5px] border-gray-300 border-t-black"
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
  />
);

export default function LoadingScreen() {
  return (
    <BaseLayout>
      <motion.div
        className="flex flex-col items-center gap-6 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <LoadingSpinner />
        <p className="text-[#666] font-normal text-pretty text-sm md:text-base">
          Please wait a moment while the process completes. If the loading takes
          too long, ensure your network connection is stable and retry if
          necessary.
        </p>
      </motion.div>
    </BaseLayout>
  );
}
