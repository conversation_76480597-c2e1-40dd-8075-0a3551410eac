import { motion } from "motion/react";
import { useNavigate } from "react-router";
import BaseLayout from "./BaseLayout";

export default function Unauthorized({ to }) {
  const navigate = useNavigate();
  return (
    <BaseLayout>
      <motion.div
        className="flex flex-col items-center gap-6 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <h1 className="text-xl font-semibold text-gray-900">
          Files in knowledge base is not authorized
        </h1>
        <p className="text-gray-600">
          You do not have the necessary permissions. It may not be public or
          your account lacks the required access rights—please contact the
          administrator if needed.
        </p>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full rounded-lg bg-transparent py-3 text-primary font-semibold hover:bg-zinc-100 transition-colors border border-primary lg:w-[80%]"
          onClick={() => navigate(to, { replace: true })}
        >
          Home
        </motion.button>
      </motion.div>
    </BaseLayout>
  );
}
