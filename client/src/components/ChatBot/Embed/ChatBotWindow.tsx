import { useState, useEffect, useRef } from "react";
import { motion } from "motion/react";
import { MoveUp, X, Minimize2 } from "lucide-react";
import TypingIndicator from "./TypingIndicator";
import Message from "./Message";
import { queryPublicChatBot } from "../../../api";
import { getFingerprint } from "@thumbmarkjs/thumbmarkjs";
import { formatEmbedBotResponse } from "../../../lib/utils";
import AppIcon from "../../EmojiPicker/AppIcon";

interface ChatBotWindowProps {
  chatBotConfigurations: any;
  onClose: () => void;
  onMinimize: () => void;
  agentId: string;
  messages: any[];
  setMessages: (messages: any[]) => void;
}

export default function ChatBotWindow({
  chatBotConfigurations,
  onClose,
  onMinimize,
  agentId,
  messages,
  setMessages,
}: ChatBotWindowProps) {
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    const userMessage = {
      content: inputValue,
      type: "USER",
      color: chatBotConfigurations.selectedColor,
    };

    setMessages([...messages, userMessage]);
    setInputValue("");
    setIsLoading(true);

    // Generate unique device fingerprint hash
    const fingerprint = await getFingerprint();

    try {
      const response = await queryPublicChatBot(
        agentId,
        inputValue,
        fingerprint,
      );
      const systemMessage = {
        content: formatEmbedBotResponse(response.data.content),
        type: "SYSTEM",
      };
      setMessages((prev) => [...prev, systemMessage]);
    } catch (error) {
      let status;
      if (error.response) {
        status = error.response.status;
      }
      const errorText =
        status === 429
          ? "Sorry, you have exceeded your message quota. Please try again later."
          : "Sorry, I couldn't process your message. Please try again.";
      const errorMessage = {
        content: errorText,
        type: "SYSTEM",
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="flex h-full w-full items-center justify-center">
      <div
        className="w-full overflow-hidden rounded-2xl bg-white shadow-lg"
        style={{ height: "80vh" }}
      >
        <div className="flex items-center justify-center gap-3 border-b border-b-[#e5e5e5] p-4">
          <div
            className="flex h-10 w-10 items-center justify-center rounded-full"
            style={{ backgroundColor: chatBotConfigurations.avatar.bg }}
          >
            <AppIcon
              icon={chatBotConfigurations.avatar.icon}
              className="text-xl"
            />
          </div>
          <span className="font-medium">{chatBotConfigurations.botName}</span>
          <div className="ml-auto flex gap-3.5">
            <button
              onClick={onMinimize}
              className="transition-transform hover:scale-[0.85]"
            >
              <Minimize2 size={18} />
            </button>
            <button
              onClick={onClose}
              className="transition-transform hover:-rotate-45"
            >
              <X size={18} />
            </button>
          </div>
        </div>

        <div className="scrollbar-hide h-[calc(80vh-140px)] overflow-y-auto bg-gray-50 p-4">
          <div className="flex flex-col gap-5">
            {messages.map((message, index) => (
              <Message
                key={index}
                message={message}
                isUser={message.type === "USER"}
              />
            ))}
            {isLoading && (
              <motion.div className="self-start rounded-[8px] bg-white p-3 shadow-sm">
                <TypingIndicator />
              </motion.div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        <div className="border-t border-t-[#e5e5e5] bg-white p-4">
          <form onSubmit={handleSubmit}>
            <div className="flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-4 py-2">
              <input
                type="text"
                placeholder="Send message..."
                className="flex-1 border-none bg-transparent font-inter text-sm focus:outline-none"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                disabled={isLoading}
              />
              <button
                type="submit"
                disabled={isLoading}
                className="flex h-6 w-6 items-center justify-center rounded-full"
                style={{ backgroundColor: chatBotConfigurations.selectedColor }}
              >
                <MoveUp size={14} className="text-white" />
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}
