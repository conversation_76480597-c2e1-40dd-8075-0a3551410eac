import { useState, useEffect } from "react";
import { AnimatePresence, motion } from "motion/react";

import { Loader2 } from "lucide-react";

import ChatBotWindow from "./ChatBotWindow";

import { getPublicChatBotConfig } from "../../../api";

import tailwindStyles from "../../../index.css?inline";
import { ChatBotConfig } from "../../../pages/ChatBot";

interface ChatBotWidgetProps {
  agentId: string;
}

export default function ChatBotWidget({ agentId }: ChatBotWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chatBotConfigurations, setChatBotConfigurations] =
    useState<null | Partial<ChatBotConfig>>(null);
  const [messages, setMessages] = useState([]);
  const [showErrorTooltip, setShowErrorTooltip] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await getPublicChatBotConfig(agentId);
        const data = response.data;
        // Mapping snake_case to camelCase
        const mappedConfig = {
          botName: data.agent_name,
          avatar: { bg: data.avatar_color, icon: data.agent_avatar },
          selectedColor: data.color_theme,
        };

        setChatBotConfigurations(mappedConfig);
        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };
    fetchConfig();
  }, [agentId]);

  const handleClose = () => {
    setIsOpen(false);
    setMessages([]); // Clear messages on close
  };

  const handleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleCloseWithAnimation = () => {
    setIsClosing(true);
    setTimeout(() => {
      handleClose();
      setIsClosing(false);
    }, 200);
  };

  const handleMinimizeWithAnimation = () => {
    setIsClosing(true);
    setTimeout(() => {
      handleMinimize();
      setIsClosing(false);
    }, 200);
  };

  if (loading) {
    return (
      <>
        <style>{tailwindStyles}</style>
        <div className="fixed bottom-5 right-5 flex flex-1 items-center justify-center rounded-2xl py-5">
          <motion.div
            className="flex h-14 w-14 items-center justify-center rounded-full bg-blue-500 shadow-lg"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 className="h-6 w-6 text-white" />
          </motion.div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <style>{tailwindStyles}</style>
        <div className="fixed bottom-5 right-5 flex flex-1 items-center justify-center rounded-2xl py-5">
          <div
            className="relative"
            onMouseEnter={() => setShowErrorTooltip(true)}
            onMouseLeave={() => setShowErrorTooltip(false)}
          >
            <motion.div className="flex h-14 w-14 cursor-pointer items-center justify-center rounded-full bg-[#D95345]/70 shadow-lg">
              💬
            </motion.div>
            <AnimatePresence mode="wait">
              {showErrorTooltip && (
                <motion.div
                  className="absolute -top-16 right-0 w-64 rounded-[6px] bg-white/90 px-3 py-1 text-sm text-black shadow-lg md:w-80"
                  initial={{ scale: 0.5 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0 }}
                >
                  Sorry, the chatbot is currently unavailable. Please try again
                  later!
                  <div className="absolute right-5 top-full h-0 w-0 border-x-8 border-t-8 border-x-transparent border-t-white"></div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <style>{tailwindStyles}</style>
      <div className="fixed bottom-5 right-5 flex-1 rounded-2xl font-inter">
        {(!isOpen || isMinimized) && (
          <button
            className="flex h-14 w-14 cursor-pointer items-center justify-center rounded-full text-2xl text-white shadow-lg transition-transform duration-200 hover:scale-110 active:scale-90"
            onClick={() => {
              if (!isOpen) setIsOpen(true);
              if (isMinimized) setIsMinimized(false);
            }}
            style={{ backgroundColor: chatBotConfigurations?.selectedColor }}
          >
            💬
          </button>
        )}
        {isOpen && !isMinimized && (
          <motion.div
            className={`w-[300px] origin-bottom-right 2xl:w-[500px] ${isClosing ? "animate-chatWindowClose" : "animate-chatWindowAppear"} `}
          >
            <ChatBotWindow
              chatBotConfigurations={chatBotConfigurations}
              onClose={handleCloseWithAnimation}
              onMinimize={handleMinimizeWithAnimation}
              agentId={agentId}
              messages={messages}
              setMessages={setMessages}
            />
          </motion.div>
        )}
      </div>
    </>
  );
}
