import { motion } from "motion/react";

interface MessageProps {
  message: {
    content: string;
    color?: string;
  };
  isUser: boolean;
}

export default function Message({ message, isUser }: MessageProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={`${
        isUser ? "self-end text-white" : "self-start bg-white"
      } max-w-[80%] rounded-[8px] px-3 py-2.5 shadow-sm`}
      style={isUser ? { backgroundColor: message.color } : {}}
    >
      {isUser ? (
        <p
          className="overflow-wrap-break-word hyphens-auto break-words text-sm"
          style={{ wordBreak: "break-word", overflowWrap: "break-word" }}
        >
          {message.content}
        </p>
      ) : (
        message.content.split("|||").map((text, index, arr) => (
          <>
            <p
              className="overflow-wrap-break-word hyphens-auto break-words text-sm"
              style={{ wordBreak: "break-word", overflowWrap: "break-word" }}
            >
              {text}
            </p>
            {index! == arr.length - 1 && <br />}
          </>
        ))
      )}
    </motion.div>
  );
}
