import { motion } from 'motion/react';

export default function TypingIndicator() {
  return (
    <motion.div 
      className="flex gap-1 p-1"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {[1, 2, 3].map((dot) => (
        <motion.div
          key={dot}
          className="w-2 h-2 rounded-full bg-gray-400"
          animate={{ y: [0, -5, 0] }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: dot * 0.1
          }}
        />
      ))}
    </motion.div>
  );
}