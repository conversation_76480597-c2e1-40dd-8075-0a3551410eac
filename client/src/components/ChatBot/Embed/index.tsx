import ReactDOM from "react-dom/client";

import ChatWidget from "./ChatWidget";

import { normalizeAttribute } from "../../../lib/utils";

class ChatBotWidget extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
  }
  connectedCallback() {
    const props = this.getPropsFromAttributes();
    const root = ReactDOM.createRoot(this.shadowRoot);
    root.render(<ChatWidget {...props} />);
  }

  getPropsFromAttributes() {
    const props = {};
    for (const { name, value } of this.attributes) {
      props[normalizeAttribute(name)] = value;
    }
    return props;
  }
}

export default ChatBotWidget;
