import { useState } from "react";
import ChatExplore, { AgentData } from "./components/ChatExplore";
import { deleteChat } from "../../api/";
import { useLocation, useNavigate } from "react-router";
import { useKnowledgeBaseStore } from "../../store/knowledgeBaseStore";
import { PAGE_SIZE } from "../../lib/utils";
import { useAgentStore } from "../../store/agentStore";
import AppHeader from "../AppHeaderComponent";
import PaginationComponent from "../KnowledgeBaseExplorerComponent/Pagination";

type ChatPageProps = {
  chats: AgentData[];
  fetchChats: ({ ...any }) => Promise<void>;
  totalPages: number;
  setTotalPages: React.Dispatch<React.SetStateAction<number>>;
};

export default function ChatPage({
  chats,
  fetchChats,
  totalPages,
  setTotalPages,
}: ChatPageProps) {
  const [selectedChat, setSelectedChat] = useState<{
    sessionId: string;
    folderId: string;
    
  } | null>(null);
  const [page, setPage] = useState(1);
  const navigate = useNavigate();
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (state) => state.setActiveKnowledgeBase,
  );
  const setActiveAgent = useAgentStore((state) => state.setActiveAgent);
  const location = useLocation();

  const handleDeleteChat = async (sessionId: string) => {
    try {
      await deleteChat(sessionId);
      setActiveKnowledgeBase(null, location.pathname);
      fetchChats({ page: 1, page_size: PAGE_SIZE });
    } catch (error) {
      console.error("Failed to delete chat:", error);
    }
  };

  const handleSelectChat = (agent: Partial<AgentData>) => {
    navigate(`/chat/${agent.agent_name}/${agent.folder_id}/${agent.id}`);
    setActiveAgent(agent);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  return (
    <div className="w-full bg-sidebar">
      <div className="flex flex-col items-center">
        {selectedChat && chats?.length === 0 && (
          <div className="sticky top-0 z-10 w-full">
            <AppHeader />
          </div>
        )}
        <main className="flex h-[calc(100vh-4.5rem)] w-full flex-col justify-between">
          <ChatExplore
            initialChats={chats}
            onDeleteChat={handleDeleteChat}
            onSelectChat={handleSelectChat}
            setTotalPages={setTotalPages}
            setPage={setPage}
            page={page}
          />

          {totalPages > 1 && (
            <div className="flex justify-center md:mb-28">
              <PaginationComponent
                handlePageChange={handlePageChange}
                page={page}
                totalPages={totalPages}
              />
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
