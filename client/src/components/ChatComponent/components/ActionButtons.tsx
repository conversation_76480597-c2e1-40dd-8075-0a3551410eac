import {
  FileTextIcon,
  LightbulbIcon,
  StarIcon,
  BarChartIcon,
} from "lucide-react";
import { Button } from "../../ui/button";
import { cn } from "../../../lib/utils";

export default function ActionButtons({
  setInputValue,
  className,
}: {
  setInputValue: React.Dispatch<React.SetStateAction<string | undefined>>;
  className?: string;
}) {
  const actions = [
    { icon: FileTextIcon, label: "Summarize Text", color: "#7A90FF" },
    { icon: LightbulbIcon, label: "Brainstorm", color: "#FF7300" },
    { icon: StarIcon, label: "Key Takeaways", color: "#2CC675" },
    { icon: BarChartIcon, label: "Analyse Data", color: "#FF57DD" },
  ];

  return (
    <div
      className={cn(
        "flex w-full flex-wrap justify-center gap-4 p-4",
        className,
      )}
    >
      {actions.map((action, index) => (
        <Button
          key={index}
          variant="outline"
          size="lg"
          className="shrink-0 gap-3 rounded-lg shadow-[0_-1px_100px_0_rgba(0,0,0,0.07)]"
          onClick={() => setInputValue(action.label + ": ")}
        >
          <action.icon className="h-4 w-4" color={action.color} />
          {action.label}
        </Button>
      ))}
    </div>
  );
}
