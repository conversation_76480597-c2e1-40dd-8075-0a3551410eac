import { Avatar, AvatarImage, AvatarFallback } from "../../ui/avatar";
import { cn } from "../../../lib/utils";
import { Skeleton } from "../../ui/skeleton";
import { Tooltip, TooltipContent, TooltipTrigger } from "../../ui/tooltip";
import { useEffect, useState } from "react";
import TextReveal from "./TextReveal";
import { useUserStore } from "../../../store/userStore";
import { Code, FileText, Link2 } from "lucide-react";
import { motion } from "motion/react";
import { getPresignedURLByFileName } from "../../../api";
import { useParams } from "react-router";
import PdfPreview from "../../ui/file/pdf-preview";
import Markdown from "react-markdown";
import CustomMarkdown from "../../shared/markdown";
import remarkGfm from "remark-gfm";

type MetadataType = {
  file_directory: string;
  filename: string;
  filetype: string;
  languages: string[];
  last_modified: string;
  page_number: number;
  text: string;
  unique_id: string;
  file_index: number;
  source: string; // URL or file path
};

export type Plot = {
  title: string;
  url: string;
};

type ChatContent =
  | string
  | {
      summary: string;
      details: Array<{
        answer: string;
        metadata: MetadataType[];
      }>;
    }
  | {
      message_intro: string;
      response_text: string;
    };

type ChatBubbleProps = {
  type: "USER" | "SYSTEM" | "LOADING";
  content: ChatContent;
  metadata_text?: MetadataType[];
  plots?: Plot[];
  onPlotClick?: (plot: Plot) => void;
};

const ChatBubble: React.FC<ChatBubbleProps> = ({
  type,
  content,
  plots,
  onPlotClick,
}) => {
  const { user, fetchUser, isLoading } = useUserStore();
  const isUser = type === "USER";
  const isLoadingBubble = type === "LOADING";
  const requiresPanel = plots && plots.length !== 0;
  const { folderId } = useParams();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [presignedURL, setPresignedURL] = useState<string | null>(null);

  useEffect(() => {
    if (!user?.username && !isLoading) {
      fetchUser();
    }
  }, [user?.username, isLoading, fetchUser]);

  const handleShowPreview = async (fileName: string) => {
    const storedData =
      JSON.parse(localStorage.getItem("presignedURLsByFileName") as string) ||
      {};
    const fileData = storedData[fileName];

    if (fileData) {
      setPresignedURL(fileData);
      setIsDialogOpen(true);
      return;
    }

    setLoading(true);
    setError(null);
    setIsDialogOpen(true);
    try {
      const response = await getPresignedURLByFileName(folderId!, fileName);
      const newPresignedURL = response.data?.url;

      storedData[fileName] = newPresignedURL;
      localStorage.setItem(
        "presignedURLsByFileName",
        JSON.stringify(storedData),
      );

      setTimeout(() => {
        const updatedData =
          JSON.parse(
            localStorage.getItem("presignedURLsByFileName") as string,
          ) || {};
        delete updatedData[fileName];
        localStorage.setItem(
          "presignedURLsByFileName",
          JSON.stringify(updatedData),
        );
      }, 1800 * 1000);

      setPresignedURL(newPresignedURL);
    } catch {
      setError("Something went wrong. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const renderMetadata = (metadata: MetadataType[]) => (
    <div className="max-h-64 py-2 text-xs">
      {metadata.map((meta, index) => (
        <div key={index}>
          <div className="flex items-center gap-4 pb-4">
            <FileText size={48} />
            <div className="flex flex-col">
              <div className="text-sm">{meta.filename}</div>
              <div className="text-md">{meta.filetype}</div>
            </div>
          </div>
          <Markdown remarkPlugins={[remarkGfm]} className="prose mb-2 text-xs">
            {meta.text}
          </Markdown>
        </div>
      ))}
    </div>
  );

  const truncateFilename = (filename: string, maxLength: number = 40) => {
    if (filename.length <= maxLength) return filename;
    const startLength = Math.ceil(maxLength / 2);
    const endLength = Math.floor(maxLength / 2) - 3; // Adjust for '...'
    return filename.slice(0, startLength) + "..." + filename.slice(-endLength);
  };

  const renderSources = (details: Array<{ metadata: MetadataType[] }>) => {
    const uniqueSourcesWithMeta = Array.from(
      new Map(
        details.flatMap((detail) =>
          detail.metadata.map((meta) => [meta.filename, meta]),
        ),
      ).values(),
    );

    return (
      uniqueSourcesWithMeta.length > 0 && (
        <div className="mt-4 flex items-center pt-2 text-sm">
          <p className="mr-1 flex items-center text-primary underline">
            {uniqueSourcesWithMeta.some((meta) =>
              meta.source?.toLowerCase().startsWith("http"),
            ) ? (
              <Link2 size={16} className="mr-1 text-primary" />
            ) : (
              <FileText size={16} className="mr-1 text-primary" />
            )}
            Source:
          </p>
          {uniqueSourcesWithMeta.map((meta, index) => {
            const isUrl = meta.source?.toLowerCase().startsWith("http");
            return (
              <div key={index} className="inline-block">
                {isUrl ? (
                  <a
                    href={meta.source}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mr-2 cursor-pointer text-primary underline"
                  >
                    {truncateFilename(meta.filename)}
                  </a>
                ) : (
                  <>
                    <span
                      className="mr-2 cursor-pointer text-primary underline"
                      onClick={() => handleShowPreview(meta.filename)}
                    >
                      {truncateFilename(meta.filename)}
                    </span>
                    <PdfPreview
                      source={{ file: presignedURL, fileName: meta.filename }}
                      isDialogOpen={isDialogOpen}
                      setIsDialogOpen={setIsDialogOpen}
                      loading={loading}
                      error={error}
                    />
                  </>
                )}
              </div>
            );
          })}
        </div>
      )
    );
  };

  const renderContent = () => {
    
    if (isLoadingBubble) {
      return <Skeleton className="h-4 w-full" />;
    }
    if (isUser) {
      return <p className="text-sm">{content as string}</p>;
    }
    if (typeof content === "string") {
      const processedContent = content
      .replace(/\\n/g, '\n')  // Convert \n to actual newlines
      .replace(/^"|"$/g, '')  // Remove leading/trailing quotes
      .trim();
      
      return <CustomMarkdown>{processedContent}</CustomMarkdown>;
    }
    if (
      typeof content === "object" &&
      "message_intro" in content &&
      "response_text" in content
    ) {
      return (
        <div>
          <p className="text-sm">{content.message_intro}</p>
          {requiresPanel &&
            plots.map(
              (plot, index) =>
                plot.url && (
                  <div
                    key={index}
                    className="border-black/8 my-4 flex max-w-md cursor-pointer gap-2 rounded-2xl border-2 bg-[#F5F5F7] px-4 py-3"
                    onClick={() => onPlotClick && onPlotClick(plot)}
                  >
                    <div className="flex h-8 w-8 items-center justify-center rounded-sm bg-white">
                      <Code color="#2463EB" size={16} />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-black">
                        {plot.title}
                      </div>
                      <div className="text-xs">Click to open diagram</div>
                    </div>
                  </div>
                ),
            )}
          <p className="mt-4 text-sm">{content.response_text}</p>
        </div>
      );
    }
    if (
      typeof content === "object" &&
      "summary" in content &&
      content.summary
    ) {
      return (
        <div>
          <p className="text-sm">{content.summary}</p>
          {content.details.map((detail, index) => (
            <div key={index} className="mt-4 flex items-center">
              <motion.p className="text-sm" transition={{ duration: 0.7 }}>
                {index + 1}. {detail.answer}
                {detail.metadata &&
                  detail.metadata.length > 0 &&
                  detail.metadata.map((meta) => {
                    const isUrl = meta.source?.toLowerCase().startsWith("http");
                    return isUrl ? (
                      <button
                        key={meta.file_index}
                        className="ml-2 h-5 w-5 rounded-lg bg-blue-200 text-xs text-blue-500"
                        aria-label="Source Index"
                        onClick={() =>
                          window.open(
                            meta.source,
                            "_blank",
                            "noopener,noreferrer",
                          )
                        }
                      >
                        {meta.file_index}
                      </button>
                    ) : (
                      <Tooltip key={meta.file_index}>
                        <TooltipTrigger asChild>
                          <button
                            className="ml-2 h-5 w-5 rounded-lg bg-blue-200 text-xs text-blue-500"
                            aria-label="View Metadata"
                          >
                            {meta.file_index}
                          </button>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[20rem] overflow-scroll p-6 text-left">
                          {renderMetadata([meta])}
                        </TooltipContent>
                      </Tooltip>
                    );
                  })}
              </motion.p>
            </div>
          ))}
          {content.details.length > 0 && renderSources(content.details)}
        </div>
      );
    }
    return null;
  };

  return (
    <div
      className={cn(
        "flex gap-4 rounded-xl",
        !isUser
          ? "items-start justify-start px-6 py-8"
          : "items-end justify-end py-4",
      )}
    >
      {/* Avatar */}
      {!isUser && (
        <Avatar className="flex items-center justify-center">
          <AvatarImage
            asChild
            src="/bling_fill.svg"
            className="h-full w-full bg-primary"
          >
            <img
              src="/bling_fill.svg"
              alt="System Avatar"
              className="bg-primary object-contain"
            />
          </AvatarImage>
          <AvatarFallback>AI</AvatarFallback>
        </Avatar>
      )}

      {/* Chat Bubble */}
      <div>
        <span className="text-dark-800 text-sm font-semibold">
          {!isUser && "BuddhiAI"}
        </span>
        <div
          className={cn(
            "relative rounded-lg py-2 text-[#666F8D] shadow-sm",
            isUser && "rounded-[20px] bg-primary px-6 py-4 text-white",
          )}
        >
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default ChatBubble;
