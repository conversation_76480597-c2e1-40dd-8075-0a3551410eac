import { useCallback, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON> } from "../../ui/button";
import { Input } from "../../ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "../../ui/card";
import { LucideSearch, TrashIcon, Code, MessageCircleMore } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../../ui/dialog";
import { useNavigate } from "react-router";
import { formatDateToDayMonth } from "../../../lib/utils";
import { useDebouncedCallback } from "use-debounce";
import { getAllAgents } from "../../../api";
import { Skeleton } from "../../ui/skeleton";
import { cn, PAGE_SIZE } from "../../../lib/utils";
import CreateChatDialog from "../../CreateChatDialog";
import { useUserStore } from "../../../store/userStore";
import { TooltipProvider } from "@radix-ui/react-tooltip";
import { Tooltip, TooltipContent, TooltipTrigger } from "../../ui/tooltip";
import AppIcon from "../../EmojiPicker/AppIcon";

export type AgentData = {
  id: string;
  agent_name: string;
  agent_avatar: string; // emoji string
  avatar_color: string;
  color_theme: string; // Hex color code
  authorized_domain: string;
  folder_id: string;
  folder_name: string;
  last_updated: string; // ISO 8601 date string
};

type ChatExploreProps = {
  initialChats: AgentData[];
  onDeleteChat: (sessionId: string) => void;
  onSelectChat: (agent: Partial<AgentData>) => void;
  setTotalPages: React.Dispatch<React.SetStateAction<number>>;
  page: number;
  setPage: React.Dispatch<React.SetStateAction<number>>;
};

const ChatExplore = ({
  initialChats,
  onDeleteChat,
  onSelectChat,
  setTotalPages,
  page,
  setPage,
}: ChatExploreProps) => {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [agents, setAllAgents] = useState(initialChats);
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigate = useNavigate();
  const [isFetching, setIsFetching] = useState(false);
  const [query, setQuery] = useState("");
  const isInitialRender = useRef(true); // Track if it's the first render
  const skipPageEffect = useRef(false); // Flag to skip effect when `page` is updated due to query change

  const user = useUserStore((state) => state.user);

  const MAX_FREE_CHATBOTS = user.max_agents;

  const fetchAgentsWithParams = useCallback(
    async (params: {
      page: number;
      page_size: number;
      search?: string;
      search_enabled: boolean;
    }) => {
      setIsFetching(true);
      try {
        const response = await getAllAgents(params);
        const pages = response?.count;
        setTotalPages(Math.ceil(pages / PAGE_SIZE));
        setAllAgents(response.results);
      } finally {
        setIsFetching(false);
      }
    },
    [],
  );

  const debouncedSearch = useDebouncedCallback((searchQuery: string) => {
    setPage(1); // Reset to page 1 when searching
    skipPageEffect.current = true; // Skip the page effect since it's triggered by a query change
    fetchAgentsWithParams({
      page: 1,
      page_size: PAGE_SIZE,
      search: searchQuery,
      search_enabled: true,
    });
  }, 500);

  useEffect(() => {
    // Prevent the effect from running on the first render
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    // Skip the API call if `page` was changed due to query update
    if (skipPageEffect.current) {
      skipPageEffect.current = false;
      return;
    }

    fetchAgentsWithParams({
      page,
      page_size: PAGE_SIZE,
      search: query,
      search_enabled: !!query,
    });
  }, [page]);

  const handleDelete = () => {
    if (selectedAgent) {
      onDeleteChat(selectedAgent);
      setDialogOpen(false);
      setSelectedAgent(null);
    }
  };

  return (
    <div className="h-full w-full bg-sidebar p-6">
      <div className="mb-6 flex items-center justify-between gap-10">
        <div className="w-full"></div>
        <div className="max-w-lg">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <CreateChatDialog
                    activeKnowledgeBase={false}
                    triggerText="Create"
                    disabled={
                      !user.is_paid && agents.length >= MAX_FREE_CHATBOTS
                    }
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent className="m-3 max-w-md">
                {!user.is_paid && agents.length >= MAX_FREE_CHATBOTS ? (
                  <div className="p-4">
                    <h1 className="text-lg font-semibold">
                      You are on a Free Plan!
                    </h1>
                    <p className="py-2">
                      {`Current plan only supports ${MAX_FREE_CHATBOTS} ${MAX_FREE_CHATBOTS === 1 ? "agent" : "agents"} creation. Please
                      contact us if you want to upgrade!`}
                    </p>
                    <Button variant="default">Contact Us</Button>
                  </div>
                ) : (
                  "Create a new agent."
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="my-6 flex w-full items-center rounded-md border bg-white px-2 py-1 shadow-sm">
        <div className="flex items-center space-x-2 border-r bg-white px-3">
          <LucideSearch className="mx-2 text-gray-500" size={18} />
          <span className="text-sm text-gray-500">Search agents</span>
        </div>
        <Input
          type="text"
          className="flex-1 border-none focus:!ring-offset-0 focus-visible:ring-transparent"
          onChange={(e) => {
            const searchValue = e.target.value;
            setQuery(searchValue);
            debouncedSearch(searchValue);
          }}
        />
      </div>

      <div
        className={cn(
          "flex flex-col",
          !isFetching && agents?.length === 0 && "items-center justify-center",
        )}
      >
        {isFetching ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {Array(3)
              .fill(null)
              .map((_, idx) => (
                <div className="" key={idx}>
                  <Skeleton className="h-32 w-full rounded-xl bg-black/30" />
                </div>
              ))}
          </div>
        ) : agents.length === 0 ? (
          <div className="mx-auto flex w-fit flex-col items-center gap-5 font-semibold">
            <div className="mt-12 rounded-full bg-white p-5">
              <MessageCircleMore size={32} className="text-primary" />
            </div>
            Oops, no agents found :{"("}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {agents?.map((agent, index) => (
              <Card
                key={index}
                className="cursor-pointer bg-white shadow-sm"
                onClick={() => onSelectChat(agent)}
              >
                <CardHeader className="p-4">
                  <CardTitle className="flex items-center gap-5 text-lg font-medium">
                    <div
                      className="flex h-10 w-11 items-center justify-center rounded-full border text-lg"
                      style={{ backgroundColor: agent.avatar_color }}
                    >
                      <AppIcon icon={agent.agent_avatar} />
                    </div>
                    <div className="flex flex-col">
                      {agent.agent_name}
                      {agent.last_updated && (
                        <div className="flex items-center pt-1 text-sm font-[400]">
                          Last updated on{" "}
                          {formatDateToDayMonth(agent.last_updated)}
                        </div>
                      )}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex justify-end gap-3 pt-3">
                 
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader className="my-4 gap-2 px-3">
            <DialogTitle className="text-lg font-bold">
              Are you sure you want to delete?
            </DialogTitle>
            <DialogDescription className="font-[550]">
              This action cannot be undone.
            </DialogDescription>
            <div className="flex justify-end gap-2 pt-2">
              <Button variant="ghost" onClick={() => setDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ChatExplore;
