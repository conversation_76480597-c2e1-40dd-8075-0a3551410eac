/**
 * DEPRECATED: COMPONENT KEPT FOR LEGACY USE.
 */

// import { ArrowLeft, Plus, Share2 } from "lucide-react";
// import { Button } from "../../ui/button";
// import { useNavigate, useParams } from "react-router";
// import { useToast } from "../../../hooks/use-toast";
// import { useState } from "react";
// import TextReveal from "./TextReveal";

// interface ChatHeaderProps {
//   headerText: string;
//   showBackButton?: boolean;
//   onBack?: () => void;
//   sessionId: string;
//   setNewUser?: React.Dispatch<React.SetStateAction<boolean>>;
// }

// export default function ChatHeader({
//   headerText,
//   showBackButton = false,
//   onBack,
//   setNewUser,
// }: ChatHeaderProps) {
//   const [loading, setLoading] = useState(false);
//   const navigate = useNavigate();
//   const { toast } = useToast();
//   const params = useParams();

//   const newChatHandler = async () => {
//     setLoading(true);
//     if (setNewUser) setNewUser(true);
//     navigate(`/chat/${params.agentName}/${params.folderId}/${params.agentId}`);
//     setLoading(false);
//   };

//   const handleShareChat = () => {
//     try {
//       navigator.clipboard.writeText(location.href);
//       toast({
//         description: "Copied URL to clipboard!",
//       });
//     } catch {
//       toast({
//         description: "Failed to copy to clipboard.",
//         variant: "destructive",
//       });
//     }
//   };

//   return (
//     <header className="flex w-full items-center justify-between border-b bg-white p-4">
//       <div className="flex items-center gap-2">
//         {showBackButton && (
//           <Button variant="ghost" size="sm" onClick={onBack}>
//             <ArrowLeft className="mr-2" />
//           </Button>
//         )}
//         <TextReveal content={headerText} className="text-lg font-semibold" />
//       </div>
//       <div className="flex items-center gap-3">
//         <Button className="bg-primary" onClick={newChatHandler}>
//           <Plus />
//           {loading ? "Creating..." : "New Chat"}
//         </Button>
//         <Button
//           className="bg-black hover:bg-black/90"
//           onClick={handleShareChat}
//         >
//           <Share2 />
//           Share
//         </Button>
//       </div>
//     </header>
//   );
// }
