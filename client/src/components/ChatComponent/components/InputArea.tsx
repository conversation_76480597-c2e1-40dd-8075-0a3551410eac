import { ArrowUpCircleIcon, ChartColumnIncreasing } from "lucide-react";
import {
  useState,
  useEffect,
  useRef,
  KeyboardEvent,
  Dispatch,
  SetStateAction,
} from "react";
import {
  createNewChatSession,
  getPreviousMessages,
  queryChat,
  queryDataAnalysisChat,
} from "../../../api";
import ChatBubble, { Plot } from "./ChatBubble";
import { useKnowledgeBaseStore } from "../../../store/knowledgeBaseStore";
import { useLocation, useParams, useSearchParams } from "react-router";
import { ChatMessageList } from "../../ui/chat/chat-message-list";
import { ChatInput } from "../../ui/chat/chat-input";
import { useToast } from "../../../hooks/use-toast";
import { AxiosError } from "axios";
import { Skeleton } from "../../ui/skeleton";
import { TooltipProvider } from "../../ui/tooltip";
import { useUserStore } from "../../../store/userStore";
import { usePlotStore } from "../../../store/plotStore";
import { SidebarPlot } from "../components/SidebarPlot";
import { motion } from "framer-motion";
import { Badge } from "../../ui/badge";
import { cn } from "../../../lib/utils";

interface ChatMessage {
  type: "USER" | "SYSTEM" | "LOADING";
  content: string;
  plots: Plot[];
}

interface InputAreaProps {
  isNewUser: boolean;
  sessionId?: string;
  folderId?: string;
  setSessionId: Dispatch<SetStateAction<string>>;
  setChatName: Dispatch<SetStateAction<string>>;
  updateChatLocally: (id: string, name?: string) => void;
}

export default function InputArea({
  isNewUser,
  sessionId,
  folderId,
  setSessionId,
  setChatName,
  updateChatLocally,
}: InputAreaProps) {
  const [inputValue, setInputValue] = useState<string>("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isFetchingMessages, setIsFetchingMessages] = useState<boolean>(false);
  const [queryAgent, setQueryAgent] = useState<boolean>(true);

  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const params = useParams<{ agentId: string }>();
  const bottomRef = useRef<HTMLDivElement | null>(null);
  const location = useLocation();
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (s) => s.setActiveKnowledgeBase,
  );

  // Sidebar state from plot store
  const { isOpen, activePlot, setActivePlot, closeSidebar } = usePlotStore();

  const DATA_ANALYST_ENABLED =
    import.meta.env.VITE_DATA_ANALYST_ENABLED === "true";

  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, loading]);

  useEffect(() => {
    if (isNewUser && !sessionId) {
      setMessages([]);
    }
  }, [isNewUser, sessionId]);

  useEffect(() => {
    if (!sessionId || !folderId) return;

    setIsFetchingMessages(true);
    getPreviousMessages(params.agentId as string, sessionId)
      .then((r) => {
        if (Array.isArray(r) && r.length > 0) {
          const mapped: ChatMessage[] = r.map((m) => ({
            type: m.type as "USER" | "SYSTEM",
            content: m.content as string,
            plots: m.plots,
          }));
          setMessages(mapped);
        }
        if (r.knowledge_base_name) {
          setActiveKnowledgeBase(r.knowledge_base_name, location.pathname);
        }
      })
      .catch((e: AxiosError) => {
        toast({
          variant: "destructive",
          description: e?.message || "Failed to load messages",
        });
      })
      .finally(() => setIsFetchingMessages(false));
  }, [
    sessionId,
    folderId,
    params.agentId,
    location.pathname,
    setActiveKnowledgeBase,
    toast,
  ]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMsg: ChatMessage = {
      type: "USER",
      content: inputValue,
      plots: [],
    };
    setMessages((prev) => [...prev, userMsg]);
    setInputValue("");
    setLoading(true);

    try {
      let newSessionId = sessionId;
      if (!sessionId) {
        const session = await createNewChatSession(params.agentId as string);
        newSessionId = session.session_id;
        setSessionId(newSessionId as string);
        setSearchParams({ sessionId: newSessionId as string });

        // Immediately update the chat locally
        updateChatLocally(newSessionId as string, "New Chat");
      }
      let res;
      try {
        if (queryAgent) {
          res = await queryChat(
            inputValue,
            params.agentId as string,
            newSessionId as string,
          );
        } else {
          res = await queryDataAnalysisChat(
            inputValue,
            params.agentId as string,
            newSessionId as string,
          );
        }
      } catch {
        toast({
          variant: "destructive",
          description: res.detail,
        });
      }

      const systemMsg: ChatMessage = {
        type: "SYSTEM",
        content: res.content,
        plots: res.plot_urls || [],
      };

      // If response has plots, open the sidebar automatically
      if (systemMsg.plots && systemMsg.plots.length > 0) {
        setActivePlot(systemMsg.plots[0]); // Set the first plot as the active plot
      }

      setMessages((prev) => [...prev, systemMsg]);

      setChatName(res.chat_name);
      updateChatLocally(newSessionId as string, res.chat_name);
      useUserStore.getState().setTokenLeft(res.token_left);
    } catch (error) {
      toast({
        variant: "destructive",
        description:
          ((error as AxiosError)?.response?.data as string) ||
          "Failed to query chat",
      });
    } finally {
      setLoading(false);
    }
  };

  const onKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handlePlotClick = (plot: Plot) => {
    setActivePlot(plot); // Set clicked plot as active
  };

  if (isNewUser && messages.length === 0) {
    return (
      <div className="relative flex h-[calc(100vh-theme(space.16))] w-full flex-col items-center justify-center gap-10">
        <h2 className="pb-4 text-center text-3xl font-bold">Ready to chat</h2>
        <div className="w-full max-w-4xl">
          <div className="flex w-full flex-col items-center gap-4 rounded-xl bg-white p-4 shadow-[0_0_100px_rgba(0,0,0,0.1)]">
            <form className="relative w-full flex-1 rounded-xl bg-background p-1">
              <ChatInput
                placeholder="Ask me anything"
                value={inputValue}
                onKeyDown={onKeyDown}
                onChange={(e) => setInputValue(e.target.value)}
                className="mb-2 min-h-12 resize-none rounded-lg border-none bg-background p-3 shadow-none focus:ring-transparent focus-visible:border-none focus-visible:ring-transparent"
              />
              <div className="flex w-full flex-row-reverse items-center justify-between px-3">
                <ArrowUpCircleIcon
                  className={`h-6 w-6 ${
                    inputValue
                      ? "cursor-pointer text-blue-600"
                      : "cursor-default opacity-50"
                  }`}
                  onClick={handleSendMessage}
                />
                {DATA_ANALYST_ENABLED && (
                  <Badge
                    className={cn(
                      "duration-[0.3s] cursor-pointer gap-1 border-2 py-2 transition ease-in-out",
                      !queryAgent &&
                        "border-primary bg-[#4D91E13B] text-primary",
                    )}
                    onClick={() => setQueryAgent((value) => !value)}
                    variant="secondary"
                  >
                    <ChartColumnIncreasing
                      className={queryAgent ? "text-black" : "text-primary"}
                      size={14}
                    />
                    Data Analysis
                  </Badge>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex h-[calc(100vh-theme(space.16))] w-full flex-col">
      <div className="mx-auto flex h-full w-full max-w-4xl flex-col">
        {/* Content area that shrinks when sidebar opens */}
        <motion.div
          className={`scrollbar-hide flex-1 overflow-y-auto pb-4 transition-all duration-200`}
          initial={{ width: "100%" }}
          animate={{ width: isOpen ? "calc(100% - 300px)" : "100%" }}
        >
          {isFetchingMessages ? (
            <div className="mt-10 flex flex-col gap-3">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : (
            <div className="flex flex-col gap-1">
              <ChatMessageList className="scrollbar-hide">
                <TooltipProvider>
                  {messages.map((m, i) => (
                    <ChatBubble
                      key={i}
                      type={m.type}
                      content={m.content}
                      plots={m.plots}
                      onPlotClick={handlePlotClick} // Pass plot click handler to ChatBubble
                    />
                  ))}
                  {loading && <ChatBubble type="LOADING" content="" />}
                  <div ref={bottomRef} />
                </TooltipProvider>
              </ChatMessageList>
            </div>
          )}
        </motion.div>
        <motion.div
          className="w-full rounded-xl"
          initial={{ width: "100%" }}
          animate={{ width: isOpen ? "calc(100% - 300px)" : "100%" }}
        >
          <div className="sticky bottom-3 mb-2 rounded-xl">
            <div className="flex w-full flex-col items-center gap-4 shadow-[0_0_100px_rgba(0,0,0,0.1)]">
              <form
                className="relative w-full flex-1 rounded-xl bg-background p-4"
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSendMessage();
                }}
              >
                <ChatInput
                  placeholder="Ask me anything"
                  value={inputValue}
                  onKeyDown={onKeyDown}
                  onChange={(e) => setInputValue(e.target.value)}
                  disabled={loading}
                  className="mb-2 min-h-12 resize-none rounded-lg border-none bg-background p-3 shadow-none focus:ring-transparent focus-visible:border-none focus-visible:ring-transparent"
                />
                <div className="flex w-full flex-row-reverse items-center justify-between px-3">
                  <ArrowUpCircleIcon
                    className={`h-6 w-6 ${
                      inputValue
                        ? "cursor-pointer text-blue-600"
                        : "cursor-default opacity-50"
                    }`}
                    onClick={handleSendMessage}
                  />
                  {DATA_ANALYST_ENABLED && (
                    <Badge
                      className={cn(
                        "duration-[0.3s] cursor-pointer gap-1 border-2 py-2 transition ease-in-out",
                        !queryAgent &&
                          "border-primary bg-[#4D91E13B] text-primary",
                      )}
                      onClick={() => setQueryAgent((value) => !value)}
                      variant="secondary"
                    >
                      <ChartColumnIncreasing
                        className={queryAgent ? "text-black" : "text-primary"}
                        size={14}
                      />
                      Data Analysis
                    </Badge>
                  )}
                </div>
              </form>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Sidebar for Plot */}
      <SidebarPlot
        isOpen={isOpen}
        onClose={closeSidebar}
        activePlot={activePlot}
      />
    </div>
  );
}
