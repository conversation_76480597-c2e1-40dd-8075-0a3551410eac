import { X } from "lucide-react";
import { Plot } from "../components/ChatBubble";
import { Button } from "../../ui/button";

interface SidebarPlotProps {
  isOpen: boolean;
  onClose: () => void;
  activePlot: Plot | null;
}

export function SidebarPlot({ isOpen, onClose, activePlot }: SidebarPlotProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 z-50 flex h-full w-[400px] flex-col border-l border-border bg-background shadow-lg">
      <div className="flex items-center justify-between border-b p-4">
        <h3 className="text-lg font-semibold">
          {activePlot?.title || "Plot View"}
        </h3>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        <div className="flex h-full flex-col">
          {activePlot ? (
            <div className="flex flex-col gap-4">
              <img
                src={activePlot.url || "/placeholder.svg?height=400&width=400"}
                alt={activePlot.title}
                className="w-full rounded-md"
              />
            </div>
          ) : (
            <div className="flex h-full items-center justify-center">
              <p className="text-muted-foreground">No plot selected</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
