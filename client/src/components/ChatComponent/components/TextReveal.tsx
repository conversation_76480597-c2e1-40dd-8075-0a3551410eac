import { motion } from "motion/react";

const variants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
};

export default function TextReveal({
  content,
  className,
}: {
  content: string;
  className?: string;
}) {
  const splitContent = content.match(/\S+|\s/g)!;

  return (
    <motion.p
      initial="hidden"
      animate="visible"
      transition={{ staggerChildren: 0.02 }}
      className={className}
    >
      {splitContent.map((word, index) => (
        <motion.span
          key={index}
          transition={{ duration: 0.3 }}
          variants={variants}
        >
          {word}
        </motion.span>
      ))}
    </motion.p>
  );
}
