import { Check, Loader2, Plus } from "lucide-react";
import { Popover, PopoverTrigger, PopoverContent } from "../ui/popover";
import {
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  Command,
} from "../ui/command";
import { cn, PAGE_SIZE } from "../../lib/utils";
import { Button } from "../ui/button";
import { Link } from "react-router";
import { KnowledgeBase } from "../../store/knowledgeBaseStore";
import { useEffect, useState } from "react";
import { getFolders } from "../../api";

type SelectKBModalProps = {
  /**
   * The *currently* selected knowledge base, if any.
   */
  selectedValue?: Partial<KnowledgeBase>;

  /**
   * Callback invoked when the user picks a knowledge base from the list.
   */
  onSelect: (value: Partial<KnowledgeBase>, ...args: any[]) => void;
  /**
   * Whether the knowledge base is considered active (for styling).
   */
  activeKnowledgeBase?: boolean;
  /**
   * Fallback text on the button if no `selectedValue`.
   */
  defaultText?: string;
  className?: string;
  iconReverse?: boolean;
  selectedValueId?: string;
};

export function SelectKBModal({
  selectedValue,
  onSelect,
  activeKnowledgeBase,
  defaultText,
  className,
  iconReverse,
  selectedValueId,
}: SelectKBModalProps) {
  const [open, setOpen] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [folders, setFolders] = useState<KnowledgeBase[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchFolders = async () => {
    setIsFetching(true);
    try {
      const data = await getFolders({ page: 1, page_size: PAGE_SIZE });
      setFolders(data.results);
    } catch (err) {
      setError((err as Error).message);
      console.error("Failed to fetch folders:", err);
    } finally {
      setIsFetching(false);
    }
  };

  useEffect(() => {
    fetchFolders();
  }, []);

  const filteredFolder = folders.filter(
    (folder) => folder.id === selectedValueId,
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between py-5",
            activeKnowledgeBase &&
              "bg-primary text-white hover:bg-primary/80 hover:text-white",
          )}
        >
          {iconReverse && <Plus />}
          {isFetching && <Loader2 className="animate-spin" />}
          {selectedValue?.name || filteredFolder.length !== 0
            ? selectedValue?.name || filteredFolder[0].name
            : defaultText || (
                <span className="text-black/60">Select Knowledge Base</span>
              )}
          {!iconReverse && <Plus />}
        </Button>
      </PopoverTrigger>

      <PopoverContent
        className={cn("min-w-[15rem] max-w-[27rem] p-0", className)}
        side="top"
        asChild
        avoidCollisions
      >
        <Command className="p-4">
          <CommandInput
            placeholder="Search Knowledge Base"
            className="bg-[#F5F5F7]"
          />
          <CommandList>
            <CommandEmpty>No knowledge base found.</CommandEmpty>
            <CommandGroup>
              <Link to={"/knowledgeBase"}>
                <CommandItem className="cursor-pointer py-3 text-primary data-[selected='true']:bg-white data-[selected='true']:text-primary">
                  Create New Knowledge Base <Plus />
                </CommandItem>
              </Link>

              {folders.map((kb) => (
                <CommandItem
                  key={kb.id}
                  value={kb.name}
                  onSelect={() => {
                    onSelect(kb);
                    setOpen(false);
                  }}
                  className="cursor-pointer py-3"
                >
                  {kb.name}
                  <Check
                    className={cn(
                      "ml-auto",
                      selectedValue?.name === kb.name
                        ? "opacity-100"
                        : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
