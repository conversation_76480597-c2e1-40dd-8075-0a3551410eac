import { Database, Loader2, Plus } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "../ui/dialog";
import { Separator } from "../ui/separator";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useState } from "react";
import { Textarea } from "../ui/textarea";
import { SelectKBModal } from "./SelectKBModal";
import { useLocation, useNavigate } from "react-router";
import {
  KnowledgeBase,
  useKnowledgeBaseStore,
} from "../../store/knowledgeBaseStore";
import { useToast } from "../../hooks/use-toast";
import { createAgent, createNewChatSession } from "../../api";
import { statusCodes } from "../../api/constants";
import { useAgentStore } from "../../store/agentStore";
import AppIcon from "../EmojiPicker/AppIcon";
import EmojiPicker from "../EmojiPicker";

type CreateChatDialogProps = {
  activeKnowledgeBase: boolean;
  triggerText: string;
  disabled?: boolean;
};

const CreateChatDialog = ({
  activeKnowledgeBase,
  triggerText,
  disabled,
}: CreateChatDialogProps) => {
  const [chatbotName, setChatbotName] = useState<string>("");
  const [instructions, setInstructions] = useState<string>("");
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] =
    useState<Partial<KnowledgeBase>>({
      id: "81fde902-6ef8-4d84-9303-dfaf9505e1f5",
      name: "Default folder",
      file_count: 2,
    });
  const [loading, setLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<boolean>(false);
  const [chatbotIcon, setChatbotIcon] = useState<string>(":robot_face:");
  const [chatBotIconColor, setChatBotIconColor] = useState<string>("#D5D9EB");
  const location = useLocation();
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (state) => state.setActiveKnowledgeBase,
  );
  const setActiveAgent = useAgentStore((state) => state.setActiveAgent);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleEmojiSelect = (emoji: string, background: string): void => {
    setChatbotIcon(emoji);
    setChatBotIconColor(background);
    setShowEmojiPicker(false);
  };

  const handleCreateChatbot = async (): Promise<void> => {
    setLoading(true);
    if (!chatbotName || !instructions || !selectedKnowledgeBase || !chatbotIcon) {
      const unfilledFields = [];

      if (!chatbotName) unfilledFields.push("Agent Name");
      if (!instructions) unfilledFields.push("Instructions");
      if (!selectedKnowledgeBase) unfilledFields.push("Knowledge Base");
      if (!chatbotIcon) unfilledFields.push("Agent Icon");

      toast({
        description: `Please fill in all fields: ${unfilledFields.join(", ")}`,
        variant: "destructive",
      });
      setLoading(false);

      return;
    }

    setActiveKnowledgeBase(
      selectedKnowledgeBase as KnowledgeBase,
      location.pathname,
    );
    try {
      const createAgentResponse = await createAgent(
        selectedKnowledgeBase.id as string,
        chatbotName,
        chatbotIcon,
        chatBotIconColor,
        undefined,
        instructions,
      );
      if (createAgentResponse.status !== statusCodes.SUCCESS) {
        toast({
          description: "Failed to create agent",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }
      const result = await createNewChatSession(
        createAgentResponse.data.id as string,
      );
      if (result) {
        toast({
          description: "Agent created successfully",
        });
        setActiveAgent({
          agent_name: chatbotName,
          folder_id: selectedKnowledgeBase.id,
        });
        navigate(
          `/chat/${createAgentResponse.data.agent_name}/${createAgentResponse.data.folder_id}/${createAgentResponse.data.id}?sessionId=${result.session_id}`,
        );
      } else {
        toast({
          description: "Failed to create agent",
          variant: "destructive",
        });
      }
    } catch {
      toast({
        description: "An error occurred while creating the agent",
        variant: "destructive",
      });
      return;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger className="w-full" asChild>
        <Button className="w-full" disabled={disabled}>
          {activeKnowledgeBase ? <Database /> : <Plus />}
          <span className="text-md">{triggerText}</span>
          {activeKnowledgeBase && <Plus className="ml-auto" />}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[30rem] rounded-lg">
        <DialogHeader>
          <DialogTitle>Create Agent</DialogTitle>
        </DialogHeader>
        <Separator className="my-2" />
        <div className="space-y-4">
          <div>
            <label className="mt-2 block text-sm font-medium text-gray-700">
              Agent icon and name
            </label>
            <div className="mt-4 flex items-center gap-3">
              <button
                className="flex h-10 w-11 items-center justify-center rounded-full border text-lg"
                style={{ backgroundColor: chatBotIconColor }}
                onClick={() => setShowEmojiPicker(true)}
                aria-label="Select emoji"
              >
                <AppIcon icon={chatbotIcon} />
              </button>
              <Input
                type="text"
                placeholder="Agent Name"
                value={chatbotName}
                required
                onChange={(e) => setChatbotName(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm"
              />
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Instructions
            </label>
            <Textarea
              rows={6}
              placeholder="Instructions"
              value={instructions}
              required
              onChange={(e) => setInstructions(e.target.value)}
            />
          </div>
          {/* <div>
            <label className="block text-sm font-medium text-gray-700">
              Select Knowledge Base
            </label>
            <div className="my-3">
              <SelectKBModal
                selectedValue={selectedKnowledgeBase}
                onSelect={(value: Partial<KnowledgeBase>) =>
                  setSelectedKnowledgeBase(value)
                }
                className="w-[27rem]"
              />
            </div>
          </div> */}
        </div>
        <div className="w-full gap-x-2">
          <Button
            onClick={handleCreateChatbot}
            className="w-full"
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="animate-spin" />
            ) : (
              <>
                <Plus /> Create Agent
              </>
            )}
          </Button>
        </div>
        {showEmojiPicker && (
          <EmojiPicker
            open={showEmojiPicker}
            onClose={() => setShowEmojiPicker(false)}
            onSelect={(emoji, background) => {
              handleEmojiSelect(emoji, background);
            }}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateChatDialog;
