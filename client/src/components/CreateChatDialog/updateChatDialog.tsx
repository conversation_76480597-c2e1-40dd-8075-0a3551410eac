import { Loader2, Plus, Settings } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "../ui/dialog";
import { Separator } from "../ui/separator";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { useEffect, useState } from "react";
import { Textarea } from "../ui/textarea";
import { SelectKBModal } from "./SelectKBModal";
import { useLocation, useNavigate } from "react-router";
import {
  KnowledgeBase,
  useKnowledgeBaseStore,
} from "../../store/knowledgeBaseStore";
import { useToast } from "../../hooks/use-toast";
import { getChatBotConfig, updateChatBot } from "../../api";
import { statusCodes } from "../../api/constants";
import AppIcon from "../EmojiPicker/AppIcon";
import EmojiPicker from "../EmojiPicker";

type UpdateChatDialogProps = {
  agentId: string;
  folderId: string;
};

const UpdateChatDialog = ({ agentId, folderId }: UpdateChatDialogProps) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [chatbotName, setChatbotName] = useState<string>("");
  const [instructions, setInstructions] = useState<string>("");
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] =
    useState<Partial<KnowledgeBase>>();
  const [loading, setLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<boolean>(false);
  const [chatbotIcon, setChatbotIcon] = useState<string>(":robot_face:");
  const [chatBotIconColor, setChatBotIconColor] = useState<string>("#D5D9EB");
  const location = useLocation();
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (state) => state.setActiveKnowledgeBase,
  );
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleEmojiSelect = (emoji: string, background: string): void => {
    setChatbotIcon(emoji);
    setChatBotIconColor(background);
    setShowEmojiPicker(false);
  };

  const getAgentData = async (agentId: string) => {
    const result = await getChatBotConfig(agentId);
    setChatbotName(result.data.agent_name);
    setInstructions(result.data.instructions || "");
    setChatbotIcon(result.data.agent_avatar);
    setChatBotIconColor(result.data.avatar_color);
  };

  useEffect(() => {
    getAgentData(agentId);
  }, []);

  const handleUpdateAgent = async (): Promise<void> => {
    setLoading(true);

    setActiveKnowledgeBase(
      selectedKnowledgeBase as KnowledgeBase,
      location.pathname,
    );
    try {
      const formData = new FormData();
      formData.append("agent_name", chatbotName);
      formData.append("agent_avatar", chatbotIcon);
      formData.append("avatar_color", chatBotIconColor);
      formData.append("instructions", instructions);
      if (selectedKnowledgeBase) {
        formData.append("folder_id", selectedKnowledgeBase.id as string);
      }
      const updateAgentResponse = await updateChatBot(agentId, formData);
      if (updateAgentResponse.status !== statusCodes.SUCCESS) {
        toast({
          description: "Failed to update agent",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      toast({
        description: "Agent updated successfully",
      });
    } catch {
      toast({
        description: "An error occurred while updating the agent",
        variant: "destructive",
      });
      return;
    } finally {
      setLoading(false);
      setDialogOpen(false);
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Settings strokeWidth={1.5} className="cursor-pointer" />
      </DialogTrigger>
      <DialogContent className="max-w-[30rem] rounded-lg">
        <DialogHeader>
          <DialogTitle>Update Agent</DialogTitle>
        </DialogHeader>
        <Separator className="my-2" />
        <div className="space-y-4">
          <div>
            <label className="mt-2 block text-sm font-medium text-gray-700">
              Agent icon and name
            </label>
            <div className="mt-4 flex items-center gap-3">
              <button
                className="flex h-10 w-11 items-center justify-center rounded-full border text-lg"
                style={{ backgroundColor: chatBotIconColor }}
                onClick={() => setShowEmojiPicker(true)}
                aria-label="Select emoji"
              >
                <AppIcon icon={chatbotIcon} />
              </button>
              <Input
                type="text"
                placeholder="Agent Name"
                value={chatbotName}
                required
                onChange={(e) => setChatbotName(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm"
              />
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Instructions
            </label>
            <Textarea
              rows={6}
              placeholder="Instructions"
              value={instructions}
              required
              onChange={(e) => setInstructions(e.target.value)}
            />
          </div>
          {/*<div>
            <label className="block text-sm font-medium text-gray-700">
              Select Knowledge Base
            </label>
            <div className="my-3">
              <SelectKBModal
                selectedValueId={folderId}
                selectedValue={selectedKnowledgeBase}
                onSelect={(value: Partial<KnowledgeBase>) =>
                  setSelectedKnowledgeBase(value)
                }
                className="w-[27rem]"
              />
            </div>
          </div>*/}
        </div>
        <div className="w-full gap-x-2">
            <Button
              onClick={handleUpdateAgent}
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="animate-spin" />
              ) : (
                <>
                  <Plus /> Update Agent
                </>
              )}
            </Button>
        </div>
        {showEmojiPicker && (
          <EmojiPicker
            open={showEmojiPicker}
            onClose={() => setShowEmojiPicker(false)}
            onSelect={(emoji, background) => {
              handleEmojiSelect(emoji, background);
            }}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UpdateChatDialog;
