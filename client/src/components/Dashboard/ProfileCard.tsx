import { useState } from "react";
import DeleteAccountDialog from "../DeleteAccountDialog";
import { PasswordInput } from "../PasswordInputComponent";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { cn } from "../../lib/utils";
import { updatePassword } from "../../api";
import { useToast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function ProfileCard({ user }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
    validatePasswords(value, confirmPassword);
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const value = e.target.value;
    setConfirmPassword(value);
    validatePasswords(password, value);
  };

  const validatePasswords = (pass: string, confirmPass: string) => {
    if (!pass || !confirmPass) {
      setIsButtonDisabled(true);
    } else if (pass !== confirmPass) {
      setIsButtonDisabled(true);
    } else {
      setIsButtonDisabled(false);
    }
  };

  function handleDialogClose() {
    setIsDialogOpen(false);
  }

  function handleDialogOpen() {
    setIsDialogOpen(true);
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (password !== confirmPassword) {
      toast({
        variant: "destructive",
        description: "Password do not match",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await updatePassword(password);
      toast({ description: "Password updated successfully" });
      setPassword("");
      setConfirmPassword("");
    } catch (err: any) {
      toast({
        variant: "destructive",
        description:
          err.response?.data?.message ||
          "Failed to reset password. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <article className="mx-auto flex w-full max-w-screen-md flex-col gap-8 rounded-[30px] border border-[#E4E8EE] bg-background p-10 shadow">
      <div className="flex items-center gap-5 border-b border-b-[#D9D9D980] pb-8">
        <Avatar>
          <AvatarImage src={user.profile_image} />
          <AvatarFallback className="bg-primary font-medium text-white">
            {user.username[0].toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <div className="flex gap-3">
            <span className="text-lg font-medium">{user.username}</span>
            <span className="rounded-[8px] bg-[#EFF6FF] px-2.5 py-1 text-sm text-primary">
              {user.is_paid ? "Premium" : "Free"} Plan
            </span>
          </div>
          <span className="text-sm font-normal opacity-50">{user.email}</span>
        </div>
      </div>

      <form
        className="flex w-full flex-col gap-7 border-b border-b-[#D9D9D980] pb-8"
        onSubmit={handleSubmit}
      >
        <div className="flex w-full items-center gap-10">
          <div className="flex w-full flex-col gap-1">
            <label className="text-base font-medium">Password</label>
            <PasswordInput
              required
              value={password}
              onChange={handlePasswordChange}
              className={cn(
                "rounded border-2 px-3 py-2 focus:border-none focus:outline-none focus:ring-0",
                password &&
                  confirmPassword &&
                  password === confirmPassword &&
                  "border-green-500 focus:ring-green-500",
                password &&
                  confirmPassword &&
                  password !== confirmPassword &&
                  "border-red-500 focus:ring-red-500",
              )}
              placeholder="Password"
              minLength={6}
            />
          </div>
          <div className="flex w-full flex-col gap-1">
            <label className="text-base font-medium">Confirm Password</label>
            <PasswordInput
              required
              value={confirmPassword}
              onChange={handleConfirmPasswordChange}
              className={cn(
                "rounded border px-3 py-2 focus:border-none focus:outline-none focus:ring-0",
                password &&
                  confirmPassword &&
                  password === confirmPassword &&
                  "border-green-500 focus:ring-green-500",
                password &&
                  confirmPassword &&
                  password !== confirmPassword &&
                  "border-red-500 focus:ring-red-500",
              )}
              minLength={6}
              placeholder="Confirm Password"
            />
          </div>
        </div>
        <button
          disabled={isSubmitting || isButtonDisabled}
          className={cn(
            "flex flex-1 cursor-pointer items-center justify-center gap-2 rounded-[8px] bg-primary py-3 text-primary-foreground hover:bg-primary/90",
            isButtonDisabled && "cursor-not-allowed",
          )}
        >
          {isSubmitting ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Change Password
        </button>
      </form>
      <button
        className="w-fit font-medium text-destructive/80 hover:text-destructive"
        disabled={false}
        onClick={handleDialogOpen}
      >
        Delete Account
      </button>

      <DeleteAccountDialog
        isOpen={isDialogOpen}
        onClose={handleDialogClose}
        onDelete={() => {}}
      />
    </article>
  );
}
