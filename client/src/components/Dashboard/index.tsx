import { CircleUserRound } from "lucide-react";
import { Skeleton } from "../ui/skeleton";
import { useEffect } from "react";
import ProfileCard from "./ProfileCard";
import { useUserStore } from "../../store/userStore";

export default function Dashboard() {
  const { user, isLoading, fetchUser } = useUserStore();

  useEffect(() => {
    if (!user?.username && !isLoading) {
      fetchUser();
    }
  }, [user?.username, isLoading, fetchUser]);

  if (isLoading || !user) {
    return (
      <section className="gap-30 mt-8 flex min-h-screen flex-1 flex-col px-20">
        <Skeleton className="h-9 w-full" />
        <Skeleton className="h-32 w-full" />
      </section>
    );
  }

  return (
    <section className="mx-auto mt-8 flex max-w-screen-xl flex-1 flex-col gap-12 px-20">
      <header className="flex items-center gap-3.5 border-b border-b-[#0000001A] pb-4">
        <CircleUserRound size={18} />
        <h1 className="text-2xl font-semibold text-accent-foreground">
          Profile Settings
        </h1>
      </header>
      <ProfileCard user={user} />
    </section>
  );
}
