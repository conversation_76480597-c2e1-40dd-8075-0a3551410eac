import React, { useState } from "react";
import { PasswordInput } from "../PasswordInputComponent";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Button } from "../ui/button";
import { deleteUser } from "../../api";
import { useAuthStore } from "../../store/authStore";
import { useNavigate } from "react-router";
import { useToast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";

interface DeleteAccountDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
}

const DeleteAccountDialog: React.FC<DeleteAccountDialogProps> = ({
  isOpen,
  onClose,
  onDelete,
}) => {
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const navigate = useNavigate();
  const logout = useAuthStore((state) => state.logout);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      await deleteUser(password);
      setIsSuccess(true);
      logout();
      navigate("/signup", { replace: true });
      toast({ description: "Account deleted successfully" });
    } catch (err: any) {
      setError(
        err.response?.data?.message ||
          "Failed to delete account. Please try again.",
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="flex max-w-[400px] flex-col gap-5 p-7 sm:rounded-[25px]">
        <DialogHeader className="flex flex-col gap-3">
          <DialogTitle className="text-xxl max-w-[70%] font-semibold leading-tight">
            Are you sure you want to{" "}
            <span className="text-red-500">delete your account?</span>
          </DialogTitle>
          <p className="text-sm text-gray-600">
            Deleting your account will remove all of your information from our
            database. This cannot be undone.
          </p>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-3.5">
          <div className="space-y-2">
            <span className="text-sm font-medium">
              Type your password to delete your account
            </span>
            <PasswordInput
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full pr-10"
              placeholder="Enter new password"
              minLength={6}
              autoComplete="new-password"
            />
            <AnimatePresence>
              {error && (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-sm text-red-500"
                >
                  {error}
                </motion.p>
              )}
            </AnimatePresence>
          </div>

          <div className="space-y-3.5 pt-2">
            <Button
              variant="destructive"
              className="w-full bg-red-500 hover:bg-red-600"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Delete Account
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full border-primary text-primary"
              onClick={onClose}
            >
              Go Back
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteAccountDialog;
