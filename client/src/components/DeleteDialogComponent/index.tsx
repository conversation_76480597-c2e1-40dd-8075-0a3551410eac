import { TrashIcon } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import {
  DialogHeader,
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "../ui/dialog";
import { cn } from "../../lib/utils";

type DeleteDialogProps = {
  descriptionText?: string;
  onDelete: (e: React.SyntheticEvent) => void;
  deleteInProgress: boolean;
  deleteText?: string;
  className?: string;
};

const DeleteDialog = ({
  descriptionText = "",
  onDelete,
  deleteInProgress,
  deleteText = "",
  className,
}: DeleteDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn("text-gray-500 hover:text-red-600", className)}
        >
          <TrashIcon className="h-4 w-4 text-black/70" />
          {deleteText}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader className="my-4 gap-2 px-3">
          <DialogTitle className="text-lg font-bold">
            Are you sure you want to delete?
          </DialogTitle>
          <DialogDescription className="font-[550]">
            {descriptionText}
          </DialogDescription>
          <div className="flex justify-end gap-2 pt-2">
            <DialogClose asChild>
              <Button variant="ghost">Cancel</Button>
            </DialogClose>
            <DialogClose asChild>
              <Button variant="destructive" onClick={onDelete}>
                {deleteInProgress ? "Deleting..." : "Delete"}
              </Button>
            </DialogClose>
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteDialog;
