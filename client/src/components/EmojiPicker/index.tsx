import type { Emoji, EmojiMartData } from "@emoji-mart/data";
import data from "@emoji-mart/data";
import {
  <PERSON><PERSON>,
  Di<PERSON>Content,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Search } from "lucide-react";
import type { ChangeEvent } from "react";
import { useState } from "react";
import { Button } from "../ui/button.tsx";
import { Input } from "../ui/input.tsx";

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      "em-emoji": React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & { id: string; fallback?: string; shortcodes?: string };
    }
  }
}

import { SearchIndex, init } from "emoji-mart";
import { cn } from "../../lib/utils.ts";
init({ data });

async function search(value: string) {
  const emojis: Emoji[] = (await SearchIndex.search(value)) || [];
  return emojis.map((emoji) => emoji.skins[0].native);
}

const backgroundColors = [
  "#FFEAD5",
  "#E4FBCC",
  "#D3F8DF",
  "#E0F2FE",
  "#E0EAFF",
  "#EFF1F5",
  "#FBE8FF",
  "#FCE7F6",
  "#FEF7C3",
  "#E6F4D7",
  "#D5F5F6",
  "#D1E9FF",
  "#D1E0FF",
  "#D5D9EB",
  "#ECE9FE",
  "#FFE4E8",
];

const EmojiPicker = ({
  open,
  onClose,
  onSelect,
}: {
  open: boolean;
  onClose: () => void;
  onSelect: (emoji: string, background: string) => void;
}) => {
  const { categories } = data as EmojiMartData;
  const [selectedEmoji, setSelectedEmoji] = useState("");
  const [selectedBackground, setSelectedBackground] = useState(
    backgroundColors[0],
  );
  const [searchedEmojis, setSearchedEmojis] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const searchEmojis = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.value === "") {
      setIsSearching(false);
    } else {
      setIsSearching(true);
      const emojis = await search(e.target.value);
      setSearchedEmojis(emojis);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="w-[362px] rounded-lg bg-background">
        <DialogHeader>
          <DialogTitle>Pick an Emoji</DialogTitle>
        </DialogHeader>

        <div className="p-3">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 flex items-center pl-3">
              <Search className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <Input
              type="search"
              className="rounded-lg bg-gray-100 px-3 pl-10 text-sm font-normal"
              placeholder="Search emojis..."
              onChange={searchEmojis}
            />
          </div>
        </div>
        <div className="divider m-0 mb-3 h-[2px]" />
        <div className="max-h-[200px] w-full overflow-y-auto overflow-x-hidden px-3">
          {isSearching && (
            <div key="category-search" className="flex flex-col">
              <p className="mb-1 text-xs font-medium uppercase text-[#101828]">
                Search
              </p>
              <div className="grid h-full w-full grid-cols-8 gap-1">
                {searchedEmojis.map((emoji: string, index: number) => (
                  <div
                    key={`emoji-search-${index}`}
                    className="inline-flex h-10 w-10 items-center justify-center rounded-lg"
                    onClick={() => setSelectedEmoji(emoji)}
                  >
                    <div className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg p-1 ring-gray-300 ring-offset-1 hover:ring-1">
                      <em-emoji id={emoji} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {categories.map((category, index: number) => (
            <div key={`category-${index}`} className="flex flex-col">
              <p className="mb-1 text-xs font-medium uppercase text-[#101828]">
                {category.id}
              </p>
              <div className="grid h-full w-full grid-cols-8 gap-1">
                {category.emojis.map((emoji, index: number) => (
                  <div
                    key={`emoji-${index}`}
                    className="inline-flex h-10 w-10 items-center justify-center rounded-lg"
                    onClick={() => setSelectedEmoji(emoji)}
                  >
                    <div className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg p-1 ring-gray-300 ring-offset-1 hover:ring-1">
                      <em-emoji id={emoji} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        <div className={cn("p-3", selectedEmoji === "" ? "opacity-25" : "")}>
          <p className="mb-2 text-xs font-medium uppercase text-[#101828]">
            Choose Style
          </p>
          <div className="grid h-full w-full grid-cols-8 gap-1">
            {backgroundColors.map((color) => (
              <div
                key={color}
                className={cn(
                  "cursor-pointer",
                  "ring-offset-1 hover:ring-1",
                  "inline-flex h-10 w-10 items-center justify-center rounded-lg",
                  color === selectedBackground ? "ring-1 ring-gray-300" : "",
                )}
                onClick={() => setSelectedBackground(color)}
              >
                <div
                  className="flex h-8 w-8 items-center justify-center rounded-lg p-1"
                  style={{ background: color }}
                >
                  {selectedEmoji !== "" && <em-emoji id={selectedEmoji} />}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="divider m-0 h-[2px]" />
        <DialogFooter className="flex items-center justify-center gap-x-2 p-3">
          <Button className="w-full" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button
            disabled={selectedEmoji === ""}
            className="w-full"
            onClick={() => onSelect(selectedEmoji, selectedBackground)}
          >
            Ok
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EmojiPicker;
