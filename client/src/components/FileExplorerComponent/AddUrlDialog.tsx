import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogTitle,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Plus, X } from "lucide-react";

interface AddUrlDialogProps {
  isOpen: boolean;
  onClose: (open: boolean) => void;
  onSubmit: (urlData: {
    url: string;
    crawlerType: string;
    maxRequests: number;
    crawlLinks: boolean;
    enqueueStrategy: string;
  }) => void;
}

export function AddUrlDialog({ isOpen, onClose, onSubmit }: AddUrlDialogProps) {
  const [urls, setUrls] = useState<string[]>([""]);
  // Default values that will be used but not shown in UI
  const crawlLinks = false;
  const crawlerType = "cheerio";
  const maxRequests = 50;
  const enqueueStrategy = "same-domain";

  const addUrlField = () => {
    setUrls([...urls, ""]);
  };

  const removeUrlField = (index: number) => {
    const newUrls = urls.filter((_, i) => i !== index);
    setUrls(newUrls);
  };

  const updateUrl = (index: number, value: string) => {
    const newUrls = [...urls];
    newUrls[index] = value;
    setUrls(newUrls);
  };

  const handleSubmit = () => {
    // Filter out empty URLs and submit each valid URL
    const validUrls = urls.filter(url => url.trim() !== "");
    validUrls.forEach(url => {
      onSubmit({
        url,
        crawlerType,
        maxRequests,
        crawlLinks,
        enqueueStrategy,
      });
    });
    onClose(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Website URL</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {urls.map((url, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>URL {index + 1}</Label>
                {urls.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => removeUrlField(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <Input
                type="url"
                placeholder="Enter URL"
                value={url}
                onChange={(e) => updateUrl(index, e.target.value)}
              />
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            className="mt-2"
            onClick={addUrlField}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Another URL
          </Button>

          {/* Removed the crawl links switch component */}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Add URLs</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}