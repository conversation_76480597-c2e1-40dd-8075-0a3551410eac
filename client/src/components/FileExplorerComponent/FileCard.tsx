import { <PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>2 } from "lucide-react";
import { Card, CardDescription, CardTitle } from "../ui/card";
import DeleteDialog from "../DeleteDialogComponent";
import { FileCardProps, Status } from ".";
import { cn, formatBytes } from "../../lib/utils";
import GoogleDrive<PERSON>ogo from "../../assets/googleDriveLogo.svg";
import { Badge } from "../ui/badge";
import { useState } from "react";
import { getPresignedURL } from "../../api";
import PdfPreview from "../ui/file/pdf-preview";

const badgeStyles: Record<Status, string> = {
  Success: "bg-[#0FA958]/20 text-[#0FA958] border-[#0FA958] border-2",
  Failed: "bg-[#FF3C3C]/20 text-[#FF3C3C] border-[#FF3C3C] border-2",
  Processing: "bg-[#3C87F]/30 text-[#3C87F] border-[#3C87F] border-2",
  Queue: "bg-[#FF9620]/20 text-[#FF9620] border-[#FF9620] border-2",
};

export default function FileCard({
  fileName,
  fileSize,
  externalLink,
  type,
  onDeleteFile,
  deleteInProgress,
  id,
  status,
  folderId,
  url,
}: FileCardProps & { folderId: string }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [presignedURL, setPresignedURL] = useState<string | null>(null);

  const handleClick = async () => {
    // For URL type, open the URL in a new tab
    if (type === "url" && url) {
      window.open(url, "_blank", "noopener,noreferrer");
      return;
    }

    // Retrieve stored data from localStorage
    const storedData =
      JSON.parse(localStorage.getItem("presignedURLs") as string) || {};
    const fileData = storedData[id];

    // Check if the presigned URL is already in localStorage
    if (fileData) {
      setPresignedURL(fileData); // Use the cached URL
      setIsDialogOpen(true); // Open dialog with the existing URL
      return;
    }

    setLoading(true);
    setError(null);
    setIsDialogOpen(true);

    try {
      // Fetch a new presigned URL
      const response = await getPresignedURL(folderId, id);
      const newPresignedURL = response.data?.url;

      // Store the URL in localStorage
      storedData[id] = newPresignedURL;
      localStorage.setItem("presignedURLs", JSON.stringify(storedData));

      // Schedule removal after 1800 seconds (30 minutes)
      setTimeout(() => {
        const updatedData =
          JSON.parse(localStorage.getItem("presignedURLs") as string) || {};
        delete updatedData[id];
        localStorage.setItem("presignedURLs", JSON.stringify(updatedData));
      }, 1800 * 1000);

      // Set the state with the new URL
      setPresignedURL(newPresignedURL);
    } catch (err) {
      setError("Failed to fetch the presigned URL. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Card className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-4">
          <div className="text-blue-500">
            {type === "url" ? (
              <Link2 size={32} />
            ) : type === "DRIVE" ? (
              <img
                src={GoogleDriveLogo}
                alt="Google Drive"
                className="h-8 w-8"
              />
            ) : (
              <LucideFileText size={32} />
            )}
          </div>

          <div>
            <CardTitle className="flex items-center gap-2 text-sm font-medium">
              {fileName}
              {type === "DRIVE" && (
                <img src={GoogleDriveLogo} alt="Google Drive" />
              )}
            </CardTitle>
            <CardDescription className="text-xs text-gray-500">
              {type === "url" ? "Web URL" : formatBytes(parseInt(fileSize))}
            </CardDescription>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {type !== "text/csv" && (
            <button
              onClick={handleClick}
              disabled={loading}
              title={type === "url" ? "Open URL in new tab" : "Preview file"}
            >
              <Eye size={18} />
            </button>
          )}
          <Badge variant="secondary" className={cn("", badgeStyles[status])}>
            {status}
          </Badge>
          {status === "Queue" && (
            <Loader2 className="animate-spin text-[#FF9620]" />
          )}
          {status === "Processing" && (
            <Loader2 className="animate-spin text-primary" />
          )}
          {status === "Failed" && <RotateCcw className="text-destructive" />}
          {status === "Success" && (
            <DeleteDialog
              deleteInProgress={deleteInProgress}
              onDelete={async () => await onDeleteFile(id)}
            />
          )}
        </div>
      </Card>
      <PdfPreview
        source={{ file: presignedURL, fileName }}
        isDialogOpen={isDialogOpen}
        setIsDialogOpen={setIsDialogOpen}
        loading={loading}
        error={error}
      />
    </>
  );
}
