import { Plus } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import {
  Di<PERSON>Header,
  DialogFooter,
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
} from "../ui/dialog";
import { Input } from "../ui/input";

function GoogleDriveDialog({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: (open: boolean) => void;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Google Drive Link</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Input
            type="text"
            placeholder="Drive link here"
            className="col-span-3 text-primary"
          />
        </div>
        <DialogFooter>
          <Button variant="ghost" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button type="submit">
            <Plus />
            Add
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default GoogleDriveDialog;
