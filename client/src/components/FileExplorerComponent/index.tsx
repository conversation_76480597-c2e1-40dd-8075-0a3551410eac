import { FileText, LucideSearch } from "lucide-react";
import { Input } from "../ui/input";
import { FileType } from "../../pages/KnowledgeBase/knowledgeBase";
import { Skeleton } from "../ui/skeleton";
import { useCallback, useEffect, useRef, useState } from "react";
import FileCard from "./FileCard";
import { deleteFile, getNotDeletedFiles } from "../../api";
import { useToast } from "../../hooks/use-toast";
import { useDebouncedCallback } from "use-debounce";
import { cn, PAGE_SIZE } from "../../lib/utils";
import PaginationComponent from "../KnowledgeBaseExplorerComponent/Pagination";
import { getStatus } from "../../api";

type FileExplorerProps = {
  initialFiles: FileType[];
  onDeleteFile: (fileId: string) => Promise<void>;
  deleteInProgress: boolean;
  fetchingFiles: boolean;
  folderId: string;
  totalPages: number;
  setTotalPages: (totalPages: number) => void;
};

export type Status = "Processing" | "Queue" | "Failed" | "Success";

export type FileCardProps = {
  fileName: string;
  fileSize: string;
  externalLink?: string;
  type: "DRIVE" | "PDF" | "url" | "text/csv";
  onDeleteFile: (fileId: string) => Promise<void>;
  deleteInProgress: boolean;
  id: string;
  status: Status;
  url?: string;
};

const FileExplorer = ({
  folderId,
  initialFiles,
  totalPages,
  setTotalPages,
}: FileExplorerProps) => {
  const [files, setFiles] = useState(initialFiles);
  const [query, setQuery] = useState("");

  useEffect(() => {
    const interval = setInterval(async () => {
      const updatedFiles = await Promise.all(
        files.map(async (file) => {
          try {
            if (file.status === "Success") return file;

            const updatedFile = await getStatus(file.folderid, file.id); // Call the API for each file
            return { ...file, status: updatedFile.status }; // Update the file status
          } catch (error) {
            console.error(`Error fetching file ${file.id}:`, error);
            return file; // Return the original file if there's an error
          }
        }),
      );
      setFiles(updatedFiles);
    }, 15000);

    return () => clearInterval(interval); // Cleanup on component unmount
  }, [files]);

  const [isFetching, setIsFetching] = useState(false);
  const [deleteInProgress, setDeleteInProgress] = useState(false);
  const [page, setPage] = useState(1);
  const isInitialRender = useRef(true); // Track if it's the first render
  const skipPageEffect = useRef(false); // Flag to skip effect when `page` is updated due to query change
  const { toast } = useToast();

  const fetchFilesWithParams = useCallback(
    async (params: {
      page: number;
      page_size: number;
      search?: string;
      search_enabled: boolean;
    }) => {
      setIsFetching(true);
      try {
        const response = await getNotDeletedFiles(folderId, params);
        const fetchedFiles: FileType[] = response.data?.results || [];
        const pages = response.data?.count;
        setFiles(fetchedFiles);
        setTotalPages(Math.ceil(pages / PAGE_SIZE));
      } finally {
        setIsFetching(false);
      }
    },
    [],
  );

  const debouncedSearch = useDebouncedCallback((searchQuery: string) => {
    setPage(1); // Reset to page 1 when searching
    skipPageEffect.current = true; // Skip the page effect since it's triggered by a query change
    fetchFilesWithParams({
      page: 1,
      page_size: PAGE_SIZE,
      search: searchQuery,
      search_enabled: true,
    });
  }, 500);

  useEffect(() => {
    // Prevent the effect from running on the first render
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    // Skip the API call if `page` was changed due to query update
    if (skipPageEffect.current) {
      skipPageEffect.current = false;
      return;
    }

    fetchFilesWithParams({
      page,
      page_size: PAGE_SIZE,
      search: query,
      search_enabled: !!query,
    });
  }, [page]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      setDeleteInProgress(true);
      await deleteFile(folderId, fileId);
      setFiles((prevFiles) => prevFiles.filter((file) => file.id !== fileId));
    } catch (error) {
      console.error("Error deleting file:", error);
      toast({
        variant: "destructive",
        description: "Couldn't delete file. Please try again later.",
      });
    } finally {
      setDeleteInProgress(false);
    }
  };

  return (
    <div className="mx-auto w-full flex-1 px-10 py-6">
      <div className="my-6 flex w-full items-center rounded-md border bg-white px-2 py-1 shadow-sm">
        <div className="flex items-center space-x-2 border-r bg-white px-3">
          <LucideSearch className="mx-2 text-gray-500" size={18} />
          <span className="text-sm text-gray-500">Search Files</span>
        </div>

        <Input
          type="text"
          className="flex-1 border-none focus:!ring-offset-0 focus-visible:ring-transparent"
          onChange={(e) => {
            const searchValue = e.target.value;
            setQuery(searchValue);
            debouncedSearch(searchValue);
          }}
          value={query}
        />
      </div>
      <div
        className={cn(
          "space-y-4",
          !isFetching &&
            files.length === 0 &&
            "flex flex-1 items-center justify-center",
        )}
      >
        {isFetching ? (
          Array(3)
            .fill(null)
            .map((_, idx) => (
              <div className="flex items-center space-y-3" key={idx}>
                <Skeleton className="h-16 w-full rounded-xl bg-black/30" />
              </div>
            ))
        ) : files.length === 0 ? (
          <div className="mx-auto mt-12 flex w-fit flex-col items-center gap-5 font-semibold">
            <div className="rounded-full bg-white p-5">
              <FileText size={32} className="text-primary" />
            </div>
            Oops, no files found :{"("}
          </div>
        ) : (
          files?.map((file, index) => (
            <FileCard
              key={index}
              fileName={file.name}
              fileSize={file.size}
              type={file.type as "DRIVE" | "PDF"}
              onDeleteFile={handleDeleteFile}
              deleteInProgress={deleteInProgress}
              id={file.id}
              status={file.status}
              folderId={folderId}
              url={file.url}
            />
          ))
        )}
      </div>
      {totalPages > 1 && (
        <div className="mt-6">
          <PaginationComponent
            handlePageChange={handlePageChange}
            page={page}
            totalPages={totalPages}
          />
        </div>
      )}
    </div>
  );
};

export default FileExplorer;
