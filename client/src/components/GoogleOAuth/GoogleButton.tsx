import React, { useRef, useEffect } from "react";
import { googleLogin } from "../../api";
import { statusCodes } from "../../api/constants";
import { useAuthStore } from "../../store/authStore";
import { useNavigate } from "react-router";
import { useToast } from "../../hooks/use-toast";

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (params: {
            client_id: string;
            callback: (response: { credential?: string }) => void;
            use_fedcm_for_prompt: boolean;
          }) => void;
          renderButton: (
            parent: HTMLElement,
            options: {
              theme: string;
              size: string;
              width: string;
              text: string;
              logo_alignment: string;
            },
          ) => void;
          prompt: () => void;
        };
      };
    };
  }
}

const GoogleSignInButton: React.FC = () => {
  const buttonDivRef = useRef<HTMLDivElement | null>(null);
  const login = useAuthStore((state) => state.login);
  const navigate = useNavigate();
  const { toast } = useToast();

  function googleAuthHandler(): (response: { credential?: string }) => void {
    return async (response) => {
      if (!response.credential) return;

      try {
        const res = await googleLogin(response.credential);
        if (res.status !== statusCodes.SUCCESS) {
          throw new Error("Google authentication failed. Please try again.");
        }
        toast({
          description: "Authentication successful.",
        });
        login(res.data.access_token);
        navigate("/");
      } catch (error) {
        toast({
          variant: "destructive",
          description: (error as Error).message,
        });
        console.error("Error sending token to backend:", error);
      }
    };
  }

  useEffect(() => {
    if (window.google?.accounts) {
      window.google.accounts.id.initialize({
        client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID as string,
        callback: googleAuthHandler(),
        use_fedcm_for_prompt: true,
      });
      if (buttonDivRef.current) {
        window.google.accounts.id.renderButton(buttonDivRef.current, {
          theme: "outline",
          size: "large",
          width: Math.round(
            window.innerWidth * (340 / window.innerWidth),
          ).toString(),
          text: "continue_with",
          logo_alignment: "center",
        });
      }
      window.google.accounts.id.prompt();
    }
  }, []);

  return <div ref={buttonDivRef} />;
};

export default GoogleSignInButton;
