/**
 *
 * DEPRECATED COMPONENT, KEPT FOR LEGACY REASONS.
 */

import { Database, Folder, Plus } from "lucide-react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Separator } from "../ui/separator";
import { useKnowledgeBaseStore } from "../../store/knowledgeBaseStore";
import { Button } from "../ui/button";
import { Card, CardHeader, CardTitle, CardDescription } from "../ui/card";
import { DialogHeader } from "../ui/dialog";
import { getFolders } from "../../api";
import { useEffect, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router";

type KBSelectionDialogProps = {
  activeKnowledgeBase: boolean;
  triggerText: string;
};

const KBSelectionDialog = ({
  activeKnowledgeBase,
  triggerText,
}: KBSelectionDialogProps) => {
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (state) => state.setActiveKnowledgeBase,
  );
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [folders, setFolders] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isFetching, setIsFetching] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const fetchFolders = async () => {
    setIsFetching(true);
    try {
      const data = await getFolders({ page: 1, page_size: 100 });
      // Update folders and total page count dynamically
      setFolders(data.results);
    } finally {
      setIsFetching(false);
    }
  };
  useEffect(() => {
    fetchFolders();
  }, []);
  return (
    <Dialog>
      <DialogTrigger className="w-full" asChild>
        <Button className="w-full">
          {activeKnowledgeBase ? <Database /> : <Plus />}
          <span className="text-md">{triggerText}</span>
          {activeKnowledgeBase && <Plus className="ml-auto" />}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[70rem]">
        <DialogHeader>
          <DialogTitle>Select a Knowledge Base</DialogTitle>
        </DialogHeader>
        <Separator className="my-2" />
        <div className="grid h-full w-full grid-cols-2 gap-4 gap-y-8 py-4 lg:grid-cols-3">
          {folders.map((item) => (
            <DialogClose key={item.id}>
              <Card
                className="flex cursor-pointer items-center gap-2 px-6 shadow-sm"
                onClick={() => {
                  setActiveKnowledgeBase(item, location.pathname);
                  navigate(`/chat/${item.id}/`);
                }}
              >
                <div className="relative">
                  <Folder className="absolute inset-0 m-auto text-primary" />
                  <div className="h-10 w-10 rounded-full bg-[#F5F5F7]"></div>
                </div>
                <CardHeader>
                  <CardTitle className="text-sm">{item.name}</CardTitle>
                  <CardDescription className="text-left text-xs text-[#666666]">
                    {item.file_count || "No"}{" "}
                    {item.file_count === 1 ? "File" : "Files"}
                  </CardDescription>
                </CardHeader>
              </Card>
            </DialogClose>
          ))}
          <Link to="/knowledgeBase">
            <Card className="flex h-full min-h-[5rem] w-full cursor-pointer items-center justify-center gap-2">
              <Plus strokeWidth={1.5} size={16} />
              <span className="text-sm font-[600]">
                Create New Knowledge Base
              </span>
            </Card>
          </Link>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default KBSelectionDialog;
