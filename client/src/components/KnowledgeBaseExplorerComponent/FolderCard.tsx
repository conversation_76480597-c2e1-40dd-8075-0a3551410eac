import { <PERSON><PERSON><PERSON>, Edit, Folder as FolderIcon } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "../ui/dialog";
import { Label } from "../ui/label";
import DeleteDialog from "../DeleteDialogComponent";
import { Button } from "../ui/button";
import { Card, CardHeader, CardTitle } from "../ui/card";
import { DialogHeader, DialogFooter } from "../ui/dialog";
import { Input } from "../ui/input";

import { Folder } from ".";
import { useNavigate } from "react-router";
import { useState } from "react";

type FolderProps = {
  folder: {
    id: string;
    name: string;
    file_count: number;
    description?: string;
  };
  folderEditHandler: (
    folderId: string,
    folderMetaData: Partial<Folder>,
  ) => void;
  folderDeleteHandler: (folderId: string) => void;
  deleteInProgress: boolean;
};

const FolderCard: React.FC<FolderProps> = ({
  folder,
  folderEditHandler,
  folderDeleteHandler,
  deleteInProgress,
}) => {
  const [folderMetaData, setFolderMetaData] = useState<Partial<Folder>>({
    name: folder.name ?? "",
    description: folder.description ?? "",
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const handleCardClick = () => {
    navigate(`/knowledgeBase/${folder.name}/${folder.id}`);
  };

  const handleFolderMetaDataUpdate = async (
    e: React.SyntheticEvent<HTMLFormElement>,
  ) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      setIsLoading(true);
      await folderEditHandler(folder.id, folderMetaData);
      setIsLoading(false);
      setIsDialogOpen(false); // Close the dialog only if the handler succeeds
      setFolderMetaData({
        name: folder.name ?? "",
        description: folder.description ?? "",
      });
    } catch (error) {
      console.error("Failed to update folder metadata:", error);
    }
  };

  return (
    <Card
      className="cursor-pointer bg-white shadow-sm"
      onClick={handleCardClick}
    >
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-4">
          <div className="flex">
            <FolderIcon className="mr-2 h-5 w-5 text-blue-600" />
          </div>
          <div className="flex flex-col">
            <p className="flex items-center gap-3 text-sm">{folder.name}</p>
            <p className="text-xs font-normal text-gray-500">
              {folder.file_count > 0 ? folder.file_count : "No"}{" "}
              {folder.file_count === 1 ? "File" : "Files"}
            </p>
          </div>
        </CardTitle>
        <span
          className="flex items-center gap-2"
          onClick={(e) => e.stopPropagation()} // Prevent routing
        >
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <span
                onClick={
                  (e) => e.stopPropagation() // Prevent routing
                }
              >
                <Pencil
                  size={14}
                  strokeWidth={2}
                  className="cursor-pointer text-black/70"
                />
              </span>
            </DialogTrigger>
            <DialogContent
              className="sm:max-w-[40rem]"
              onClick={(e) => e.stopPropagation()}
            >
              <DialogHeader className="mb-2">
                <DialogTitle>Update Folder</DialogTitle>
              </DialogHeader>
              <form
                onSubmit={handleFolderMetaDataUpdate}
                className="flex flex-col gap-4"
              >
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Folder Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="Folder Name"
                    className="col-span-3"
                    value={folderMetaData.name}
                    onClick={(e) => e.stopPropagation()} // Prevent dialog close
                    onChange={(e) =>
                      setFolderMetaData((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Input
                    id="description"
                    className="col-span-3"
                    placeholder="Folder Description"
                    value={folderMetaData.description}
                    onClick={(e) => e.stopPropagation()} // Prevent dialog close
                    onChange={(e) =>
                      setFolderMetaData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                  />
                </div>
                <DialogFooter>
                  <div className="flex justify-end gap-2">
                    <DialogClose asChild>
                      <Button variant="ghost" type="button">
                        Cancel
                      </Button>
                    </DialogClose>
                    <Button type="submit" onClick={(e) => e.stopPropagation()}>
                      <Edit className="ml-auto" />
                      {isLoading ? "Updating..." : "Update"}
                    </Button>
                  </div>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
          <DeleteDialog
            onDelete={(e) => {
              e?.stopPropagation();
              folderDeleteHandler(folder.id);
            }}
            deleteInProgress={deleteInProgress}
          />
        </span>
      </CardHeader>
    </Card>
  );
};

export default FolderCard;
