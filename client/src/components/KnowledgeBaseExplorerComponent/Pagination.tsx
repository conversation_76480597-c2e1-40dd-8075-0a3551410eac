import React, { useCallback } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../ui/pagination";
import { cn } from "../../lib/utils";

type PaginationProps = {
  page: number;
  totalPages: number;
  handlePageChange: (page: number) => void;
};

const PaginationComponent: React.FC<PaginationProps> = ({
  page,
  totalPages,
  handlePageChange,
}) => {
  // Memoized calculation of page numbers
  const getPageNumbers = useCallback(() => {
    const startPage = Math.max(1, page - 1); // One page before the current
    const endPage = Math.min(totalPages, page + 1); // One page after the current
    const pages = [];
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }, [page, totalPages]);

  const pageNumbers = getPageNumbers();

  return (
    <Pagination>
      <PaginationContent className="flex items-center gap-2">
        {/* Show Previous button only if not on the first page */}
        {page > 1 && (
          <PaginationItem>
            <PaginationPrevious
              href="#"
              size="default"
              onClick={() => handlePageChange(page - 1)}
              isActive
            />
          </PaginationItem>
        )}

        {pageNumbers.map((pageNum) => (
          <PaginationItem
            key={pageNum}
          >
            <PaginationLink
              href="#"
              size="default"
              isActive={page === pageNum}
              onClick={() => handlePageChange(pageNum)}
              className={cn(
                page !== pageNum &&
                  "hover:bg-white/80 hover:text-gray-800"
              )}
            >
              {pageNum}
            </PaginationLink>
          </PaginationItem>
        ))}

        {/* Show Next button only if not on the last page */}
        {page < totalPages && (
          <PaginationItem>
            <PaginationNext
              href="#"
              size="default"
              onClick={() => handlePageChange(page + 1)}
              isActive
            />
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  );
};

export default PaginationComponent;
