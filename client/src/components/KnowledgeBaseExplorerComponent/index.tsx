import { useState, useCallback, useEffect, useRef } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Plus, LucideSearch, Folder } from "lucide-react";
import {
  Dialog,
  <PERSON><PERSON>Close,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "../ui/dialog";
import { Skeleton } from "../ui/skeleton";
import {
  createFolder,
  deleteFolder,
  getFolders,
  updateFolder,
} from "../../api";
import { useToast } from "../../hooks/use-toast";
import FolderCard from "./FolderCard";
import PaginationComponent from "./Pagination";
import { useDebouncedCallback } from "use-debounce";
import { cn, PAGE_SIZE } from "../../lib/utils";
import { useNavigate } from "react-router";

export type Folder = {
  id: string;
  name: string;
  file_count: number;
  description?: string;
};

type KnowledgeBaseExplorerProps = {
  initialFolders: Folder[];
  totalPages: number;
  setTotalPages: React.Dispatch<React.SetStateAction<number>>;
};

const KnowledgeBaseExplorer = ({
  initialFolders,
  totalPages,
  setTotalPages,
}: KnowledgeBaseExplorerProps) => {
  const [newKnowledgeBase, setNewKnowledgeBase] = useState("");
  const [deleteInProgress, setDeleteInProgress] = useState(false);
  const [folders, setFolders] = useState(initialFolders);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [page, setPage] = useState(1);
  const { toast } = useToast();
  const navigate = useNavigate();

  const isInitialRender = useRef(true); // Track if it's the first render
  const skipPageEffect = useRef(false); // Flag to skip effect when `page` is updated due to query change
  const fetchFoldersWithParams = useCallback(
    async (params: {
      page: number;
      page_size: number;
      search?: string;
      search_enabled: boolean;
    }) => {
      setIsFetching(true);
      try {
        const data = await getFolders(params);
        setFolders(data.results);
        const totalPagesCount = Math.ceil(data.count / PAGE_SIZE);
        setTotalPages(totalPagesCount);
      } finally {
        setIsFetching(false);
      }
    },
    [],
  );

  const debouncedSearch = useDebouncedCallback((searchQuery: string) => {
    setPage(1); // Reset to page 1 when searching
    skipPageEffect.current = true; // Skip the page effect since it's triggered by a query change
    fetchFoldersWithParams({
      page: 1,
      page_size: PAGE_SIZE,
      search: searchQuery,
      search_enabled: true,
    });
  }, 500);

  useEffect(() => {
    // Prevent the effect from running on the first render
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    // Skip the API call if `page` was changed due to query update
    if (skipPageEffect.current) {
      skipPageEffect.current = false;
      return;
    }

    fetchFoldersWithParams({
      page,
      page_size: PAGE_SIZE,
      search: query,
      search_enabled: !!query,
    });
  }, [page]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const folderDeleteHandler = async (folderId: string) => {
    try {
      setDeleteInProgress(true);
      await deleteFolder(folderId);
      fetchFoldersWithParams({
        page,
        page_size: PAGE_SIZE,
        search: query,
        search_enabled: !!query,
      });
    } catch {
      toast({
        variant: "destructive",
        description: "Error deleting folder",
      });
    } finally {
      setDeleteInProgress(false);
    }
  };

  const folderEditHandler = async (
    folderId: string,
    folderMetaData: Partial<Folder>,
  ) => {
    try {
      if (folderMetaData?.name?.trim()) {
        await updateFolder(folderId, folderMetaData);
        toast({
          variant: "default",
          description: "Folder updated successfully",
        });
        fetchFoldersWithParams({
          page,
          page_size: PAGE_SIZE,
          search: query,
          search_enabled: !!query,
        });
      } else {
        toast({
          variant: "destructive",
          description: "Invalid data provided for updating",
        });
      }
    } catch {
      toast({
        variant: "destructive",
        description: "Error updating folder",
      });
    }
  };

  const folderCreateHandler = async () => {
    setLoading(true);
    try {
      const result = await createFolder(newKnowledgeBase, "");
      fetchFoldersWithParams({
        page,
        page_size: PAGE_SIZE,
        search: query,
        search_enabled: !!query,
      });
      navigate(`/knowledgeBase/${newKnowledgeBase}/${result.id}`);
    } catch (err: any) {
      if (err?.response?.status === 400) {
        // Handle the specific error for free users
        toast({
          variant: "destructive",
          description:
            "You are a free user and cannot create more than 3 folders.",
        });
      } else {
        // Handle other errors
        toast({
          variant: "destructive",
          description:
            "An error occurred while creating the folder. Please try again.",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex-1 flex w-full flex-col gap-6 bg-sidebar p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Knowledge Base</h1>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="default" className="flex items-center">
              <Plus className="h-4 w-4" />
              Create
            </Button>
          </DialogTrigger>
          <DialogContent
            className="sm:max-w-[425px]"
            onClick={(e) => e.stopPropagation()}
          >
            <DialogHeader>
              <DialogTitle>New Folder</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Input
                type="text"
                placeholder="Folder Name"
                className="col-span-3"
                //value={newKnowledgeBase}
                onChange={(e) => setNewKnowledgeBase(e.target.value)}
              />
            </div>
            <DialogFooter>
              <div className="flex justify-end gap-2">
                <DialogClose asChild>
                  <Button
                    variant="ghost"
                    onClick={() => setNewKnowledgeBase("")}
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button onClick={folderCreateHandler}>
                    <Plus className="ml-auto" />
                    {loading ? "Creating..." : "Create"}
                  </Button>
                </DialogClose>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex flex-1 flex-col gap-6">
        <div className="flex w-full items-center rounded-md border bg-white px-2 py-1 shadow-sm">
          <div className="flex items-center space-x-2 border-r bg-white px-3">
            <LucideSearch className="mx-2 text-gray-500" size={18} />
            <span className="text-sm text-gray-500">Search Folders</span>
          </div>

          <Input
            type="text"
            className="flex-1 border-none focus:!ring-offset-0 focus-visible:ring-transparent"
            onChange={(e) => {
              const searchValue = e.target.value;
              setQuery(searchValue);
              debouncedSearch(searchValue);
            }}
            value={query}
          />
        </div>

        <div
          className={cn(
            "grid grid-cols-1 gap-4 md:grid-cols-3",
            !isFetching && folders.length === 0 && "flex-1",
          )}
        >
          {isFetching ? (
            Array(3)
              .fill(null)
              .map((_, idx) => (
                <div className="flex flex-col items-center space-y-3" key={idx}>
                  <Skeleton className="h-24 w-60 rounded-xl bg-black/30" />
                </div>
              ))
          ) : folders.length === 0 ? (
            <div className="col-span-full mx-auto flex w-fit flex-col place-content-center items-center gap-5 font-semibold">
              <div className="rounded-full bg-white p-5">
                <Folder size={32} className="text-primary" />
              </div>
              Oops, no folder found :{"("}
            </div>
          ) : (
            folders.map((folder, index) => (
              <FolderCard
                folder={folder}
                key={index}
                folderDeleteHandler={folderDeleteHandler}
                folderEditHandler={folderEditHandler}
                deleteInProgress={deleteInProgress}
              />
            ))
          )}
        </div>

        {totalPages > 1 && (
          <div className="mt-6">
            <PaginationComponent
              handlePageChange={handlePageChange}
              page={page}
              totalPages={totalPages}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default KnowledgeBaseExplorer;
