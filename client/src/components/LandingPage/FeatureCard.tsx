import { motion } from "motion/react";

export default function FeatureCard({ icon, title, description, bgColor }) {
  return (
    <motion.div
      className="flex flex-col rounded-xl p-7 tracking-tight"
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      style={{ backgroundColor: bgColor }}
    >
      <h3 className="mb-1.5 self-start text-lg font-semibold text-[#0B0A0A] md:mb-2 md:text-xl">
        {title}
      </h3>
      <p className="mb-4 self-start text-sm font-medium text-[#0B0A0A99] md:text-base">
        {description}
      </p>
      <img src={icon} className="aspect-square w-full self-start" />
    </motion.div>
  );
}
