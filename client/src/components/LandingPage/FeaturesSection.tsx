import { motion } from "motion/react";
import FeatureCard from "./FeatureCard";
import AIChat from "../../assets/LandingPage/AIChat.svg";
import Lock from "../../assets/LandingPage/Lock.svg";
import KBUpload from "../../assets/LandingPage/KBUpload.svg";
import DataAnalysis from "../../assets/LandingPage/DataAnalysis.svg";
import MultiFile from "../../assets/LandingPage/MultiFile.svg";
import ChatBot from "../../assets/LandingPage/ChatBot.svg";

const features = [
  {
    icon: AIChat,
    title: "AI-Powered Chat",
    description: "Engage in natural, real-time conversations with your data.",
    bgColor: "#DDDDFF", // Light purple background
  },
  {
    icon: ChatBot,
    title: "Embed and Customize Chatbot",
    description:
      "Integrate your AI-powered chatbot into your website for seamless user interactions.",
    bgColor: "#FFEBFA", // Light purple/blue background
  },
  {
    icon: Lock,
    title: "Privacy & Security",
    description:
      "End-to-end encryption ensures your data stays safe and private.",
    bgColor: "#FFCFD0", // Light pink background
  },
  {
    icon: KBUpload,
    title: "Customizable Knowledge Base",
    description:
      "Create your own knowledge base by uploading custom data to meet your needs.",
    bgColor: "#E4FFC3", // Light green background
  },
  {
    icon: DataAnalysis,
    title: "Data Analysis",
    description:
      "Analyze complex datasets and extract actionable insights instantly.",
    bgColor: "#FFF8B6", // Light yellow background
  },
  {
    icon: MultiFile,
    title: "Multi-File Support",
    description:
      "Interact with PDF, DOCS, CSV, JSON, XLSX, and XLSM files or link Google Drive.",
    bgColor: "#C3E2FF", // Light blue background
  },
];

export default function FeaturesSection() {
  return (
    <section className="w-full bg-white pb-10 pt-11 lg:pb-10 lg:pt-20" id="lp-features">
      <div className="mx-auto max-w-7xl px-7">
        <motion.h2
          className="mb-2.5 text-center text-2xl font-bold text-[#040815] md:mb-5 md:text-3xl lg:text-4xl"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
        >
          Our Features
        </motion.h2>
        <motion.p
          className="mb-7 text-center text-sm font-medium text-[#666666] md:mb-9 md:text-base lg:mb-12 lg:text-lg"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
        >
          An intelligent assistant designed to streamline your knowledge base
          management
        </motion.p>
        <div className="grid grid-cols-1 place-items-center gap-8 md:grid-cols-3">
          {features.map((feature, index) => (
            <FeatureCard key={index} {...feature} />
          ))}
        </div>
      </div>
    </section>
  );
}
