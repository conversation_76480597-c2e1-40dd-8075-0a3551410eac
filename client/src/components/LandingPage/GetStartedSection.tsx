import { Link } from "react-router";
import FooterImage from "../../assets/LandingPage/Footer.svg";

export default function GetStartedSection() {
  return (
    <section className="w-full bg-[#2463EB] pb-0 pt-11 lg:pt-20">
      <div className="mx-auto flex max-w-7xl flex-col justify-between gap-10 px-7 md:flex-row md:gap-0">
        <div className="mx-auto flex w-[70%] flex-col items-center md:w-full md:items-start">
          <p className="mb-2.5 text-center text-base text-white md:mb-4 md:text-lg lg:text-xl">
            Get Started
          </p>
          <h2 className="mb-3 text-center text-2xl font-semibold text-white md:mb-6 md:text-3xl lg:text-4xl">
            Ready to Transform the Way You Work?
          </h2>
          <p className="mb-5 text-center text-sm text-white md:mb-8 md:text-base lg:mb-12 lg:text-lg">
            Experience the future of knowledge management today!
          </p>
          <Link className="rounded-full bg-white px-8 py-3 font-semibold text-black transition-colors hover:bg-opacity-90" to="/signup">
            Get a Free Demo
          </Link>
        </div>

        <img src={FooterImage} className="aspect-square w-[380px]" />
      </div>
    </section>
  );
}
