import { useState } from "react";
import { motion } from "motion/react";
import { BookOpen } from "lucide-react";
import { TextShimmer } from "./TextShimmer";
import { Link } from "react-router";
import { cn } from "../../lib/utils";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header
      className={cn(
        "w-full border-b border-b-[#FFFFFF29] bg-[#2463EB] py-5 transition-colors",
        isMenuOpen && "bg-[#0B4BD5]",
      )}
    >
      <nav className="mx-auto flex max-w-7xl items-center justify-between px-7 text-white">
        {/* Logo Section */}
        <div className="flex items-center gap-3">
          <BookOpen />
          <span className="text-xl font-semibold">BuddhiAI</span>
        </div>

        {/* Desktop Menu */}
        <div className="hidden items-center gap-12 tracking-tight lg:flex">
          <TextShimmer
            as="a"
            duration={1}
            className="cursor-pointer [--base-color:theme(colors.white)] [--base-gradient-color:#B2B2FF]"
            onHover={true}
            href="#lp-features"
          >
            Benefits
          </TextShimmer>
          <TextShimmer
            as="a"
            duration={1}
            className="cursor-pointer [--base-color:theme(colors.white)] [--base-gradient-color:#B2B2FF]"
            onHover={true}
            href="#lp-how-it-works"
          >
            How it Works
          </TextShimmer>
          <TextShimmer
            as="a"
            duration={1}
            className="cursor-pointer [--base-color:theme(colors.white)] [--base-gradient-color:#B2B2FF]"
            onHover={true}
            href="#lp-pricing"
          >
            Pricing
          </TextShimmer>
        </div>

        {/* Desktop Actions */}
        <div className="hidden items-center gap-8 lg:flex">
          <Link className="text-white" to="/login">
            Login
          </Link>
          <Link className="rounded-full bg-white px-6 py-3.5 text-base font-semibold tracking-tight text-black transition-colors hover:bg-opacity-90" to="/signup">
            Try Product
          </Link>
        </div>

        {/* Hamburger Menu Button */}
        <button
          className="text-white lg:hidden"
          onClick={() => setIsMenuOpen((prev) => !prev)}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </nav>

      {/* Mobile Menu */}
      <motion.div
        initial={{ height: 0 }}
        animate={{ height: isMenuOpen ? "auto" : 0 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className="overflow-hidden text-white backdrop-blur-sm lg:hidden"
      >
        <div className="mt-6 flex items-center justify-around px-2.5">
          <a href="#lp-features">Benefits</a>
          <a href="#lp-how-it-works">How it Works</a>
          <a href="#lp-pricing">Pricing</a>
          <Link to="/login">Login</Link>
        </div>
      </motion.div>
    </header>
  );
}
