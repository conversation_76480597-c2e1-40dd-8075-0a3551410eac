import { motion } from "motion/react";
import { fadeInUp } from "../../pages/LandingPage";
import { Tilt } from "./Tilt";

import Hero from "../../assets/LandingPage/Hero.svg";
import { Link } from "react-router";

export default function HeroSection() {
  return (
    <section className="w-full bg-[#2463EB] pb-10 pt-12 lg:pb-10 lg:pt-20">
      <motion.div
        className="mx-auto flex max-w-7xl flex-col items-center px-7 text-center"
        initial="initial"
        animate="animate"
        variants={fadeInUp}
      >
        <motion.h1
          className="mb-5 w-full text-4xl font-bold leading-snug text-white md:mb-6 md:text-6xl lg:text-7xl"
          variants={fadeInUp}
        >
          Chat with your own
          <br />
          Knowledge Base
        </motion.h1>

        <motion.p
          className="mb-12 max-w-2xl text-sm font-medium text-white md:mb-10 md:text-lg"
          variants={fadeInUp}
        >
          Instantly retrieve, summarize, and interact with your documents,
          knowledge bases, or FAQs using our advanced AI-powered chatbot
        </motion.p>

        <motion.div
          className="flex w-full flex-col gap-3 tracking-tight md:mb-8 md:w-fit md:flex-row lg:mb-12 lg:gap-6"
          variants={fadeInUp}
        >
          <Link className="rounded-full bg-white px-8 py-3 font-semibold text-black transition-colors hover:bg-opacity-90" to="/signup">
            Get a Free Demo
          </Link>
          <a
            className="rounded-full bg-transparent px-8 py-3 font-semibold text-white transition-colors md:bg-[#000000B2] md:text-[#C3D4E9] md:hover:text-white"
            href="#lp-pricing"
          >
            See Pricing
          </a>
        </motion.div>
        <Tilt
          rotationFactor={3}
          isRevese
          className="hidden cursor-pointer md:block"
        >
          <motion.img
            src={Hero}
            variants={fadeInUp}
            className="mx-auto block aspect-auto w-[70%]"
          />
        </Tilt>
      </motion.div>
    </section>
  );
}
