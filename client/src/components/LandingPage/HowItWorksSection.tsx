import { useState } from "react";
import { AnimatePresence, motion } from "motion/react";

import CreateImage from "../../assets/LandingPage/Create.svg";
import SelectImage from "../../assets/LandingPage/Select.svg";
import ChatImage from "../../assets/LandingPage/Chat.svg";
import { Tilt } from "./Tilt";
import { BorderTrail } from "./BorderTrail";

export default function HowItWorksSection() {
  const [activeTab, setActiveTab] = useState(0);
  const tabs = ["Create", "Select", "Chat"];

  return (
    <section
      className="w-full bg-[#F3F5F7] pb-10 pt-11 lg:pb-10 lg:pt-20"
      id="lp-how-it-works"
    >
      <div className="mx-auto max-w-7xl px-7">
        <span className="mb-2.5 block text-center text-sm font-semibold text-[#709DFF] md:mb-4 md:text-lg lg:text-xl">
          HOW IT WORKS
        </span>
        <h2 className="mb-3 text-center text-2xl font-bold text-black md:mb-6 md:text-3xl lg:text-4xl">
          Few Easy Steps and Done
        </h2>
        <p className="mx-auto mb-8 text-center text-sm font-medium text-[#666666] md:mb-16 md:w-[70%] md:text-base lg:text-lg">
          Upload your files, set your preferences, and start chatting with your
          data—it's that simple!
        </p>

        <div className="mb-12 flex justify-center md:mb-16">
          <div className="inline-flex rounded-[10px] bg-white p-1 text-sm font-semibold md:text-base lg:text-lg">
            {tabs.map((tab, index) => (
              <motion.button
                key={tab}
                className={`rounded-[10px] px-8 py-2 ${
                  activeTab === index
                    ? "bg-[#2463EB] text-white"
                    : "text-[#202630]"
                }`}
                onClick={() => setActiveTab(index)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {tab}
              </motion.button>
            ))}
          </div>
        </div>

        <AnimatePresence mode="wait">
          <Tilt rotationFactor={2} isRevese>
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="relative mx-auto w-fit cursor-pointer rounded-[30px] border shadow-lg"
            >
              <BorderTrail
                className="bg-[#B2B2FF]"
                borderWidthClass="border-[3px]"
                size={200}
              />
              <motion.img
                src={
                  activeTab === 0
                    ? CreateImage
                    : activeTab === 1
                      ? SelectImage
                      : ChatImage
                }
                className="max-h-[450px] rounded-[30px] p-4"
              />
            </motion.div>
          </Tilt>
        </AnimatePresence>
      </div>
    </section>
  );
}
