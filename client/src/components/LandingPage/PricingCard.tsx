import { Check, X } from "lucide-react";
import { motion } from "motion/react";
import { cn } from "../../lib/utils";
import { useNavigate } from "react-router";

export default function PricingCard({ plan, isPopular }) {
  const navigate = useNavigate();
  return (
    <motion.div
      className="rounded-xl bg-[#F3F5F7] p-8"
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
    >
      <div className="flex flex-col gap-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 md:gap-3">
            <img src={plan.icon} className="h-6 w-6 md:h-8 md:w-8" />
            <h3 className="mb-2 text-2xl font-bold text-[#040815] md:text-3xl lg:text-3xl">
              {plan.name}
            </h3>
          </div>
          {isPopular && (
            <span className="rounded-full bg-[#0D121F] px-4 py-1 text-sm text-white">
              Popular
            </span>
          )}
        </div>
        <p className="mb-6 text-[#596780]">{plan.description}</p>
      </div>

      <div className="mb-8">
        <span className="text-4xl font-bold text-[#040815]">${plan.price}</span>
        <span className="ml-2 text-[#596780]">/month</span>
      </div>

      <ul className="mb-8 space-y-6">
        {plan.features.map((feature, index) => (
          <li key={index} className="flex items-center gap-3">
            <span
              className={cn(
                "flex items-center justify-center rounded-full p-1 text-white",
                feature.included ? "bg-[#00A90E]" : "bg-[#596780]",
              )}
            >
              {feature.included ? <Check size={16} /> : <X size={16} />}
            </span>
            <span
              className={cn(
                "font-medium",
                feature.included ? "text-[#0D121F]" : "text-[#596780]",
              )}
            >
              {feature.text}
            </span>
          </li>
        ))}
      </ul>

      <button
        className="w-full rounded-full border border-[#2463EB] bg-transparent py-3 font-semibold text-[#2463EB] transition-colors hover:bg-[#2463EB]/10 disabled:cursor-not-allowed"
        disabled={plan.name !== "Free"}
        onClick={() => navigate("/signup")}
      >
        {plan.name === "Free" ? "Get Your Free Plan" : "Coming Soon"}
      </button>
    </motion.div>
  );
}
