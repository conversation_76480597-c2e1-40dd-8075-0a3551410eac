import { useState } from "react";
import { motion } from "motion/react";
import PricingCard from "./PricingCard";

import Lovely from "../../assets/LandingPage/Lovely.svg";
import Crown from "../../assets/LandingPage/Crown.svg";
import Flash from "../../assets/LandingPage/Flash.svg";
import CurvyArrow from "../../assets/LandingPage/CurvyArrow.svg";

export default function PricingSection() {
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: "Free",
      description: "Perfect plan to get started",
      price: 0,
      features: [
        { text: "Sync across device", included: true },
        { text: "5 workspace", included: true },
        { text: "Collaborate with 5 user", included: true },
        { text: "Sharing permission", included: false },
        { text: "Admin tools", included: false },
        { text: "100+ integrations", included: false },
      ],
      icon: Love<PERSON>,
    },
    {
      name: "<PERSON>",
      description: "Perfect plan for professionals!",
      price: isYearly ? 144 : 12,
      features: [
        { text: "Everything in Free Plan", included: true },
        { text: "Unlimited workspace", included: true },
        { text: "Collaborative workspace", included: true },
        { text: "Sharing permission", included: true },
        { text: "Admin tools", included: true },
        { text: "100+ integrations", included: true },
      ],
      icon: Crown,
    },
    {
      name: "Ultimate",
      description: "Best suits for great company!",
      price: isYearly ? 396 : 33,
      features: [
        { text: "Everything in Pro Plan", included: true },
        { text: "Daily performance reports", included: true },
        { text: "Dedicated assistant", included: true },
        { text: "Artificial intelligence", included: true },
        { text: "Marketing tools & automations", included: true },
        { text: "Advanced security", included: true },
      ],
      icon: Flash,
    },
  ];

  return (
    <section
      className="w-full bg-white pb-10 pt-11 lg:pb-10 lg:pt-20"
      id="lp-pricing"
    >
      <div className="mx-auto max-w-7xl px-6">
        <div className="relative">
          <h2 className="mb-3 text-center text-2xl font-bold text-[#0D121F] md:mb-6 md:text-3xl lg:text-4xl">
            Ready to Get Started?
          </h2>
          <p className="mb-8 text-center text-sm text-[#596780] md:mb-12 md:text-base lg:text-lg">
            Choose a plan that suits your business needs
          </p>

          <div className="relative mb-28 flex items-center justify-center gap-4">
            <span className="text-sm font-semibold text-[#0D121F] md:text-base lg:text-lg">
              Monthly
            </span>
            <button
              className="relative h-8 w-16 rounded-full bg-[#2463EB] p-1"
              onClick={() => setIsYearly(!isYearly)}
            >
              <motion.div
                className="absolute inset-0 left-1 my-auto h-6 w-6 rounded-full bg-white"
                animate={{ x: isYearly ? 30 : 0 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              />
            </button>
            <span className="text-sm font-semibold text-[#0D121F] md:text-base lg:text-lg">
              Yearly
            </span>
            <span className="absolute left-4 right-0 top-14 mx-auto w-fit rounded-full bg-[#E7DEFE] px-2.5 py-1.5 text-sm font-medium tracking-tight text-[#0D121F]">
              Save 65%
            </span>
            <img
              className="absolute -right-44 left-0 top-7 mx-auto w-fit scale-105"
              src={CurvyArrow}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {plans.map((plan, index) => (
            <PricingCard key={plan.name} plan={plan} isPopular={index === 1} />
          ))}
        </div>
      </div>
    </section>
  );
}
