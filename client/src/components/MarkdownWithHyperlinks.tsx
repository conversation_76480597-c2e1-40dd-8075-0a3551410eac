import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import Markdown from './shared/markdown';
import { Button } from './ui/button';
import { ExternalLink, FileText } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import SourceDocumentViewer from './SourceDocumentViewer';

interface HyperlinkData {
  hyperlink_id: string;
  source_ref: {
    file_id: string;
    file_name: string;
    chunk_id: string;
    page_number?: number;
    section_title?: string;
    start_char?: number;
    end_char?: number;
    content_preview?: string;
  };
  anchor_text: string;
  context: string;
}

interface SourceFile {
  id: string;
  name: string;
  type: string;
  size: number;
  status: string;
  path?: string;
}

interface MarkdownWithHyperlinksProps {
  content: string;
  hyperlinks?: Record<string, HyperlinkData>;
  onSourceView?: (hyperlinkData: HyperlinkData) => void;
  sourceFiles?: SourceFile[];
  onDownloadFile?: (file: SourceFile) => void;
}

const MarkdownWithHyperlinks: React.FC<MarkdownWithHyperlinksProps> = ({
  content,
  hyperlinks = {},
  onSourceView,
  sourceFiles = [],
  onDownloadFile
}) => {
  const [selectedHyperlink, setSelectedHyperlink] = useState<HyperlinkData | null>(null);
  const [showSourceDialog, setShowSourceDialog] = useState(false);
  const [showSourceViewer, setShowSourceViewer] = useState(false);

  // Parse content and replace hyperlink markers with clickable links
  const parseContentWithHyperlinks = (text: string): React.ReactNode => {
    if (!text || Object.keys(hyperlinks).length === 0) {
      return <Markdown>{text}</Markdown>;
    }

    // Replace hyperlink markers with HTML that can be processed by markdown
    const hyperlinkPattern = /\[\[source:([^\]]+)\]\]/g;
    const textWithHtmlLinks = text.replace(hyperlinkPattern, (match, hyperlinkId) => {
      const hyperlinkData = hyperlinks[hyperlinkId];
      if (hyperlinkData) {
        return ` <span class="hyperlink-inline" data-hyperlink-id="${hyperlinkId}"><a href="#" class="text-blue-600 hover:text-blue-800 text-xs underline no-underline">🔗 source</a></span>`;
      }
      return '';
    });

    return (
      <div
        className="prose prose-sm max-w-none"
        onClick={(e) => {
          e.preventDefault();
          const target = e.target as HTMLElement;
          const hyperlinkSpan = target.closest('.hyperlink-inline');
          if (hyperlinkSpan) {
            const hyperlinkId = hyperlinkSpan.getAttribute('data-hyperlink-id');
            if (hyperlinkId && hyperlinks[hyperlinkId]) {
              handleHyperlinkClick(hyperlinks[hyperlinkId]);
            }
          }
        }}
      >
        <Markdown>{textWithHtmlLinks}</Markdown>
      </div>
    );
  };

  const handleHyperlinkClick = (hyperlinkData: HyperlinkData) => {
    setSelectedHyperlink(hyperlinkData);
    setShowSourceDialog(true);
    
    // Call external handler if provided
    if (onSourceView) {
      onSourceView(hyperlinkData);
    }
  };

  const handleCloseDialog = () => {
    setShowSourceDialog(false);
    setSelectedHyperlink(null);
  };

  return (
    <>
      <div className="prose prose-sm max-w-none">
        {parseContentWithHyperlinks(content)}
      </div>

      {/* Source Reference Dialog */}
      <Dialog open={showSourceDialog} onOpenChange={setShowSourceDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText size={20} />
              Source Reference
            </DialogTitle>
            <DialogDescription>
              Information about the source of this content
            </DialogDescription>
          </DialogHeader>
          
          {selectedHyperlink && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">File Name</label>
                  <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.file_name}</p>
                </div>
                
                {selectedHyperlink.source_ref.section_title && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Section</label>
                    <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.section_title}</p>
                  </div>
                )}
                
                {selectedHyperlink.source_ref.page_number && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Page Number</label>
                    <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.page_number}</p>
                  </div>
                )}
                
                <div>
                  <label className="text-sm font-medium text-gray-700">Context</label>
                  <p className="text-sm text-gray-900 capitalize">{selectedHyperlink.context}</p>
                </div>
              </div>
              
              {selectedHyperlink.source_ref.content_preview && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Content Preview</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-md border">
                    <p className="text-sm text-gray-800 italic">
                      "{selectedHyperlink.source_ref.content_preview}"
                    </p>
                  </div>
                </div>
              )}
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={handleCloseDialog}>
                  Close
                </Button>
                <Button
                  onClick={() => {
                    // Open the source document viewer
                    setShowSourceViewer(true);
                    handleCloseDialog();
                  }}
                >
                  View Full Document
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Source Document Viewer */}
      <SourceDocumentViewer
        isOpen={showSourceViewer}
        onClose={() => setShowSourceViewer(false)}
        hyperlinkData={selectedHyperlink}
        sourceFiles={sourceFiles}
        onDownloadFile={onDownloadFile}
      />
    </>
  );
};

export default MarkdownWithHyperlinks;
