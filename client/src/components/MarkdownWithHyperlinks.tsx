import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import Markdown from './shared/markdown';
import { Button } from './ui/button';
import { ExternalLink, FileText } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';

interface HyperlinkData {
  hyperlink_id: string;
  source_ref: {
    file_id: string;
    file_name: string;
    chunk_id: string;
    page_number?: number;
    section_title?: string;
    start_char?: number;
    end_char?: number;
    content_preview?: string;
  };
  anchor_text: string;
  context: string;
}

interface MarkdownWithHyperlinksProps {
  content: string;
  hyperlinks?: Record<string, HyperlinkData>;
  onSourceView?: (hyperlinkData: HyperlinkData) => void;
}

const MarkdownWithHyperlinks: React.FC<MarkdownWithHyperlinksProps> = ({
  content,
  hyperlinks = {},
  onSourceView
}) => {
  const [selectedHyperlink, setSelectedHyperlink] = useState<HyperlinkData | null>(null);
  const [showSourceDialog, setShowSourceDialog] = useState(false);

  // Parse content and replace hyperlink markers with clickable links
  const parseContentWithHyperlinks = (text: string): React.ReactNode => {
    if (!text || Object.keys(hyperlinks).length === 0) {
      return <Markdown>{text}</Markdown>;
    }

    // Replace hyperlink markers with HTML that preserves markdown structure
    const hyperlinkPattern = /\[\[source:([^\]]+)\]\]/g;
    let processedText = text;
    const hyperlinkButtons: { [key: string]: React.ReactNode } = {};

    // Replace each hyperlink marker with a unique placeholder
    processedText = processedText.replace(hyperlinkPattern, (match, hyperlinkId) => {
      const hyperlinkData = hyperlinks[hyperlinkId];
      if (hyperlinkData) {
        const placeholder = `__HYPERLINK_${hyperlinkId}__`;
        hyperlinkButtons[placeholder] = (
          <Button
            key={`link-${hyperlinkId}`}
            variant="ghost"
            size="sm"
            className="inline-flex items-center gap-1 h-auto p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 text-xs ml-1"
            onClick={() => handleHyperlinkClick(hyperlinkData)}
          >
            <ExternalLink size={12} />
            <span className="underline">source</span>
          </Button>
        );
        return placeholder;
      }
      return match;
    });

    // Custom renderer for markdown that handles our hyperlink placeholders
    const components = {
      p: ({ children }: any) => {
        const processChildren = (child: any): any => {
          if (typeof child === 'string') {
            // Split by hyperlink placeholders and insert buttons
            const parts = child.split(/(__HYPERLINK_[^_]+__)/);
            return parts.map((part: string, index: number) => {
              if (part.startsWith('__HYPERLINK_') && hyperlinkButtons[part]) {
                return hyperlinkButtons[part];
              }
              return part;
            });
          }
          return child;
        };

        const processedChildren = React.Children.map(children, processChildren);
        return <p className="mb-3 last:mb-0 text-gray-700">{processedChildren}</p>;
      },
      // Preserve other markdown elements
      h1: ({ children }: any) => <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0">{children}</h1>,
      h2: ({ children }: any) => <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0">{children}</h2>,
      h3: ({ children }: any) => <h3 className="text-base font-semibold mb-2 mt-3 first:mt-0">{children}</h3>,
      strong: ({ children }: any) => <strong className="font-semibold">{children}</strong>,
      ul: ({ children }: any) => <ul className="list-disc list-outside mb-3 ml-4">{children}</ul>,
      ol: ({ children }: any) => <ol className="list-decimal list-outside mb-3 ml-4">{children}</ol>,
      li: ({ children }: any) => {
        const processChildren = (child: any): any => {
          if (typeof child === 'string') {
            const parts = child.split(/(__HYPERLINK_[^_]+__)/);
            return parts.map((part: string, index: number) => {
              if (part.startsWith('__HYPERLINK_') && hyperlinkButtons[part]) {
                return hyperlinkButtons[part];
              }
              return part;
            });
          }
          return child;
        };
        const processedChildren = React.Children.map(children, processChildren);
        return <li className="mb-1">{processedChildren}</li>;
      }
    };

    return (
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={components}
      >
        {processedText}
      </ReactMarkdown>
    );
  };

  const handleHyperlinkClick = (hyperlinkData: HyperlinkData) => {
    setSelectedHyperlink(hyperlinkData);
    setShowSourceDialog(true);
    
    // Call external handler if provided
    if (onSourceView) {
      onSourceView(hyperlinkData);
    }
  };

  const handleCloseDialog = () => {
    setShowSourceDialog(false);
    setSelectedHyperlink(null);
  };

  return (
    <>
      <div className="prose prose-sm max-w-none">
        {parseContentWithHyperlinks(content)}
      </div>

      {/* Source Reference Dialog */}
      <Dialog open={showSourceDialog} onOpenChange={setShowSourceDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText size={20} />
              Source Reference
            </DialogTitle>
            <DialogDescription>
              Information about the source of this content
            </DialogDescription>
          </DialogHeader>
          
          {selectedHyperlink && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">File Name</label>
                  <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.file_name}</p>
                </div>
                
                {selectedHyperlink.source_ref.section_title && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Section</label>
                    <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.section_title}</p>
                  </div>
                )}
                
                {selectedHyperlink.source_ref.page_number && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Page Number</label>
                    <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.page_number}</p>
                  </div>
                )}
                
                <div>
                  <label className="text-sm font-medium text-gray-700">Context</label>
                  <p className="text-sm text-gray-900 capitalize">{selectedHyperlink.context}</p>
                </div>
              </div>
              
              {selectedHyperlink.source_ref.content_preview && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Content Preview</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-md border">
                    <p className="text-sm text-gray-800 italic">
                      "{selectedHyperlink.source_ref.content_preview}"
                    </p>
                  </div>
                </div>
              )}
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={handleCloseDialog}>
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    // TODO: Implement full document viewer
                    console.log('Open full document:', selectedHyperlink.source_ref.file_name);
                    handleCloseDialog();
                  }}
                >
                  View Full Document
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MarkdownWithHyperlinks;
