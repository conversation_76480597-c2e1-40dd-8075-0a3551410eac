import React, { useState } from 'react';
import Markdown from './shared/markdown';
import { Button } from './ui/button';
import { ExternalLink, FileText } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';

interface HyperlinkData {
  hyperlink_id: string;
  source_ref: {
    file_id: string;
    file_name: string;
    chunk_id: string;
    page_number?: number;
    section_title?: string;
    start_char?: number;
    end_char?: number;
    content_preview?: string;
  };
  anchor_text: string;
  context: string;
}

interface MarkdownWithHyperlinksProps {
  content: string;
  hyperlinks?: Record<string, HyperlinkData>;
  onSourceView?: (hyperlinkData: HyperlinkData) => void;
}

const MarkdownWithHyperlinks: React.FC<MarkdownWithHyperlinksProps> = ({
  content,
  hyperlinks = {},
  onSourceView
}) => {
  const [selectedHyperlink, setSelectedHyperlink] = useState<HyperlinkData | null>(null);
  const [showSourceDialog, setShowSourceDialog] = useState(false);

  // Parse content and replace hyperlink markers with clickable links
  const parseContentWithHyperlinks = (text: string): React.ReactNode => {
    if (!text || Object.keys(hyperlinks).length === 0) {
      return <Markdown>{text}</Markdown>;
    }

    // Find all hyperlink markers in the format [[source:hyperlink_id]]
    const hyperlinkPattern = /\[\[source:([^\]]+)\]\]/g;
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;

    while ((match = hyperlinkPattern.exec(text)) !== null) {
      const [fullMatch, hyperlinkId] = match;
      const hyperlinkData = hyperlinks[hyperlinkId];

      // Add text before the hyperlink
      if (match.index > lastIndex) {
        const beforeText = text.slice(lastIndex, match.index);
        parts.push(
          <Markdown key={`text-${lastIndex}`}>{beforeText}</Markdown>
        );
      }

      // Add the hyperlink button
      if (hyperlinkData) {
        parts.push(
          <Button
            key={`link-${hyperlinkId}`}
            variant="ghost"
            size="sm"
            className="inline-flex items-center gap-1 h-auto p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 text-xs"
            onClick={() => handleHyperlinkClick(hyperlinkData)}
          >
            <ExternalLink size={12} />
            <span className="underline">source</span>
          </Button>
        );
      }

      lastIndex = match.index + fullMatch.length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      parts.push(
        <Markdown key={`text-${lastIndex}`}>{remainingText}</Markdown>
      );
    }

    return <div className="inline">{parts}</div>;
  };

  const handleHyperlinkClick = (hyperlinkData: HyperlinkData) => {
    setSelectedHyperlink(hyperlinkData);
    setShowSourceDialog(true);
    
    // Call external handler if provided
    if (onSourceView) {
      onSourceView(hyperlinkData);
    }
  };

  const handleCloseDialog = () => {
    setShowSourceDialog(false);
    setSelectedHyperlink(null);
  };

  return (
    <>
      <div className="prose prose-sm max-w-none">
        {parseContentWithHyperlinks(content)}
      </div>

      {/* Source Reference Dialog */}
      <Dialog open={showSourceDialog} onOpenChange={setShowSourceDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText size={20} />
              Source Reference
            </DialogTitle>
            <DialogDescription>
              Information about the source of this content
            </DialogDescription>
          </DialogHeader>
          
          {selectedHyperlink && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">File Name</label>
                  <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.file_name}</p>
                </div>
                
                {selectedHyperlink.source_ref.section_title && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Section</label>
                    <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.section_title}</p>
                  </div>
                )}
                
                {selectedHyperlink.source_ref.page_number && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Page Number</label>
                    <p className="text-sm text-gray-900">{selectedHyperlink.source_ref.page_number}</p>
                  </div>
                )}
                
                <div>
                  <label className="text-sm font-medium text-gray-700">Context</label>
                  <p className="text-sm text-gray-900 capitalize">{selectedHyperlink.context}</p>
                </div>
              </div>
              
              {selectedHyperlink.source_ref.content_preview && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Content Preview</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-md border">
                    <p className="text-sm text-gray-800 italic">
                      "{selectedHyperlink.source_ref.content_preview}"
                    </p>
                  </div>
                </div>
              )}
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={handleCloseDialog}>
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    // TODO: Implement full document viewer
                    console.log('Open full document:', selectedHyperlink.source_ref.file_name);
                    handleCloseDialog();
                  }}
                >
                  View Full Document
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MarkdownWithHyperlinks;
