import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";
import { useEffect, useRef } from "react";
import { useNavigate } from "react-router";
import { useUserStore } from "../../store/userStore";
import { BookText, LogOut, User } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "../ui/dropdown-menu";
import { useAuthStore } from "../../store/authStore";

const AccountChip = () => {
  const { user, fetchUser, isLoading } = useUserStore();
  const navigate = useNavigate();
  const retryCount = useRef(0);
  const MAX_RETRIES = 3;

  const logout = useAuthStore((state) => state.logout);
  const removeUserData = useUserStore((state) => state.removeUserData);

  const handleLogout = () => {
    logout();
    removeUserData();
    navigate("/login", { replace: true });
  };

  useEffect(() => {
    if (!user?.username && !isLoading && retryCount.current < MAX_RETRIES) {
      fetchUser();
      retryCount.current += 1;
    }
  }, [user?.username, isLoading, fetchUser]);

  useEffect(() => {
    if (user?.username) {
      retryCount.current = 0;
    }
  }, [user?.username]);

  const handleDocumentation = () => {
    navigate("#");
  };

  const handleProfile = () => {
    navigate("/dashboard");
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer">
          {user.profile_image && (
            <AvatarImage
              src={user.profile_image}
              height={22}
              width={22}
              alt=""
            />
          )}
          <AvatarFallback className="bg-primary font-medium text-white">
            {user?.username?.[0]?.toUpperCase() || "A"}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={handleProfile}
          className="flex cursor-pointer items-center justify-between p-2"
        >
          Profile
          <User />
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDocumentation}
          className="flex cursor-pointer items-center justify-between p-2"
        >
          Documentation
          <BookText />
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleLogout}
          className="flex cursor-pointer items-center justify-between p-2"
        >
          Logout
          <LogOut />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AccountChip;
