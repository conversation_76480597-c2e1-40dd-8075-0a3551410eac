import { Sidebar, SidebarContent, SidebarMenu } from "../ui/sidebar";
import { InactiveSidebar } from "./SidebarStateComponents/InactiveSidebar";
import {
  ActiveSidebar,
  ChatSession,
} from "./SidebarStateComponents/ActiveSidebar";
import { Database, Files, MessageSquarePlus } from "lucide-react";
import { useParams } from "react-router";

export type MenuItem = {
  title: string;
  icon: any;
  link: string;
};

type AppSidebarProps = {
  fetchChats: () => void;
  loading: boolean;
  chats: ChatSession[];
};

const items: MenuItem[] = [
  {
    title: "Workspace",
    icon: MessageSquarePlus,
    link: "/",
  },
  {
    title: "Document Summarizer",
    icon: Files,
    link: "/document-summarizer",
  },

];

export const AppSidebar = ({ fetchChats, loading, chats }: AppSidebarProps) => {
  const params = useParams();
  return (
    <Sidebar side="left" className="mt-[4rem]">
      <SidebarContent>
        <SidebarMenu className="mt-4">
          {params.agentName ? (
            <ActiveSidebar
              fetchChats={fetchChats}
              loading={loading}
              chats={chats}
            />
          ) : (
            <InactiveSidebar items={items} />
          )}
        </SidebarMenu>
      </SidebarContent>
    </Sidebar>
  );
};
