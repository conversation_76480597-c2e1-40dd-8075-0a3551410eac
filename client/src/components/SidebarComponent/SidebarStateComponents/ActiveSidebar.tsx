import { ChevronLeft, MessageCircleMore, Plus } from "lucide-react";
import { But<PERSON> } from "../../ui/button";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../../ui/sidebar";
import { useEffect, useState } from "react";
import { deleteChat, createUrl } from "../../../api";
import { useToast } from "../../../hooks/use-toast";
import { useNavigate, useParams, useSearchParams } from "react-router";
import { Separator } from "../../ui/separator";
import { Skeleton } from "../../ui/skeleton";
import { cn } from "../../../lib/utils";
import UpdateChatDialog from "../../CreateChatDialog/updateChatDialog";
import { AddUrlDialog } from "../../FileExplorerComponent/AddUrlDialog";
import DeleteDialog from "../../DeleteDialogComponent";

export interface ChatSession {
  session_id: string;
  folder_id: string;
  agent_id: string;
  created_at: string;
  knowledge_base_name: string;
  chat_name: string;
  last_message: string;
}

type ActiveSidebarProps = {
  loading: boolean;
  chats: ChatSession[];
  fetchChats: () => void;
};

export const ActiveSidebar = ({
  fetchChats,
  loading,
  chats,
}: ActiveSidebarProps) => {
  const [isUrlDialogOpen, setUrlDialogOpen] = useState(false);
  const [newChatLoading, setNewChatLoading] = useState(false);
  const [activeChatSessionId, setActiveChatSessionId] = useState<string>();
  const [deleteInProgress, setDeleteInProgress] = useState<boolean>(false);
  const { toast } = useToast();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    const sessionId = searchParams.get("sessionId");
    setActiveChatSessionId(sessionId || undefined);
  }, [searchParams]);

  const handleDeleteChat = async (sessionId: string) => {
    try {
      setDeleteInProgress(true);
      await deleteChat(sessionId);
      if (activeChatSessionId === sessionId) {
        navigate(
          `/chat/${params.agentName}/${params.folderId}/${params.agentId}`,
        );
      }
      fetchChats();
    } catch {
      toast({
        variant: "destructive",
        description: "Failed to delete chat",
      });
    } finally {
      setDeleteInProgress(false);
    }
  };

  const handleUrlSubmit = async (urlData: {
    url: string;
    crawlerType: string;
    maxRequests: number;
    crawlLinks: boolean;
    enqueueStrategy: string;
  }) => {
    try {
      toast({
        description: "Adding URL...",
      });

      const response = await createUrl(params.folderId as string, {
        url: urlData.url,
        meta_data: {
          crawler_type: urlData.crawlerType,
          max_requests: urlData.maxRequests,
          crawl_links: urlData.crawlLinks,
          enqueue_strategy: urlData.enqueueStrategy,
        },
      });

      if (response.data) {
        toast({
          description: "URL added successfully",
        });
      }
    } catch (error) {
      console.error("Error adding URL:", error);
      toast({
        variant: "destructive",
        description: "Failed to add URL. Please try again.",
      });
    }
  };

  const newChatHandler = async () => {
    setNewChatLoading(true);
    navigate(`/chat/${params.agentName}/${params.folderId}/${params.agentId}`);
    setNewChatLoading(false);
  };

  const isNewChat = !activeChatSessionId;

  return (
    <div className="px-3">
      <style>
        {`.sidebar-item:not(:hover) .trash-icon {
            display: none;
          }`}
      </style>

      <SidebarGroupLabel className="mb-6 flex w-full items-center justify-between gap-2 text-[15px] font-bold text-black">
        <div className="flex items-center gap-2">
          <ChevronLeft
            onClick={() => navigate("/")}
            className="cursor-pointer text-lg"
          />
          <span className="max-w-[140px] truncate text-xl">
            {params.agentName}
          </span>
        </div>
        <UpdateChatDialog
          agentId={params.agentId as string}
          folderId={params.folderId as string}
        />
      </SidebarGroupLabel>

      <div className="my-3">
        <AddUrlDialog
          isOpen={isUrlDialogOpen}
          onClose={() => setUrlDialogOpen(false)}
          onSubmit={handleUrlSubmit}
        />
      </div>
      <Separator className="mt-4" />

      <Button className="w-full bg-primary" onClick={newChatHandler}>
        <Plus />
        {newChatLoading ? "Creating..." : "New Chat"}
      </Button>
      <Separator className="mt-4" />
      <SidebarGroup>
        <SidebarGroupLabel className="mb-2 font-semibold">
          Previous Conversations
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {isNewChat && (
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <div
                    className={cn(
                      "flex cursor-pointer justify-between rounded py-6 pl-2 text-left text-sm text-gray-700 hover:bg-gray-100",
                      "border-2 border-primary/20 text-primary",
                    )}
                  >
                    <div className="flex items-center gap-2">
                      <MessageCircleMore />
                      New Chat
                    </div>
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )}
            {loading ? (
              <>
                <Skeleton className="rounded px-4 py-6" />
                <Skeleton className="rounded px-4 py-6" />
                <Skeleton className="rounded px-4 py-6" />
              </>
            ) : (
              chats.map((item) => (
                <SidebarMenuItem
                  key={item.session_id}
                  onClick={() =>
                    navigate(
                      `/chat/${params.agentName}/${item.folder_id}/${item.agent_id}?sessionId=${item.session_id}`,
                    )
                  }
                >
                  <SidebarMenuButton asChild>
                    <div
                      className={cn(
                        "sidebar-item group flex cursor-pointer items-center justify-between rounded py-6 pl-2 text-left text-sm text-gray-700 hover:bg-gray-100",
                        activeChatSessionId === item.session_id &&
                          "border-2 border-primary/40 text-primary",
                      )}
                    >
                      <div className="flex flex-1 items-center gap-2 overflow-hidden">
                        <MessageCircleMore className="flex-shrink-0" />
                        <span className="block max-w-[150px] truncate">
                          {item.chat_name || "New Chat"}
                        </span>
                      </div>
                      <DeleteDialog
                        onDelete={(e) => {
                          e.stopPropagation();
                          handleDeleteChat(item.session_id);
                        }}
                        className="trash-icon duration-250 transition ease-in-out"
                        deleteInProgress={deleteInProgress}
                      />
                    </div>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))
            )}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </div>
  );
};
