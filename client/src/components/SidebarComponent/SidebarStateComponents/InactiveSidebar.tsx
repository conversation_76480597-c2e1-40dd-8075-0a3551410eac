import { <PERSON> } from "react-router";
import { SidebarMenuItem, SidebarMenuButton } from "../../ui/sidebar";
import { MenuItem } from "../AppSidebar";
import { cn } from "../../../lib/utils";

type InactiveSidebarProps = {
  items: MenuItem[];
};

export const InactiveSidebar = ({ items }: InactiveSidebarProps) => {
  const currentPath = window.location.pathname;

  const withCondition = (condition: boolean, className: string) =>
    condition ? className : "";

  return items.map((item) => (
    <SidebarMenuItem key={item.title} className="px-4">
      <SidebarMenuButton asChild className="">
        <Link
          className={cn(
            "cursor-pointer gap-4 p-6 text-sm font-medium transition-colors hover:bg-[#e9f1fa]",
            withCondition(currentPath === item.link, "bg-[#EFF6FF] text-primary")
          )}
          to={item.link}
        >
          <item.icon size={18} />
          <span>{item.title}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  ));
};
