import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Skeleton } from './ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import {
  FileText,
  Download,
  Search,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Input } from './ui/input';

interface SourceFile {
  id: string;
  name: string;
  type: string;
  size?: number;
  path: string;
  url?: string;
}

interface HyperlinkData {
  hyperlink_id: string;
  source_ref: {
    file_id: string;
    file_name: string;
    chunk_id: string;
    page_number?: number;
    section_title?: string;
    start_char?: number;
    end_char?: number;
    content_preview?: string;
  };
  anchor_text: string;
  context: string;
}

interface SourceDocumentViewerProps {
  isOpen: boolean;
  onClose: () => void;
  hyperlinkData?: HyperlinkData;
  sourceFiles: SourceFile[];
  onDownloadFile?: (file: SourceFile) => void;
}

const SourceDocumentViewer: React.FC<SourceDocumentViewerProps> = ({
  isOpen,
  onClose,
  hyperlinkData,
  sourceFiles,
  onDownloadFile
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  const [currentFileIndex, setCurrentFileIndex] = useState(0);

  // Find the source file for the current hyperlink
  const currentSourceFile = hyperlinkData
    ? sourceFiles.find(file => file.name === hyperlinkData.source_ref.file_name)
    : sourceFiles[currentFileIndex];

  const handlePreviousFile = () => {
    setCurrentFileIndex(prev => Math.max(0, prev - 1));
  };

  const handleNextFile = () => {
    setCurrentFileIndex(prev => Math.min(sourceFiles.length - 1, prev + 1));
  };

  const handleDownload = () => {
    if (currentSourceFile && onDownloadFile) {
      onDownloadFile(currentSourceFile);
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "Unknown";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
  };

  const highlightSearchTerm = (text: string, term: string) => {
    if (!term) return text;
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText size={20} />
            Source Document Viewer
          </DialogTitle>
          <DialogDescription>
            View and navigate through source documents
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0">
          {/* File Navigation Header */}
          <div className="flex items-center justify-between p-4 border-b bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousFile}
                  disabled={currentFileIndex === 0}
                >
                  <ChevronLeft size={16} />
                </Button>
                <span className="text-sm font-medium">
                  {sourceFiles.length > 0 ? `${currentFileIndex + 1} of ${sourceFiles.length}` : 'No files'}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextFile}
                  disabled={currentFileIndex >= sourceFiles.length - 1}
                >
                  <ChevronRight size={16} />
                </Button>
              </div>

              {currentSourceFile && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{currentSourceFile.type.toUpperCase()}</Badge>
                  <span className="text-sm text-gray-600">
                    {formatFileSize(currentSourceFile.size)}
                  </span>
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search in document..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download size={16} className="mr-1" />
                Download
              </Button>
            </div>
          </div>

          {/* Current File Info */}
          {currentSourceFile && (
            <Card className="m-4">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">{currentSourceFile.name}</CardTitle>
                {hyperlinkData && (
                  <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                    {hyperlinkData.source_ref.section_title && (
                      <Badge variant="secondary">
                        Section: {hyperlinkData.source_ref.section_title}
                      </Badge>
                    )}
                    {hyperlinkData.source_ref.page_number && (
                      <Badge variant="secondary">
                        Page: {hyperlinkData.source_ref.page_number}
                      </Badge>
                    )}
                    <Badge variant="secondary">
                      Context: {hyperlinkData.context}
                    </Badge>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                {hyperlinkData?.source_ref.content_preview && (
                  <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="text-sm font-medium text-yellow-800 mb-2">
                      Referenced Content:
                    </h4>
                    <p className="text-sm text-yellow-700 italic">
                      "{hyperlinkData.source_ref.content_preview}"
                    </p>
                  </div>
                )}
                
                {/* Document Content Preview */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Document Preview:</h4>
                  {loading ? (
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  ) : (
                    <div className="p-4 bg-gray-50 rounded-md border max-h-96 overflow-y-auto">
                      <p className="text-sm text-gray-600">
                        Document content preview is not available in this demo. 
                        In a full implementation, this would show the actual document content 
                        with the referenced section highlighted.
                      </p>
                      <div className="mt-4 p-3 bg-white rounded border-l-4 border-blue-500">
                        <p className="text-sm text-gray-800">
                          <strong>File:</strong> {currentSourceFile.name}<br />
                          <strong>Type:</strong> {currentSourceFile.type.toUpperCase()}<br />
                          <strong>Size:</strong> {formatFileSize(currentSourceFile.size)}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {!currentSourceFile && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <FileText size={48} className="text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No source document available</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SourceDocumentViewer;
