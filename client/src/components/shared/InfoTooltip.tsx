import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";

export default function InfoTooltip({ children, className }) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info size={16} />
        </TooltipTrigger>
        <TooltipContent className={className}>{children}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
