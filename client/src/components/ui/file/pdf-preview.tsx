import React, { useState, Dispatch, SetStateAction, useEffect } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../dialog";
import { Button } from "../button";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url,
).toString();

type PDFPReviewProps = {
  source: {
    file: string | null;
    fileName: string;
  };
  isDialogOpen: boolean;
  setIsDialogOpen: Dispatch<SetStateAction<boolean>>;
  loading: boolean;
  error?: string | null;
};

const PdfPreview: React.FC<PDFPReviewProps> = ({
  source,
  isDialogOpen,
  setIsDialogOpen,
  loading,
  error,
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  useEffect(() => {
    const updateWidth = () => {
      const container = document.querySelector(".pdf-container");
      if (container) {
        setContainerWidth(container.clientWidth);
      }
    };

    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  const handleDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPageNumber(1);
  };

  const changePage = (offset: number) => {
    setPageNumber((prevPage) =>
      Math.max(1, Math.min(prevPage + offset, numPages)),
    );
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogContent className="w-full max-w-5xl">
        <DialogHeader>
          <DialogTitle>{source.fileName}</DialogTitle>
          <DialogDescription>PDF Preview</DialogDescription>
        </DialogHeader>
        {loading && (
          <div className="flex h-[80vh] items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {error && (
          <div className="flex h-[80vh] items-center justify-center text-xl text-red-500 md:text-2xl lg:text-3xl">
            Error loading PDF
          </div>
        )}

        {!loading && source.file && !error && (
          <div className="scrollbar-hide pdf-container relative flex h-[80vh] flex-col items-center overflow-y-auto">
            <Document
              file={source.file}
              onLoadSuccess={handleDocumentLoadSuccess}
              loading={
                <div className="flex h-[80vh] items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              }
              error={
                <div className="flex h-[80vh] items-center justify-center text-xl text-red-500 md:text-2xl lg:text-3xl">
                  Error loading PDF
                </div>
              }
              className="h-full"
            >
              <Page
                pageNumber={pageNumber}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                width={containerWidth}
              />
            </Document>
          </div>
        )}
        {!loading && source.file && !error && numPages > 1 && (
          <div className="mx-auto flex items-center justify-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => changePage(-1)}
              disabled={pageNumber <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span>
              Page {pageNumber} of {numPages}
            </span>
            <Button
              variant="outline"
              size="icon"
              onClick={() => changePage(1)}
              disabled={pageNumber >= numPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PdfPreview;
