import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { ChatBotConfig } from "../pages/ChatBot";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatBytes(
  bytes: number,
  opts: {
    decimals?: number;
    sizeType?: "accurate" | "normal";
  } = {},
) {
  const { decimals = 0, sizeType = "normal" } = opts;

  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const accurateSizes = ["Bytes", "KiB", "MiB", "GiB", "TiB"];
  if (bytes === 0) return "0 Byte";
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${
    sizeType === "accurate"
      ? (accurateSizes[i] ?? "Bytes")
      : (sizes[i] ?? "Bytes")
  }`;
}

export function absoluteUrl(path: string) {
  return `${path}`;
}

export function formatDateToDayMonth(utcDate: string): string {
  const date = new Date(utcDate);

  // Get day, month, and year parts
  const day = date.getUTCDate();
  const monthIndex = date.getUTCMonth();
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sept",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Determine the ordinal suffix
  const suffix = (day: number): string => {
    if (day % 10 === 1 && day !== 11) return "st";
    if (day % 10 === 2 && day !== 12) return "nd";
    if (day % 10 === 3 && day !== 13) return "rd";
    return "th";
  };

  return `${day}${suffix(day)} ${monthNames[monthIndex]}`;
}

/**
 * Stole this from the @radix-ui/primitive
 * @see https://github.com/radix-ui/primitives/blob/main/packages/core/primitive/src/primitive.tsx
 */
export function composeEventHandlers<E>(
  originalEventHandler?: (event: E) => void,
  ourEventHandler?: (event: E) => void,
  { checkForDefaultPrevented = true } = {},
) {
  return function handleEvent(event: E) {
    originalEventHandler?.(event);

    if (
      checkForDefaultPrevented === false ||
      !(event as unknown as Event).defaultPrevented
    ) {
      return ourEventHandler?.(event);
    }
  };
}

export const PAGE_SIZE = 12;

export type ErrorType = {
  code: string;
  message: string;
};

// Error constants
export const EMBED_CHATBOT_ERRORS = {
  KNOWLEDGE_BASE_NOT_FOUND: {
    code: "KNOWLEDGE_BASE_NOT_FOUND",
    message:
      "The requested knowledge base could not be found. It seems like it has not been added or created yet. Please create a new knowledge base to proceed.",
  },
  UNAUTHORIZED: {
    code: "UNAUTHORIZED",
    message:
      "You do not have the necessary permissions. It may not be public or your account lacks the required access rights—please contact the administrator if needed.",
  },
  GENERIC_ERROR: {
    code: "GENERIC_ERROR",
    message:
      "An unexpected error occurred. This could be due to a temporary issue or an unanticipated condition in the system. Please refresh the page or try again later. If the issue persists, contact support for assistance.",
  },
};

export const defaultChatBotColors = [
  "#FF4D4D", // Red
  "#FF8B39", // Orange
  "#D7AB00", // Yellow
  "#4CAF50", // Green
  "#6BCB77", // Light Green
  "#4DD4E1", // Cyan
  "#42A5F5", // Blue
  "#3F51B5", // Indigo
];

export const normalizeAttribute = (attribute) => {
  return attribute.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
};

export const formatEmbedBotResponse = (data) => {
  const summary = data.summary;
  const answers = data.details.map((item) => item.answer).join("|||");
  return `${summary}|||${answers}`;
};

export const isChatBotFormDisabled = (config: ChatBotConfig): boolean => {
  return !config.botName.trim() || (config.isExposed && !config.domain.trim());
};
