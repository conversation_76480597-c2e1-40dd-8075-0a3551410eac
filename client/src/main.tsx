import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { BrowserRouter } from "react-router";
import { Toaster } from "./components/ui/toaster";
import AppRouter from "./pages/Router";
import { ApiInterceptor } from "./api/api";
import { CookiesProvider } from "react-cookie";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <CookiesProvider>
      <BrowserRouter>
        <ApiInterceptor />
        <AppRouter />
      </BrowserRouter>
    </CookiesProvider>
    <Toaster />
  </StrictMode>,
);
