import { useEffect, useState } from "react";
import { useParams, useSearchParams, useLocation } from "react-router";
import InputArea from "../../components/ChatComponent/components/InputArea";
import { SidebarProvider } from "../../components/ui/sidebar";
import { AppSidebar } from "../../components/SidebarComponent/AppSidebar";
import {
  KnowledgeBase,
  useKnowledgeBaseStore,
} from "../../store/knowledgeBaseStore";
import { getAllChatsForUser } from "../../api";
import { useToast } from "../../hooks/use-toast";
import { ChatSession } from "../../components/SidebarComponent/SidebarStateComponents/ActiveSidebar";
import AppHeader from "../../components/AppHeaderComponent";

const ChatScreen = () => {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (state) => state.setActiveKnowledgeBase,
  );
  const { toast } = useToast();
  const [sessionId, setSessionId] = useState(
    searchParams.get("sessionId") || "",
  );
  const [newUser, setNewUser] = useState(!searchParams.get("sessionId"));
  const [chatName, setChatName] = useState("New Chat");
  const [loading, setLoading] = useState(false);
  const [chats, setChats] = useState<ChatSession[]>([]);

  const fetchChats = async () => {
    try {
      setLoading(true);
      const response = await getAllChatsForUser(params.agentId as string);
      setChats(
        response.results.sort((a: ChatSession, b: ChatSession) =>
          b.created_at.localeCompare(a.created_at),
        ),
      );
    } catch {
      toast({
        variant: "destructive",
        description: "Failed to fetch chats",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateChatLocally = (id: string, name?: string) => {
    setChats((prev) => {
      const updated = [...prev];
      const index = updated.findIndex((c) => c.session_id === id);
      if (index !== -1) {
        const chat = { ...updated[index] };
        if (name) {
          chat.chat_name = name;
        }
        updated.splice(index, 1);
        updated.unshift(chat);
      } else {
        updated.unshift({
          session_id: id,
          agent_id: params.agentId || "",
          folder_id: params.folderId || "",
          created_at: new Date().toISOString(),
          knowledge_base_name: "",
          chat_name: name || "New Chat",
          last_message: "",
        });
      }
      return updated;
    });
  };

  useEffect(() => {
    fetchChats();
  }, [params.agentId, params.folderId]); // Only refetch if agent/folder changes

  useEffect(() => {
    const id = searchParams.get("sessionId") || "";
    setSessionId(id);
    setNewUser(!id);
    if (!id) {
      setChatName("New Chat");
    }
    setActiveKnowledgeBase(
      {
        name: params.knowledgebase,
        id: params.folderId,
      } as Partial<KnowledgeBase>,
      location.pathname,
    );
  }, [searchParams, params, location.pathname, setActiveKnowledgeBase]);

  return (
    <>
      <AppHeader />
      <div className="flex-1 flex">
        <SidebarProvider defaultOpen className="w-auto bg-card">
          <AppSidebar fetchChats={fetchChats} loading={loading} chats={chats} />
        </SidebarProvider>
        <div className="flex w-full flex-col items-center bg-sidebar">
          <InputArea
            isNewUser={newUser}
            sessionId={sessionId}
            folderId={params.folderId}
            setSessionId={setSessionId}
            setChatName={setChatName}
            updateChatLocally={updateChatLocally}
          />
        </div>
      </div>
    </>
  );
};

export default ChatScreen;
