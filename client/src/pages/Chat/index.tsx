import { MessageSquarePlus, Plus } from "lucide-react";
import { AppSidebar } from "../../components/SidebarComponent/AppSidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { SidebarProvider } from "../../components/ui/sidebar";
import ChatPage from "../../components/ChatComponent/ChatPage";
import { useEffect, useState } from "react";
import { getAllAgents, getFolders } from "../../api";
import { useKnowledgeBaseStore } from "../../store/knowledgeBaseStore";
import { Skeleton } from "../../components/ui/skeleton";
import { cn, PAGE_SIZE } from "../../lib/utils";
import { useUserStore } from "../../store/userStore";
import CreateChatDialog from "../../components/CreateChatDialog";
import { AgentData } from "../../components/ChatComponent/components/ChatExplore";
import { Link, useLocation } from "react-router";
import AppHeader from "../../components/AppHeaderComponent";
import { Button } from "../../components/ui/button";

const Chat = () => {
  const [chatBots, setChatBots] = useState<AgentData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [totalPages, setTotalPages] = useState(Infinity);
  const [folders, setFolders] = useState([]);
  const setActiveKnowledgeBase = useKnowledgeBaseStore(
    (state) => state.setActiveKnowledgeBase,
  );
  const user = useUserStore((state) => state.user);
  const location = useLocation();

  const fetchFolders = async () => {
    const data = await getFolders({ page: 1, page_size: PAGE_SIZE });
    setFolders(data.results);
  };

  const fetchChatBots = async () => {
    try {
      setLoading(true);
      const response = await getAllAgents({
        page: 1,
        page_size: PAGE_SIZE,
      });
      const pages = response?.count;
      setTotalPages(Math.ceil(pages / PAGE_SIZE));
      setChatBots(response.results || []);
    } catch (error) {
      console.error("Failed to fetch chats:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChatBots();
    fetchFolders();
  }, [location]);

  setActiveKnowledgeBase(null, location.pathname);

  return (
    <>
      <AppHeader />
      <div className="flex-1 flex bg-sidebar">
        <SidebarProvider defaultOpen className="w-auto bg-card">
          <AppSidebar fetchChats={fetchChatBots} loading={loading} chats={[]} />
        </SidebarProvider>
        {/* Content area */}
        <div className="flex flex-1 bg-sidebar">
          <div className="absolute m-6 text-2xl font-semibold">Workspace</div>
          <div
            className={cn(
              "flex w-full",
              !loading &&
              chatBots.length === 0 &&
              "items-center justify-center",
            )}
          >
            {/* Show skeleton loader while loading */}
            {loading ? (
              <div className="flex w-full flex-col items-center justify-center space-y-4">
                {/* Skeletons for various elements */}
                <Skeleton className="h-12 w-1/2" />
                <Skeleton className="h-6 w-2/3" />
                <Skeleton className="h-6 w-1/3" />
              </div>
            ) : chatBots?.length !== 0 ? (
              <ChatPage
                chats={chatBots}
                fetchChats={fetchChatBots}
                totalPages={totalPages}
                setTotalPages={setTotalPages}
              />
            ) : (
              <Card className="flex h-80 w-1/2 flex-col items-center justify-center rounded-xl shadow-xl">
                <MessageSquarePlus color="#B0C7F8" size={52} className="pt-2" />
                <CardHeader className="mb-2">
                  <CardTitle className="text-md text-center font-[600] lg:text-2xl">
                    Hey {user?.username || user?.email}, Let's Get Started!
                  </CardTitle>
                  <CardDescription className="px-10 pt-2 text-center font-semibold">
                    Create your first agent to start chatting.<br />
                    Agents follow your instructions and help you with tasks or questions.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CreateChatDialog
                    activeKnowledgeBase={false}
                    triggerText="Create Agent"
                  />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default Chat;
