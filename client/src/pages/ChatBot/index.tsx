import { useEffect, useState } from "react";
import { useParams } from "react-router";

import EmbedChatBotPage from "../../components/ChatBot/Dashboard/EmbedChatBotPage";

import LoadingScreen from "../../components/ChatBot/Dashboard/ErrorAndLoader/LoadingScreen";
import KnowledgeBaseNotFound from "../../components/ChatBot/Dashboard/ErrorAndLoader/KnowledgeBaseNotFound";
import Unauthorized from "../../components/ChatBot/Dashboard/ErrorAndLoader/Unauthorized";
import GenericError from "../../components/ChatBot/Dashboard/ErrorAndLoader/GenericError";

import { getChatBotConfig } from "../../api";

import {
  defaultChatBotColors,
  EMBED_CHATBOT_ERRORS,
  ErrorType,
} from "../../lib/utils";

export type ChatBotConfig = {
  botName: string;
  avatar: {
    bg: string;
    icon: string;
  };
  selectedColor: string;
  domain: string;
  isExposed: boolean;
};

export default function ChatBot() {
  const { agentId } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ErrorType | null>(null);
  const [botConfig, setBotConfig] = useState<ChatBotConfig | null>(null);

  useEffect(() => {
    if (!agentId) {
      setError(EMBED_CHATBOT_ERRORS.KNOWLEDGE_BASE_NOT_FOUND);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await getChatBotConfig(agentId);
        const data = response.data;

        // Mapping snake_case to camelCase
        const mappedConfig: ChatBotConfig = {
          botName: data.agent_name,
          avatar: { bg: data.avatar_color, icon: data.agent_avatar },
          selectedColor: data.color_theme || defaultChatBotColors[3],
          domain: data.authorized_domain || "",
          isExposed: data.is_exposed || false,
        };

        setBotConfig(mappedConfig);
      } catch (err: any) {
        if (err.response) {
          const { status } = err.response;
          if (status === 404) {
            setError(EMBED_CHATBOT_ERRORS.KNOWLEDGE_BASE_NOT_FOUND);
          } else if (status === 401) {
            setError(EMBED_CHATBOT_ERRORS.UNAUTHORIZED);
          } else {
            setError(EMBED_CHATBOT_ERRORS.GENERIC_ERROR);
          }
        } else {
          setError(EMBED_CHATBOT_ERRORS.GENERIC_ERROR);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [agentId]);

  if (loading) {
    return <LoadingScreen />;
  }

  if (error) {
    switch (error?.code) {
      case "KNOWLEDGE_BASE_NOT_FOUND":
        return <KnowledgeBaseNotFound to={`/`} />;
      case "UNAUTHORIZED":
        return <Unauthorized to={`/`} />;
      default:
        return <GenericError to={`/`} />;
    }
  }

  return <EmbedChatBotPage agentId={agentId!} botConfig={botConfig!} />;
}
