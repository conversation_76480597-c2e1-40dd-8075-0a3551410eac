import { useState } from "react";
import { useNavigate } from "react-router";
import { Plus, Upload } from "lucide-react";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { toast } from "sonner";
import { createSummary } from "../../api";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "../../components/ui/dialog";

interface CreateSummaryModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSuccess?: () => void;
}

export default function CreateSummaryModal({
    open,
    onOpenChange,
    onSuccess
}: CreateSummaryModalProps) {
    const defaultPrompt = `Generate a constituency development report that is well-structured, evidence-based, and focused on local issues. Keep the tone empathetic but factual, and write with a sense of urgency and purpose. Your audience includes policymakers, political leaders, media, and engaged citizens.

Follow these rules:

Use plain text only (no special formatting).

Use the five section headings below in the exact order.

Do not add extra commentary or summaries.

Leave one blank line between bullets or paragraphs.

Section Guidelines:

MAJOR PUBLIC ISSUES
List clearly titled issues with 1–5 lines of factual description. Include specific impacts or affected groups. Use only issues mentioned in the context.

GROUND LEVEL HUMAN STORY (optional)
Include only if a real story is present. Describe a real case of hardship in 3–5 short paragraphs. Include name, age, village, etc., if available. Focus on emotion, detail, and systemic causes.

KEY PUBLIC DEMANDS
Bullet list format. Each demand must be specific, locally grounded, and based on the context. If a human story includes a public demand, you may include it here instead of Section 2.

UNFULFILLED DMK PROMISES 
Only include verifiable, constituency-level promises that are unfulfilled or partially completed. Start with a short title, then explain the promise, its status, and consequences with evidence.

SUGGESTED MANIFESTO PROMISES
For each major issue or demand, suggest a specific, actionable promise. Begin with a heading, then explain the solution, target group/area, and intended outcome in 1–3 lines.`;

    const navigate = useNavigate();
    const [name, setName] = useState("");
    const [prompt, setPrompt] = useState(defaultPrompt);
    const [isCreating, setIsCreating] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!name.trim()) {
            toast.error("Please enter a summary name");
            return;
        }

        try {
            setIsCreating(true);
            
            const summary = await createSummary({
                name: name.trim(),
                prompt: prompt.trim() || defaultPrompt,
            });

            toast.success("Document summary created successfully");

            // Reset form
            setName("");
            setPrompt("");

            // Close modal
            onOpenChange(false);

            // Call success callback if provided
            if (onSuccess) {
                onSuccess();
            }

            // Navigate to the new summary
            navigate(`/document-summarizer/${summary.id}`);
        } catch (error) {
            console.error("Failed to create summary:", error);
            toast.error("Failed to create document summary");
        } finally {
            setIsCreating(false);
        }
    };

    const handleCancel = () => {
        setName("");
        setPrompt("");
        onOpenChange(false);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle className="flex items-center">
                        <Plus size={20} className="mr-2" />
                        Create New Document Summary
                    </DialogTitle>
                    <DialogDescription>
                        Create a new document summary to analyze and extract insights from your documents.
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <label htmlFor="name" className="text-sm font-medium">
                            Summary Name *
                        </label>
                        <Input
                            id="name"
                            placeholder="Enter a name for your document summary"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <label htmlFor="prompt" className="text-sm font-medium">
                            Custom Prompt (Optional)
                        </label>
                        <Textarea
                            id="prompt"
                            placeholder="Enter a custom prompt for generating the summary..."
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            rows={8}
                        />
                        <p className="text-xs text-gray-500">
                            Leave empty to use the default political analyst prompt. You can customize this later.
                        </p>
                    </div>

                    {/* Info section */}
                    <div className="bg-blue-50 p-3 rounded-lg space-y-2">
                        <div className="flex items-start space-x-2">
                            <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                                <Upload size={12} className="text-blue-600" />
                            </div>
                            <div>
                                <h4 className="text-sm font-medium text-blue-900">Next Steps</h4>
                                <p className="text-xs text-blue-700">
                                    After creating the summary, you'll be able to upload documents and generate insights.
                                </p>
                            </div>
                        </div>
                    </div>
                </form>

                <DialogFooter>
                    <Button variant="outline" onClick={handleCancel}>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        disabled={isCreating || !name.trim()}
                        className="flex items-center"
                    >
                        {isCreating ? (
                            "Creating..."
                        ) : (
                            <>
                                <Plus size={16} className="mr-2" />
                                Create Summary
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 
