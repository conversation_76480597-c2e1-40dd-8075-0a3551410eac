import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router";
import { useEffect, useState, useRef } from "react";
import {
  getSummaryById,
  updateSummary,
  deleteSummary,
  uploadFile,
  getSummaryFiles,
  deleteSummaryFile,
  processSummary,
  DocumentSummary,
  DocumentSummaryFile
} from "../../api";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { Badge } from "../../components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import { Skeleton } from "../../components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { toast } from "sonner";
import {
  ArrowLeft,
  Upload,
  FileText,
  Play,
  Trash2,
  Edit,
  Save,
  X,
  Download,
  Sparkles
} from "lucide-react";
import Markdown from "../../components/shared/markdown";
import CopyButton from "../../components/shared/copy-button";
import { AlertDialog, AlertDialogContent } from "../../components/ui/alert-dialog";
import MarkdownWithHyperlinks from "../../components/MarkdownWithHyperlinks";
import SourceDocumentViewer from "../../components/SourceDocumentViewer";

export default function DocumentSummarizerPage() {
  const params = useParams();
  const navigate = useNavigate();
  const summaryId = params.id;

  const [summary, setSummary] = useState<DocumentSummary | null>(null);
  const [files, setFiles] = useState<DocumentSummaryFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editName, setEditName] = useState("");
  const [editPrompt, setEditPrompt] = useState("");
  const [editKeywords, setEditKeywords] = useState("");
  const [hyperlinks, setHyperlinks] = useState<Record<string, any>>({});
  const [showSourceViewer, setShowSourceViewer] = useState(false);
  const [selectedHyperlink, setSelectedHyperlink] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const defaultPrompt = `Generate a constituency development report that is well-structured, evidence-based, and focused on local issues. Keep the tone empathetic but factual, and write with a sense of urgency and purpose. Your audience includes policymakers, political leaders, media, and engaged citizens.

Follow these rules:

Use plain text only (no special formatting).

Use the five section headings below in the exact order.

Do not add extra commentary or summaries.

Leave one blank line between bullets or paragraphs.

Section Guidelines:

MAJOR PUBLIC ISSUES
List clearly titled issues with 1–5 lines of factual description. Include specific impacts or affected groups. Use only issues mentioned in the context.

GROUND LEVEL HUMAN STORY (optional)
Include only if a real story is present. Describe a real case of hardship in 3–5 short paragraphs. Include name, age, village, etc., if available. Focus on emotion, detail, and systemic causes.

KEY PUBLIC DEMANDS
Bullet list format. Each demand must be specific, locally grounded, and based on the context. If a human story includes a public demand, you may include it here instead of Section 2.

UNFULFILLED DMK PROMISES (optional)
Only include verifiable, constituency-level promises that are unfulfilled or partially completed. Start with a short title, then explain the promise, its status, and consequences with evidence.

SUGGESTED MANIFESTO PROMISES
For each major issue or demand, suggest a specific, actionable promise. Begin with a heading, then explain the solution, target group/area, and intended outcome in 1–3 lines.`;

  const fetchSummary = async () => {
    if (!summaryId) return;

    try {
      setLoading(true);
      const summaryData = await getSummaryById(summaryId);
      setSummary(summaryData);
      setEditName(summaryData.name);
      setEditPrompt(summaryData.prompt || defaultPrompt);
    } catch (error) {
      console.error("Failed to fetch summary:", error);
      toast.error("Failed to fetch summary");
    } finally {
      setLoading(false);
    }
  };

  const fetchFiles = async () => {
    if (!summaryId) return;

    try {
      const filesData = await getSummaryFiles(summaryId);
      setFiles(filesData);
    } catch (error) {
      console.error("Failed to fetch files:", error);
    }
  };

  const handleUpdateSummary = async () => {
    if (!summaryId) return;

    try {
      const updatedSummary = await updateSummary(summaryId, {
        name: editName,
        prompt: editPrompt || undefined,
      });
      setSummary(updatedSummary);
      setIsEditing(false);
      toast.success("Summary updated successfully");
    } catch (error) {
      console.error("Failed to update summary:", error);
      toast.error("Failed to update summary");
    }
  };

  const handleDeleteSummary = async () => {
    if (!summaryId) return;

    try {
      setIsDeleting(true);
      await deleteSummary(summaryId);
      toast.success("Summary deleted successfully");
      navigate("/document-summarizer");
    } catch (error) {
      console.error("Failed to delete summary:", error);
      toast.error("Failed to delete summary");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const handleFileUpload = async (files: FileList | null) => {
    if (!summaryId || !files || files.length === 0) return;
    setIsUploading(true);
    try {
      await Promise.all(
        Array.from(files).map(async (file) => {
          await uploadFile(summaryId, file);
        })
      );
      toast.success("Files uploaded successfully");
      fetchFiles();
    } catch (error) {
      console.error("Failed to upload files:", error);
      toast.error("Failed to upload one or more files");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    if (!summaryId) return;

    if (!confirm("Are you sure you want to delete this file?")) {
      return;
    }

    try {
      await deleteSummaryFile(summaryId, fileId);
      toast.success("File deleted successfully");
      fetchFiles();
    } catch (error) {
      console.error("Failed to delete file:", error);
      toast.error("Failed to delete file");
    }
  };

  const handleProcessSummary = async () => {
    if (!summaryId || !summary) return;

    try {
      setIsProcessing(true);
      const result = await processSummary(
        summaryId,
        editPrompt.trim() || defaultPrompt,
        editKeywords || undefined
      );

      if (result.summary) {
        setSummary({
          ...summary,
          summary: result.summary,
          updated_at: new Date().toISOString()
        });

        // Set hyperlinks if available
        if (result.hyperlinks) {
          setHyperlinks(result.hyperlinks);
        }
      }

      toast.success("Summary generated successfully");
    } catch (error) {
      console.error("Failed to generate summary:", error);
      toast.error("Failed to generate summary");
    } finally {
      setIsProcessing(false);
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "Unknown";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
  };

  const handleHyperlinkClick = (hyperlinkData: any) => {
    setSelectedHyperlink(hyperlinkData);
    setShowSourceViewer(true);
  };

  const handleDownloadFile = (file: any) => {
    // In a real implementation, this would download the file
    console.log('Download file:', file);
    // You could implement actual file download here
  };

  useEffect(() => {
    fetchSummary();
    fetchFiles();
  }, [summaryId]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center space-x-4 mb-6">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-24" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-96 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Summary not found</h2>
          <Link to="/document-summarizer">
            <Button >
              <ArrowLeft size={16} className="mr-2" />
              Back to Summaries
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link to="/document-summarizer">
            <Button variant="outline">
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
          </Link>
          <div>
            {isEditing ? (
              <Input
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                className="text-2xl font-bold"
              />
            ) : (
              <h1 className="text-2xl font-bold">{summary.name}</h1>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {isEditing ? (
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleUpdateSummary}>
                <Save size={16} className="mr-1" />
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={() => setIsEditing(false)}>
                <X size={16} className="mr-1" />
                Cancel
              </Button>
            </div>
          ) : (
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
                <Edit size={16} className="mr-1" />
                Edit
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowDeleteDialog(true)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 size={16} className="mr-1" />
                Delete
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1 min-h-0">
        {/* Left Side - Input Section */}
        <div className="flex flex-col min-h-0 flex-1">
          <div className="space-y-6 flex-1 flex flex-col">
            {/* Prompt Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles size={20} />
                  <span>Custom Prompt</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  id="prompt"
                  placeholder="Enter your custom prompt here..."
                  value={editPrompt}
                  onChange={(e) => setEditPrompt(e.target.value)}
                  rows={8}
                  className="resize-none"
                />
              </CardContent>
            </Card>

            {/* Keywords Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText size={20} />
                  <span>Important Keywords</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Input
                  value={editKeywords}
                  onChange={(e) => setEditKeywords(e.target.value)}
                  placeholder="Enter important keywords separated by commas (e.g., 'water shortage, dam leakage, infrastructure')"
                  className="w-full"
                />
                <p className="text-sm text-gray-500 mt-2">
                  These keywords will help focus the analysis on specific topics
                </p>
              </CardContent>
            </Card>

            {/* File Upload Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload size={20} />
                  <span>Upload Documents</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* File Upload Area */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.docx,.doc,.txt"
                    multiple
                    className="hidden"
                    onChange={(e) => handleFileUpload(e.target.files)}
                  />
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="w-full"
                  >
                    <Upload size={16} className="mr-2" />
                    {isUploading ? "Uploading..." : "Choose Files"}
                  </Button>
                  <p className="text-sm text-gray-500 mt-2">
                    Supported formats: PDF, DOCX, DOC, TXT
                  </p>
                </div>

                {/* Files List */}
                {files.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3">Uploaded Files ({files.length})</h4>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {files.map((file) => (
                        <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
                          <div className="flex items-center space-x-3">
                            <FileText size={20} className="text-blue-500" />
                            <div>
                              <p className="text-sm font-medium">{file.name}</p>
                              <div className="flex items-center space-x-2 text-xs text-gray-500">
                                <Badge variant="outline">{file.type}</Badge>
                                <span>{formatFileSize(file.size)}</span>
                              </div>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          {/* Generate Button at the bottom */}
          <div className="pt-6">
            <Button
              onClick={handleProcessSummary}
              disabled={isProcessing || files.length === 0}
              className="w-full h-12 text-lg"
              size="lg"
            >
              <Play size={20} className="mr-2" />
              {isProcessing ? "Generating..." : "Generate Summary"}
            </Button>
          </div>
        </div>

        {/* Right Side - Output Section */}
        <div className="space-y-6 flex flex-col min-h-0 flex-1">
          {/* Generated Summary */}
          <Card className="flex flex-col flex-1 min-h-0">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText size={20} />
                  <span>Generated Summary</span>
                </div>
                {summary.summary && (
                  <CopyButton data={summary.summary} />
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col flex-1 min-h-0">
              {summary.summary ? (
                <div className="flex flex-col flex-1 min-h-0 space-y-4">
                  <div className="flex-1 min-h-0 overflow-y-auto border rounded-lg p-4 bg-white">
                    <MarkdownWithHyperlinks
                      content={summary.summary}
                      hyperlinks={hyperlinks}
                      onSourceView={handleHyperlinkClick}
                    />
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>Generated on {summary.updated_at ? new Date(summary.updated_at).toLocaleDateString() : 'Unknown'}</span>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-64 text-center">
                  <FileText size={48} className="text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-2">No summary generated yet</p>
                  <p className="text-sm text-gray-400">
                    Upload files and click "Generate Summary" to create your first summary
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Summary</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{summary.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteSummary}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Generating summary dialog */}
      <AlertDialog open={isProcessing}>
        <AlertDialogContent className="flex flex-col items-center justify-center py-10">
          <div className="flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
            </svg>
            <span className="text-lg font-semibold text-gray-800">Generating Summary...</span>
          </div>
          <p className="text-gray-500 text-center max-w-sm">
            Your summary is being generated. This may take a few moments depending on the size of the documents uploaded.
          </p>
        </AlertDialogContent>
      </AlertDialog>

      {/* Source Document Viewer */}
      <SourceDocumentViewer
        isOpen={showSourceViewer}
        onClose={() => setShowSourceViewer(false)}
        hyperlinkData={selectedHyperlink}
        sourceFiles={files}
        onDownloadFile={handleDownloadFile}
      />

    </div>
  );
}
