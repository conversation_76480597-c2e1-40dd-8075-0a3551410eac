import { <PERSON>Text, Trash2, Download, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "../../components/ui/button";
import { Badge } from "../../components/ui/badge";
import { DocumentSummaryFile } from "../../api";

interface FileListProps {
  files: DocumentSummaryFile[];
  onDeleteFile: (fileId: string) => void;
  onDownloadFile?: (fileId: string) => void;
}

export default function FileList({ files, onDeleteFile, onDownloadFile }: FileListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Success":
        return "bg-green-100 text-green-800";
      case "Processing":
        return "bg-blue-100 text-blue-800";
      case "Failed":
        return "bg-red-100 text-red-800";
      case "Queue":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Success":
        return <CheckCircle size={16} />;
      case "Processing":
        return <Clock size={16} />;
      case "Failed":
        return <AlertCircle size={16} />;
      default:
        return <Clock size={16} />;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "Unknown";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
  };

  if (files.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText size={48} className="text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500 mb-2">No files uploaded yet</p>
        <p className="text-sm text-gray-400">
          Upload your first document to get started
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {files.map((file) => (
        <div
          key={file.id}
          className="flex items-center justify-between p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <FileText size={24} className="text-blue-500" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {file.name}
                </p>
                <Badge variant="outline" className="text-xs">
                  {file.type}
                </Badge>
              </div>
              <div className="flex items-center space-x-3 text-xs text-gray-500">
                <span>{formatFileSize(file.size)}</span>
                {file.meta_data && Object.keys(file.meta_data).length > 0 && (
                  <span>• {Object.keys(file.meta_data).length} metadata fields</span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge className={getStatusColor(file.status)}>
              {getStatusIcon(file.status)}
              <span className="ml-1 text-xs">{file.status}</span>
            </Badge>

            <div className="flex items-center space-x-1">
              {onDownloadFile && file.url && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onDownloadFile(file.id)}
                  className="text-blue-500 hover:text-blue-700"
                >
                  <Download size={14} />
                </Button>
              )}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onDeleteFile(file.id)}
                className="text-red-500 hover:text-red-700"
              >
                <Trash2 size={14} />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
