import { MessageSquarePlus, Plus, Search, FileText, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { AppSidebar } from "../../components/SidebarComponent/AppSidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { SidebarProvider } from "../../components/ui/sidebar";
import { useEffect, useState } from "react";
import { getSummaries, deleteSummary } from "../../api";
import { Skeleton } from "../../components/ui/skeleton";
import { cn, PAGE_SIZE } from "../../lib/utils";
import { useUserStore } from "../../store/userStore";
import { Link, useLocation } from "react-router";
import AppHeader from "../../components/AppHeaderComponent";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Badge } from "../../components/ui/badge";
import { toast } from "sonner";
import { Trash2, Edit } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import CreateSummaryModal from "./CreateSummaryModal";

interface DocumentSummary {
  id: string;
  name: string;
  status: string;
  summary?: string;
  prompt?: string;
  updated_at: string;
  files_count?: number;
}

const DocumentSummarizer = () => {
  const [summaries, setSummaries] = useState<DocumentSummary[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [summaryToDelete, setSummaryToDelete] = useState<DocumentSummary | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const user = useUserStore((state) => state.user);
  const location = useLocation();

  const fetchSummaries = async () => {
    try {
      setLoading(true);
      const response = await getSummaries({
        page: 1,
        page_size: PAGE_SIZE,
        search: searchQuery,
      });
      setSummaries(response.results || []);
    } catch (error) {
      console.error("Failed to fetch summaries:", error);
      toast.error("Failed to fetch summaries");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSummaries();
  }, [location, searchQuery]);

  const handleDeleteSummary = async () => {
    if (!summaryToDelete) return;

    try {
      setIsDeleting(true);
      await deleteSummary(summaryToDelete.id);
      toast.success("Summary deleted successfully");
      fetchSummaries();
    } catch (error) {
      console.error("Failed to delete summary:", error);
      toast.error("Failed to delete summary");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setSummaryToDelete(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle size={16} />;
      case "in_progress":
        return <Clock size={16} />;
      case "failed":
        return <AlertCircle size={16} />;
      default:
        return <Clock size={16} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const filteredSummaries = summaries.filter(summary =>
    summary.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <AppHeader />
      <div className="flex-1 flex bg-sidebar">
        <SidebarProvider defaultOpen className="w-auto bg-card">
          <AppSidebar fetchChats={() => { }} loading={loading} chats={[]} />
        </SidebarProvider>
        {/* Content area */}
        <div className="flex flex-1 bg-sidebar">
          <div className="w-full p-6">
            {/* Header */}
            <div className="mb-6">
              <div className="text-2xl font-semibold mb-4">Document Summarizer</div>
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <Input
                    placeholder="Search summaries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-full"
                  />
                </div>
                <Button onClick={() => setShowCreateModal(true)}>
                  <Plus size={16} className="mr-2" />
                  Create New Summary
                </Button>
              </div>
            </div>

            {/* Content */}
            <div
              className={cn(
                "flex w-full",
                !loading &&
                filteredSummaries.length === 0 &&
                "items-center justify-center",
              )}
            >
              {/* Show skeleton loader while loading */}
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="h-full min-h-48">
                      <CardHeader>
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-20 w-full" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : filteredSummaries?.length !== 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
                  {filteredSummaries.map((summary) => (
                    <Card key={summary.id} className="h-full min-h-48 hover:shadow-lg transition-shadow cursor-pointer">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg font-semibold line-clamp-2">
                              {summary.name}
                            </CardTitle>
                            <CardDescription className="text-sm text-gray-500 mt-1">
                              {formatDate(summary.updated_at)}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <FileText size={16} />
                            <span>{summary.files_count || 0} files</span>
                          </div>
                          {summary.summary && (
                            <p className="text-sm text-gray-700 line-clamp-3">
                              {summary.summary}
                            </p>
                          )}
                          <div className="flex items-center justify-between pt-2">
                            <Link to={`/document-summarizer/${summary.id}`}>
                              <Button size="sm" variant="outline">
                                View Details
                              </Button>
                            </Link>
                            <div className="flex items-center space-x-1">
                              <Link to={`/document-summarizer/${summary.id}`}>
                                <Button size="sm" variant="ghost">
                                  <Edit size={14} />
                                </Button>
                              </Link>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setSummaryToDelete(summary);
                                  setShowDeleteDialog(true);
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 size={14} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="flex h-80 w-1/2 flex-col items-center justify-center rounded-xl shadow-xl">
                  <FileText color="#B0C7F8" size={52} className="pt-2" />
                  <CardHeader className="mb-2">
                    <CardTitle className="text-md text-center font-[600] lg:text-2xl">
                      Hey {user?.username || user?.email}, Let's Get Started!
                    </CardTitle>
                    <CardDescription className="px-10 pt-2 text-center font-semibold">
                      Start by creating a document summary to analyze and extract insights from your documents
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="default" onClick={() => setShowCreateModal(true)}>
                      <Plus />
                      Create Document Summary
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Summary</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{summaryToDelete?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteSummary}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Summary Modal */}
      <CreateSummaryModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onSuccess={fetchSummaries}
      />
    </>
  );
};

export default DocumentSummarizer;
