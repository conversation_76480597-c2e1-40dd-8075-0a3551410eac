import { useState } from "react";
import { sendVerificationEmail } from "../api";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "../components/ui/card";
import { AnimatePresence, motion } from "motion/react";
import { Button } from "../components/ui/button";
import { Loader2 } from "lucide-react";

import BlingIcon from "../assets/bling_logo.svg";
import { useToast } from "../hooks/use-toast";
import { useAuthStore } from "../store/authStore";
import { useNavigate } from "react-router";
import { useUserStore } from "../store/userStore";

export default function EmailNotVerified({ userEmail }: { userEmail: string }) {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const logout = useAuthStore((state) => state.logout);
  const removeUserData = useUserStore((state) => state.removeUserData);

  const handleResendEmail = async () => {
    setIsLoading(true);
    try {
      await sendVerificationEmail();
      toast({
        description: "Successfully resent verification email.",
      });
    } catch (err) {
      toast({
        variant: "destructive",
        description:
          "Failed to resend verification email. Please try again later",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToSignUp = () => {
    logout();
    removeUserData();
    navigate("/signup", { replace: true });
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        className="flex min-h-screen items-center justify-center bg-sidebar"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <Card className="mx-auto w-full max-w-[400px] rounded-[30px] shadow-lg">
          <CardHeader className="space-y-5">
            <div className="flex items-center justify-center gap-2">
              <img src={BlingIcon} />
              <h1 className="text-xl font-semibold">
                Buddhi<span className="text-primary">AI</span>
              </h1>
            </div>
            <h2 className="text-center text-xl font-semibold tracking-tight">
              Verify your email address
            </h2>
          </CardHeader>

          <CardContent className="space-y-4">
            <motion.div className="flex flex-col gap-6">
              <p className="text-center text-gray-600">
                We have sent a verification link to{" "}
                <span className="font-bold">{userEmail}</span>
              </p>

              <Button
                type="submit"
                onClick={handleResendEmail}
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="mr-[2px] h-4 w-4 animate-spin" />
                ) : null}
                {isLoading ? "Resending" : "Resend Email"}
              </Button>
              <Button
                className="-mt-3 w-full rounded-lg border border-primary bg-transparent py-3 font-semibold text-primary transition-colors hover:bg-zinc-100"
                onClick={handleBackToSignUp}
              >
                Back to Sign up
              </Button>
            </motion.div>
          </CardContent>

          <CardFooter className="flex flex-col gap-1 text-center text-xs text-gray-600">
            <span>Click on the link to complete the verification process.</span>
            <span>You might need to check your spam folder. </span>
            <span>Link is valid for 15 minutes.</span>
          </CardFooter>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
