import { useNavigate } from "react-router";
import BlingIcon from "../../assets/bling_logo.svg";
import { Button } from "../../components/ui/button";

const GeneralNotFound = () => {
  const navigate = useNavigate();
  return (
    <div className="flex h-screen items-center justify-center bg-sidebar">
      <div className="flex h-1/2 max-w-[40rem] flex-col items-center justify-center gap-2 rounded-xl bg-white px-12 py-10 shadow-md">
        <div className="mb-4 flex items-center justify-center gap-2">
          <img src={BlingIcon} />
          <h1 className="text-xl font-semibold">
            Buddhi<span className="text-primary">AI</span>
          </h1>
        </div>
        <h1 className="text-xl font-semibold">Oops! Something Went Wrong</h1>
        <span className="text-center text-gray-600">
          An unexpected error occurred. This could be due to a temporary issue
          or an unanticipated condition in the system. Please refresh the page
          or try again later. If the issue persists, contact support for
          assistance
        </span>
        <div className="flex w-full flex-col gap-2 px-4 pt-4">
          <Button className="py-6" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
          <Button
            variant="outline"
            className="border border-2 border-primary py-6 text-primary hover:text-primary"
            onClick={() => navigate("/")}
          >
            Go Back
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GeneralNotFound;
