import { useNavigate } from "react-router";
import BlingIcon from "../../assets/bling_logo.svg";
import { Button } from "../../components/ui/button";

const KnowledgeBaseNotFound = () => {
  const navigate = useNavigate();
  return (
    <div className="flex h-screen items-center justify-center bg-sidebar">
      <div className="flex h-1/2 max-w-[40rem] flex-col items-center justify-center gap-2 rounded-xl bg-white px-12 py-10 shadow-md">
        <div className="mb-4 flex items-center justify-center gap-2">
          <img src={BlingIcon} />
          <h1 className="text-xl font-semibold">
            Buddhi<span className="text-primary">AI</span>
          </h1>
        </div>
        <h1 className="text-xl font-semibold">Knowledge Base Does Not Exist</h1>
        <span className="text-center text-gray-600">
          The requested knowledge base could not be found. It seems like it has
          not been added or created yet. Please create a new knowledge base to
          proceed.
        </span>
        <div className="flex w-full flex-col gap-2 px-4 pt-4">
          <Button
            variant="outline"
            className="border border-2 border-primary py-6 text-primary hover:text-primary"
            onClick={() => navigate(-1)}
          >
            Go Back
          </Button>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBaseNotFound;
