import React, { useEffect, useState } from "react";
import { forgotPassword } from "../api";
import { Card, CardContent, CardHeader } from "../components/ui/card";
import { AnimatePresence, motion } from "motion/react";
import { Input } from "../components/ui/input";
import { Button } from "../components/ui/button";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router";

import BlingIcon from "../assets/bling_logo.svg";
import { useAuthStore } from "../store/authStore";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);

  const navigate = useNavigate();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    if (isAuthenticated || checkAuth()) {
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, checkAuth]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      await forgotPassword(email);
      setIsSuccess(true);
    } catch (err) {
      setError("Failed to send reset link. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-sidebar">
      <Card className="mx-auto w-full max-w-[400px] rounded-[30px] shadow-lg">
        <CardHeader className="space-y-5">
          <div className="flex items-center justify-center gap-2">
            <img src={BlingIcon} />
            <h1 className="text-xl font-semibold">
              Buddhi<span className="text-primary">AI</span>
            </h1>
          </div>
          <h2 className="text-center text-xl font-semibold tracking-tight">
            Forgot password?
          </h2>
        </CardHeader>

        <CardContent className="space-y-4">
          <AnimatePresence mode="wait">
            {!isSuccess ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-9"
              >
                <p className="text-center text-gray-600">
                  Enter your e-mail address below, and we'll send you an e-mail
                  allowing you to reset it.
                </p>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full"
                      placeholder="Enter your email"
                    />
                  </div>

                  {error && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center text-sm text-red-500"
                    >
                      {error}
                    </motion.p>
                  )}

                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    Reset Password
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => navigate("/login")}
                  >
                    Log In
                  </Button>
                </form>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4 py-4 text-center"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="mb-4 text-4xl text-green-500"
                >
                  ✓
                </motion.div>
                <h3 className="text-lg font-medium">Check your email</h3>
                <p className="text-gray-600">
                  We've sent a password reset link to your email address. Please
                  check your inbox and follow the instructions.
                </p>
                <Button
                  type="button"
                  variant="outline"
                  className="mt-4 w-full"
                  onClick={() => navigate("/login")}
                >
                  Back to Log In
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </div>
  );
}
