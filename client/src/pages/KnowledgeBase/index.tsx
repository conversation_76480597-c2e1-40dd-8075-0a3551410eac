import React, { useState, useEffect } from "react";
import { Database, Plus } from "lucide-react";
import { AppSidebar } from "../../components/SidebarComponent/AppSidebar";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { SidebarProvider } from "../../components/ui/sidebar";
import { Button } from "../../components/ui/button";
import {
  DialogHeader,
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import KnowledgeBaseExplorer from "../../components/KnowledgeBaseExplorerComponent";
import { Folder } from "../../components/KnowledgeBaseExplorerComponent";
import { createFolder, getFolders } from "../../api";
import { useToast } from "../../hooks/use-toast";
import { Skeleton } from "../../components/ui/skeleton";
import { cn, PAGE_SIZE } from "../../lib/utils";
import { useLocation, useNavigate } from "react-router";
import AppHeader from "../../components/AppHeaderComponent";

const KnowledgeBase = () => {
  const [newKnowledgeBase, setNewKnowledgeBase] = useState("");
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [totalPages, setTotalPages] = useState<number>(Infinity);
  const [isFetching, setIsFetching] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  const fetchFolders = async () => {
    setIsFetching(true);
    try {
      const data = await getFolders({ page: 1, page_size: PAGE_SIZE });
      setFolders(data.results);
      setTotalPages(Math.ceil(data.count / PAGE_SIZE));
    } catch {
      toast({
        variant: "destructive",
        description: "Error fetching folders",
      });
      setFolders([]);
    } finally {
      setIsFetching(false);
    }
  };

  const folderCreateHandler = async () => {
    setLoading(true);
    try {
      const result = await createFolder(newKnowledgeBase, "");
      navigate(`/knowledgeBase/${newKnowledgeBase}/${result.id}`);
      setDialogOpen(false);
      fetchFolders();
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = (e: React.SyntheticEvent<HTMLFormElement>) => {
    e.preventDefault();
    folderCreateHandler();
  };

  useEffect(() => {
    fetchFolders();
  }, []);

  return (
    <>
      <AppHeader />
      <div className="flex-1 flex">
        <SidebarProvider defaultOpen className="w-auto bg-card">
          <AppSidebar />
        </SidebarProvider>

        <div
          className={cn(
            "flex w-full justify-center",
            !isFetching && folders.length === 0 && "items-center",
          )}
        >
          {isFetching ? (
            <div className="w-full space-y-6 p-10">
              {/* Skeleton for search field */}
              <Skeleton className="mb-4 h-10 w-1/3 rounded-md" />

              {/* Skeleton for title */}
              <Skeleton className="mb-4 h-6 w-1/4 rounded-md" />

              {/* Skeleton for button */}
              <Skeleton className="mb-4 h-10 w-1/5 rounded-md" />

              {/* Skeleton loaders for cards */}
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <Skeleton className="h-40 w-full rounded-lg" />
                <Skeleton className="h-40 w-full rounded-lg" />
                <Skeleton className="h-40 w-full rounded-lg" />
                <Skeleton className="h-40 w-full rounded-lg" />
              </div>
            </div>
          ) : folders.length === 0 ? (
            <Card className="flex h-1/3 w-1/2 flex-col items-center justify-center rounded-xl shadow-[0_-1px_100px_0_rgba(0,0,0,0.10)]">
              <Database color="#B0C7F8" size={48} className="mt-2" />
              <CardHeader className="mb-2">
                <CardTitle className="text-md text-center font-[600] lg:text-2xl">
                  Create Your Own Knowledge Base
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogTrigger className="w-full" asChild>
                    <Button className="w-full" disabled={loading}>
                      <Plus className="ml-auto" />
                      {loading ? "Creating..." : "Create New"}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="font-bold">
                        New Folder
                      </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleFormSubmit}>
                      <Input
                        type="text"
                        placeholder="Folder Name"
                        className="mt-4 border-[1px] border-black/30 py-6 font-semibold focus-visible:ring-transparent"
                        value={newKnowledgeBase}
                        onChange={(e) => setNewKnowledgeBase(e.target.value)}
                        required
                      />

                      <div className="flex justify-end gap-2 pt-2">
                        <DialogClose asChild>
                          <Button variant="ghost" disabled={loading}>
                            Cancel
                          </Button>
                        </DialogClose>
                        <Button disabled={loading}>
                          <Plus className="ml-auto" />
                          {loading ? "Creating..." : "Create"}
                        </Button>
                      </div>
                    </form>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          ) : (
            <div className="flex flex-col w-full">
              <KnowledgeBaseExplorer
                initialFolders={folders}
                totalPages={totalPages}
                setTotalPages={setTotalPages}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default KnowledgeBase;
