import { useParams } from "react-router";
import { AppSidebar } from "../../components/SidebarComponent/AppSidebar";
import { SidebarProvider } from "../../components/ui/sidebar";
import { FileUploader } from "../../components/FileUploadComponent";
import { <PERSON><PERSON> } from "../../components/ui/button";
// import GoogleDriveLogo from "../../assets/googleDriveLogo.svg?react";
import { Separator } from "../../components/ui/separator";
import { ChevronLeft, Link2, Plus, Upload } from "lucide-react";
import { useState, useEffect } from "react";
import FileExplorer, { Status } from "../../components/FileExplorerComponent";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from "../../components/ui/dropdown-menu";
import GoogleDriveDialog from "../../components/FileExplorerComponent/GoogleDriveDialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "../../components/ui/dialog";
import {
  getNotDeletedFiles,
  createFile,
  deleteFile,
  createUrl,
} from "../../api/";
import { useToast } from "../../hooks/use-toast";
import { Skeleton } from "../../components/ui/skeleton";
import { PAGE_SIZE } from "../../lib/utils";
import AppHeader from "../../components/AppHeaderComponent";
import { AddUrlDialog } from "../../components/FileExplorerComponent/AddUrlDialog";

// Define types for file objects and useParams return
export type FileType = {
  name: string;
  id: string;
  size: string;
  type: "DRIVE" | "PDF";
  externalLink?: string;
  url?: string;
  status: Status;
  folderid: string;
};

const FolderPage = () => {
  const params = useParams();
  const [isUploaded, setIsUploaded] = useState(false);
  const [isGoogleDriveDialogOpen, setGoogleDriveDialogOpen] = useState(false);
  const [isFileDialogOpen, setFileDialogOpen] = useState(false);
  const [isUrlDialogOpen, setUrlDialogOpen] = useState(false);
  const [deleteInProgress, setDeleteInProgress] = useState(false);
  const [files, setFiles] = useState<FileType[]>([]);
  const [uploadInProgress, setUploadInProgress] = useState(false);
  const [fetchingFiles, setFetchingFiles] = useState(false);
  const [totalPages, setTotalPages] = useState(Infinity);
  const folderId = params.folderId || "default-folder-id";
  const { toast } = useToast();

  const fetchFiles = async () => {
    try {
      setFetchingFiles(true);
      const response = await getNotDeletedFiles(folderId, {
        page: 1,
        page_size: PAGE_SIZE,
      });
      const fetchedFiles: FileType[] = response.data?.results || [];
      const pages = response.data?.count;
      setFiles(fetchedFiles);
      setTotalPages(Math.ceil(pages / PAGE_SIZE));
      setIsUploaded(fetchedFiles.length > 0);
    } catch (error) {
      console.error("Error fetching files:", error);
      toast({
        variant: "destructive",
        description: "Couldn't fetch files. Please try again later.",
      });
    } finally {
      setFetchingFiles(false);
    }
  };

  const updateLocalFiles = (files: File[]) => {
    const filesArray = files.map((file) => {
      return {
        name: file.name,
        id: file.name,
        size: file.size.toString(),
        type: "PDF" as "PDF",
        status: "QUEUED" as Status,
        folderid: folderId,
      };
    });
    setFiles((prevFiles) => [...prevFiles, ...filesArray]);
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      setDeleteInProgress(true);
      await deleteFile(folderId, fileId);
      setFiles((prevFiles) => prevFiles.filter((file) => file.id !== fileId));
      fetchFiles();
    } catch (error) {
      console.error("Error deleting file:", error);
      toast({
        variant: "destructive",
        description: "Couldn't delete file. Please try again later.",
      });
    } finally {
      setDeleteInProgress(false);
    }
  };

  const handleFileUpload = async (uploadedFiles: File[]) => {
    try {
      toast({
        description: "Uploading files",
      });
      setUploadInProgress(true);
      const uploadPromises = uploadedFiles.map(async (file) => {
        await createFile(folderId, file.name, file);
      });
      await Promise.all(uploadPromises);
      fetchFiles();
      setFileDialogOpen(false); // Close the dialog after successful upload
      toast({
        description: "Files uploaded successfully",
      });
    } catch (error) {
      console.error("Error uploading files:", error);
      toast({
        variant: "destructive",
        description: "Couldn't upload files. Please try again later.",
      });
    } finally {
      setUploadInProgress(false);
    }
  };

  const handleUrlSubmit = async (urlData: {
    url: string;
    crawlerType: string;
    maxRequests: number;
    crawlLinks: boolean;
    enqueueStrategy: string;
  }) => {
    try {
      toast({
        description: "Adding URL...",
      });

      const response = await createUrl(folderId, {
        url: urlData.url,
        meta_data: {
          crawler_type: urlData.crawlerType,
          max_requests: urlData.maxRequests,
          crawl_links: urlData.crawlLinks,
          enqueue_strategy: urlData.enqueueStrategy,
        },
      });

      if (response.data) {
        toast({
          description: "URL added successfully",
        });
        fetchFiles();
      }
    } catch (error) {
      console.error("Error adding URL:", error);
      toast({
        variant: "destructive",
        description: "Failed to add URL. Please try again.",
      });
    }
  };

  useEffect(() => {
    fetchFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [folderId]);

  return (
    <>
      <AppHeader />
      <div className="flex flex-1">
        <SidebarProvider defaultOpen className="w-auto bg-card">
          <AppSidebar />
        </SidebarProvider>

        <div className="flex w-full flex-col bg-sidebar">
          <div className="px-10 pt-4 text-xl font-bold">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ChevronLeft
                  onClick={() => window.history.back()}
                  size={22}
                  className="cursor-pointer"
                />
                {params.folderName}
              </div>

              {isUploaded && files.length !== 0 && (
                <div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button>
                        <Plus />
                        Add Files
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="cursor-pointer px-4 py-3"
                        onClick={() => setFileDialogOpen(true)} // Trigger the file dialog
                      >
                        <Upload />
                        Upload Files
                      </DropdownMenuItem>
                      {/* <DropdownMenuItem
                      className="cursor-pointer px-4 py-3"
                      onClick={() => setGoogleDriveDialogOpen(true)}
                    >
                      <GoogleDriveLogo />
                      Google Drive
                    </DropdownMenuItem> */}
                      <DropdownMenuItem
                        className="cursor-pointer px-4 py-3"
                        onClick={() => setUrlDialogOpen(true)}
                      >
                        <Link2 />
                        Add Website URL
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  <GoogleDriveDialog
                    isOpen={isGoogleDriveDialogOpen}
                    onClose={() => setGoogleDriveDialogOpen(false)}
                  />
                </div>
              )}
            </div>

            <Separator orientation="horizontal" className="mt-3" />
          </div>

          {fetchingFiles ? (
            <div className="flex flex-col gap-6 p-10">
              {/* Skeleton Loader */}
              <Skeleton className="h-12 w-full rounded-md" />
              <Skeleton className="h-12 w-full rounded-md" />
              <Skeleton className="h-12 w-full rounded-md" />
            </div>
          ) : files.length == 0 ? (
            <div className="flex h-full items-center justify-center">
              <div className="w-full max-w-2xl rounded-2xl bg-white p-10 shadow-xl">
                <div className="pb-6 text-center text-xl font-semibold">
                  Add files to your Knowledge Base
                </div>
                <div className="space-y-6">
                  <div>
                    <h3 className="mb-2 text-base font-medium">File Upload</h3>
                    <FileUploader
                      onValueChange={(uploadedFiles) =>
                        handleFileUpload(uploadedFiles)
                      }
                      multiple
                      maxSize={1024 * 1024 * 100}
                      uploadInProgress={uploadInProgress}
                    />
                  </div>

                  <div className="flex items-center justify-center gap-4">
                    <div className="h-px flex-1 bg-border"></div>
                    <span className="text-sm text-gray-500">OR</span>
                    <div className="h-px flex-1 bg-border"></div>
                  </div>

                  <div>
                    <h3 className="mb-2 text-base font-medium">
                      Upload from URL
                    </h3>
                    <Button
                      variant="outline"
                      className="w-full gap-3 rounded-xl border-[1.55px] border-black/30 py-6 font-semibold"
                      onClick={() => setUrlDialogOpen(true)}
                    >
                      <Link2 className="h-5 w-5" />
                      Add Website URL
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <FileExplorer
              folderId={folderId}
              initialFiles={files}
              totalPages={totalPages}
              setTotalPages={setTotalPages}
              deleteInProgress={deleteInProgress}
              fetchingFiles={fetchingFiles}
              onDeleteFile={handleDeleteFile}
            />
          )}

          {/* File Upload Dialog */}
          <Dialog open={isFileDialogOpen} onOpenChange={setFileDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Upload Files</DialogTitle>
                <DialogDescription>
                  Add your files here to store them in the knowledge base.
                </DialogDescription>
              </DialogHeader>
              <FileUploader
                onValueChange={(uploadedFiles) =>
                  handleFileUpload(uploadedFiles)
                }
                multiple
                maxSize={1024 * 1024 * 100}
                uploadInProgress={uploadInProgress}
              />
            </DialogContent>
          </Dialog>

          {/* URL Dialog */}
          <AddUrlDialog
            isOpen={isUrlDialogOpen}
            onClose={() => setUrlDialogOpen(false)}
            onSubmit={handleUrlSubmit}
          />
        </div>
      </div>
    </>
  );
};

export default FolderPage;
