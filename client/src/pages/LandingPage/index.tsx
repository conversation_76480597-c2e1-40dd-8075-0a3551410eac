import Header from "../../components/LandingPage/Header";
import HeroSection from "../../components/LandingPage/Hero";
import FeaturesSection from "../../components/LandingPage/FeaturesSection";
import HowItWorksSection from "../../components/LandingPage/HowItWorksSection";
import PricingSection from "../../components/LandingPage/PricingSection";
import GetStartedSection from "../../components/LandingPage/GetStartedSection";
import Footer from "../../components/LandingPage/Footer";

// Shared animation variants
export const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 },
};

export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  transition: { duration: 0.4 },
};

export default function LandingPage(){
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <HeroSection />
      <FeaturesSection />
      <HowItWorksSection />
      <PricingSection />
      <GetStartedSection />
      <Footer />
    </div>
  );
};
