import React, { ChangeEvent, useEffect, useState } from "react";
import { Link, useNavigate } from "react-router";
import { Input } from "../components/ui/input";
import { PasswordInput } from "../components/PasswordInputComponent";
import { Button } from "../components/ui/button";
import { useToast } from "../hooks/use-toast";
import GoogleButton from "../components/GoogleOAuth/GoogleButton";
import { useAuthStore } from "../store/authStore";
import { loginUser } from "../api";
import { statusCodes } from "../api/constants";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    if (isAuthenticated || checkAuth()) {
      navigate("/", { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, checkAuth]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await loginUser(email, password);
      if (response.status !== statusCodes.SUCCESS) {
        throw new Error("Login failed.");
      }
      login(response.data.access_token);
      navigate("/", { replace: true });
    } catch (err: any) {
      if (err?.response?.status === 401) {
        // Handle unauthorized error
        setError("Invalid username or password."); // For state management
        toast({
          variant: "destructive",
          description: "Invalid username or password.",
        });
      } else {
        // Handle other errors
        setError((err as Error).message || "An unknown error occurred.");
        toast({
          variant: "destructive",
          description:
            err?.response?.data?.details || "An unknown error occurred.",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-sidebar">
      <div className="w-[400px] rounded-xl bg-white px-8 py-10 shadow-sm">
        <header className="mb-6 text-center text-2xl font-[700]">
          <Link to="/">
            Psybe Labs <span className="text-primary">AI</span>
          </Link>
        </header>
        <h1 className="mb-6 text-center text-xl font-semibold">Log in</h1>
       
        <form onSubmit={handleLogin}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium">
              Email
            </label>
            <Input
              type="email"
              id="email"
              className="mt-1 w-full"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div className="my-6">
            <div className="flex items-center justify-between">
              <label htmlFor="password" className="block text-sm font-medium">
                Password
              </label>
              {/* <div>
                <Link to="/forgot-password" className="text-xs">
                  Forgot Password?
                </Link>
              </div> */}
            </div>
            <PasswordInput
              id="password"
              className="mt-1 w-full"
              placeholder="Enter your password"
              value={password}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setPassword(e.target.value)
              }
              min={6}
            />
          </div>
          {error && <p className="mb-4 text-sm text-red-500">{error}</p>}
          <Button
            type="submit"
            className={`w-full py-3 ${loading ? "opacity-50" : ""}`}
            disabled={loading}
          >
            {loading ? "Logging in..." : "Login"}
          </Button>
        </form>
        {/* <p className="mt-4 text-center text-sm font-semibold">
          Don't have an account?{" "}
          <Link to="/signup" className="text-primary">
            Sign up
          </Link>
        </p> */}
      </div>
    </div>
  );
};

export default Login;
