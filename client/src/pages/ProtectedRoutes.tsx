import { Navigate } from "react-router";
import { useAuthStore } from "../store/authStore";
import { useUserStore } from "../store/userStore";
import EmailNotVerified from "./EmailNotVerified";
import { useEffect } from "react";

interface ProtectedRouteProps {
  children: JSX.Element;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const { user, fetchUser, isLoading } = useUserStore();

  useEffect(() => {
    fetchUser();
  }, []);

  if (!isAuthenticated && !checkAuth()) {
    return <Navigate to="/login" replace />;
  }

  if (!user?.is_verified) {
    return <EmailNotVerified userEmail={user?.email!} />;
  }

  return <section className="flex h-screen flex-col">{children}</section>;
};

export default ProtectedRoute;
