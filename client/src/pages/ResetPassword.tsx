import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { useNavigate, useSearchParams } from "react-router";
import { Card, CardContent, CardHeader } from "../components/ui/card";
import BlingIcon from "../assets/bling_logo.svg";
import { useAuthStore } from "../store/authStore";
import { PasswordInput } from "../components/PasswordInputComponent";
import { Button } from "../components/ui/button";
import { Loader2 } from "lucide-react";
import { resetPassword } from "../api";

export default function ResetPassword() {
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token") || "";

  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const navigate = useNavigate();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    if (isAuthenticated || checkAuth()) {
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, checkAuth]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    setError("");
    setIsLoading(true);

    try {
      await resetPassword(token, formData.password);
      setIsSuccess(true);
    } catch (err: any) {
      setError(
        err.response?.data?.message ||
          "Failed to reset password. Please try again.",
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-sidebar">
      <Card className="mx-auto w-full max-w-[400px] rounded-[30px] shadow-lg">
        <CardHeader className="space-y-5">
          <div className="flex items-center justify-center gap-2">
            <img src={BlingIcon} />
            <h1 className="text-xl font-semibold">
              Buddhi<span className="text-primary">AI</span>
            </h1>
          </div>
          {!isSuccess && (
            <h2 className="text-center text-xl font-semibold tracking-tight">
              Reset password
            </h2>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          <AnimatePresence mode="wait">
            {!isSuccess ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-7"
              >
                <p className="text-center text-gray-600">
                  Please enter your new password below.
                </p>

                <form onSubmit={handleSubmit} className="space-y-5">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">New Password</label>
                    <PasswordInput
                      value={formData.password}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          password: e.target.value,
                        }))
                      }
                      required
                      className="w-full"
                      placeholder="Enter new password"
                      minLength={6}
                      autoComplete="new-password"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Confirm Password
                    </label>
                    <PasswordInput
                      value={formData.confirmPassword}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          confirmPassword: e.target.value,
                        }))
                      }
                      required
                      className="w-full"
                      placeholder="Confirm new password"
                      minLength={6}
                      autoComplete="new-password"
                    />
                  </div>

                  {error && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center text-sm text-red-500"
                    >
                      {error}
                    </motion.p>
                  )}

                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    Reset Password
                  </Button>
                </form>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-7 py-4 text-center"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="mb-4 text-4xl text-green-500"
                >
                  ✓
                </motion.div>
                <h3 className="text-lg font-medium">
                  Password Reset Successful
                </h3>
                <p className="text-gray-600">
                  Your password has been successfully changed. You can now log
                  in with your new password.
                </p>
                <Button
                  type="button"
                  className="mt-4 w-full bg-blue-600 hover:bg-blue-700"
                  onClick={() => navigate("/login", { replace: true })}
                >
                  Go to Login
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </div>
  );
}
