import React from "react";
import { Routes, Route } from "react-router";
import Chat from "../pages/Chat/index";
import KnowledgeBase from "../pages/KnowledgeBase";
import FolderPage from "../pages/KnowledgeBase/knowledgeBase";
import Login from "../pages/Login";
import SignUp from "../pages/SignUp";
import ProtectedRoute from "./ProtectedRoutes";
import ChatScreen from "./Chat/chat";
import ForgotPassword from "./ForgotPassword";
import DashboardPage from "./Dashboard";
import ResetPassword from "./ResetPassword";
import VerifyEmail from "./VerifyEmail";
import LandingPage from "./LandingPage";
import GeneralNotFound from "./Error/GeneralNotFound";
import ChatNotFound from "./Error/ChatNotFound";
import KnowledgeBaseNotFound from "./Error/KnowledgebaseNotFound";
import ChatBot from "./ChatBot";
import DocumentSummarizer from "./DocumentSummarizer";
import DocumentSummarizerPage from "./DocumentSummarizer/DocumentSummarizerPage";

const AppRouter: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<Login />} />
      {/* <Route path="/signup" element={<SignUp />} /> */}
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password" element={<ResetPassword />} />
      <Route path="/verify-email" element={<VerifyEmail />} />
      <Route path="/landing" element={<LandingPage />} />
      {/* Protected Routes */}
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <Chat />
          </ProtectedRoute>
        }
      />
      
      {/* <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        }
      /> */}
      {/* Catch-all for any invalid /dashboard/* routes */}
      {/* <Route
        path="/dashboard/*"
        element={
          <ProtectedRoute>
            <GeneralNotFound />
          </ProtectedRoute>
        }
      /> */}
      {/* Chat Routes */}
      <Route
        path="/chat/:agentName/:folderId/:agentId"
        element={
          <ProtectedRoute>
            <ChatScreen />
          </ProtectedRoute>
        }
      />
      {/* <Route
        path="/chat/:agentId/dashboard"
        element={
          <ProtectedRoute>
            <ChatBot />
          </ProtectedRoute>
        }
      /> */}
      {/* If /chat is accessed without parameters, render ChatNotFound */}
      {/* <Route
        path="/chat"
        element={
          <ProtectedRoute>
            <ChatNotFound />
          </ProtectedRoute>
        }
      /> */}
      {/* Catch-all for any invalid /chat/* routes */}
      {/* <Route
        path="/chat/*"
        element={
          <ProtectedRoute>
            <GeneralNotFound />
          </ProtectedRoute>
        }
      /> */}
      {/* KnowledgeBase Routes */}
      <Route
        path="/knowledgeBase"
        element={
          <ProtectedRoute>
            <KnowledgeBase />
          </ProtectedRoute>
        }
      />
      <Route
        path="/knowledgeBase/:folderName/:folderId"
        element={
          <ProtectedRoute>
            <FolderPage />
          </ProtectedRoute>
        }
      />
      {/* Catch-all for any invalid /knowledgeBase/* routes */}
      {/* <Route
        path="/knowledgeBase/*"
        element={
          <ProtectedRoute>
            <KnowledgeBaseNotFound />
          </ProtectedRoute>
        }
      /> */}

      {/* Document Summairzer */}
      <Route
        path="/document-summarizer"
        element={
          <ProtectedRoute>
            <DocumentSummarizer />
          </ProtectedRoute>
        }
      />
      <Route
        path="/document-summarizer"
        element={
          <ProtectedRoute>
            <DocumentSummarizer />
          </ProtectedRoute>
        }
      />
      <Route
        path="/document-summarizer/:id"
        element={
          <ProtectedRoute>
            <DocumentSummarizerPage />
          </ProtectedRoute>
        }
      />
      {/* Catch-all for any invalid routes */}
      <Route
        path="*"
        element={
          <ProtectedRoute>
            <GeneralNotFound />
          </ProtectedRoute>
        }
      />
    </Routes>
  );
};

export default AppRouter;
