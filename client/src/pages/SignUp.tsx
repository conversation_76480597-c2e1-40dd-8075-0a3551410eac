import React, { ChangeEvent, useEffect, useState } from "react";
import { Input } from "../components/ui/input";
import { PasswordInput } from "../components/PasswordInputComponent";
import { Button } from "../components/ui/button";
import { useToast } from "../hooks/use-toast";
import { Link, useNavigate } from "react-router";
import { registerUser } from "../api";
import { useAuthStore } from "../store/authStore";
import GoogleButton from "../components/GoogleOAuth/GoogleButton";
import { statusCodes } from "../api/constants";

const SignUp: React.FC = () => {
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();
  const navigate = useNavigate();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const login = useAuthStore((state) => state.login);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    if (isAuthenticated || checkAuth()) {
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, checkAuth]);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      setLoading(false);
      return;
    }

    try {
      const response = await registerUser(username, email, password);
      if (response.status !== statusCodes.SUCCESS) {
        throw new Error("Signup failed. Please try again.");
      }
      login(response.data["access_token"]);
      navigate("/", { replace: true });
    } catch (err: any) {
      if (err?.response?.status === 400) {
        // If status code is 400, display the detailed error message
        setError("Email already registered."); // For internal state management
        toast({
          variant: "destructive",
          description: "Email already registered.",
        });
      } else {
        // Handle other errors
        setError((err as Error).message || "An unknown error occurred.");
        toast({
          variant: "destructive",
          description:
            err?.response?.data?.details || "An unknown error occurred.",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-sidebar">
      <div className="w-[400px] rounded-3xl bg-white px-8 py-10 shadow-md">
        <header className="mb-6 text-center text-2xl font-[700]">
          <Link to="/">
            Buddhi<span className="text-primary">AI</span>
          </Link>
        </header>
        <h1 className="mb-6 text-center text-xl font-semibold">
          Create new account
        </h1>
       
        <form onSubmit={handleSignup}>
          <div className="mb-4">
            <label htmlFor="username" className="block text-sm font-medium">
              Name
            </label>
            <Input
              type="text"
              id="username"
              className="mt-1 w-full rounded-md border border-gray-300 px-4 py-2"
              placeholder="Enter your Name"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium">
              Email
            </label>
            <Input
              type="email"
              id="email"
              className="mt-1 w-full rounded-md border border-gray-300 px-4 py-2"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div className="mb-4">
            <label htmlFor="password" className="block text-sm font-medium">
              Password
            </label>
            <PasswordInput
              id="password"
              className="mt-1 w-full rounded-md border border-gray-300 px-4 py-2"
              placeholder="Enter your password"
              value={password}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setPassword(e.target.value)
              }
              min={6}
            />
          </div>
          <div className="mb-6">
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium"
            >
              Confirm Password
            </label>
            <PasswordInput
              id="confirmPassword"
              className="mt-1 w-full rounded-md border border-gray-300 px-4 py-2"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setConfirmPassword(e.target.value)
              }
              min={6}
            />
          </div>
          {error && <p className="mb-4 text-sm text-red-500">{error}</p>}
          <Button
            type="submit"
            className={`w-full py-3 transition ${loading ? "opacity-50" : ""}`}
            disabled={loading}
          >
            {loading ? "Signing up..." : "Sign up"}
          </Button>
        </form>
        <p className="mt-4 text-center text-sm font-semibold">
          Already have an account?{" "}
          <Link to="/login" className="text-primary">
            Log in
          </Link>
        </p>
      </div>
    </div>
  );
};

export default SignUp;
