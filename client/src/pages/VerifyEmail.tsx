import { useEffect, useState } from "react";
import { verifyUserEmail } from "../api";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardFooter,
  CardHeader,
} from "../components/ui/card";
import { AnimatePresence, motion } from "motion/react";

import BlingIcon from "../assets/bling_logo.svg";
import { useToast } from "../hooks/use-toast";
import { useNavigate, useSearchParams } from "react-router";
import { Button } from "../components/ui/button";
import { ShieldAlert } from "lucide-react";
import { useAuthStore } from "../store/authStore";
import { useUserStore } from "../store/userStore";

export default function VerifyEmail() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token") || "";
  const navigate = useNavigate();
  const { toast } = useToast();
  const logout = useAuthStore((state) => state.logout);
  const removeUserData = useUserStore((state) => state.removeUserData);

  const handleVerifyEmail = async () => {
    setIsLoading(true);
    try {
      await verifyUserEmail(token);
      navigate("/", { replace: true });
      toast({
        description: "Successfully verified email.",
      });
    } catch (err) {
      setError("Something went wrong. Please try again later");
      toast({
        variant: "destructive",
        description: "Something went wrong. Please try again later",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!token) {
      navigate("/login");
      return;
    }
    handleVerifyEmail();
  }, []);

  const handleBackToSignUp = () => {
    logout();
    removeUserData();
    navigate("/signup", { replace: true });
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        className="flex min-h-screen items-center justify-center bg-sidebar"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <Card className="mx-auto w-full max-w-[400px] rounded-[30px] shadow-lg">
          <CardHeader className="space-y-5">
            <div className="flex items-center justify-center gap-2">
              <img src={BlingIcon} />
              <h1 className="text-xl font-semibold">
                Buddhi<span className="text-primary">AI</span>
              </h1>
            </div>
            <h2 className="text-center text-xl font-semibold tracking-tight">
              Verifying your email address
            </h2>
          </CardHeader>

          <CardContent className="flex flex-col items-center space-y-4">
            {error ? (
              <ShieldAlert className="h-12 w-12 text-destructive" />
            ) : (
              <motion.div
                className="h-12 w-12 rounded-full border-[6.5px] border-gray-300 border-t-black"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            )}

            <span className="text-center text-xs leading-relaxed text-gray-600">
              {error
                ? "It took longer than expected, ensure your network connection is stable and please retry."
                : "Please wait a moment while we verify your email address. If the loading takes too long, ensure your network connection is stable and retry if necessary."}
            </span>
          </CardContent>

          {error && (
            <CardFooter className="flex flex-col gap-4">
              <Button
                type="submit"
                onClick={handleVerifyEmail}
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
              >
                Retry
              </Button>
              <Button
                className="w-full rounded-lg border border-primary bg-transparent py-3 font-semibold text-primary transition-colors hover:bg-zinc-100"
                onClick={handleBackToSignUp}
              >
                Back to Sign up
              </Button>
            </CardFooter>
          )}
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
