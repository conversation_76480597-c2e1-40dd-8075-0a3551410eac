import { create } from "zustand";
import { AgentData } from "../components/ChatComponent/components/ChatExplore";

type AgentStore = {
  activeAgent: Partial<AgentData> | null;
  setActiveAgent: (agent: Partial<AgentData> | null) => void;
};

export const useAgentStore = create<AgentStore>((set) => ({
  activeAgent: null,
  setActiveAgent: (agent: Partial<AgentData> | null) => {
    set({ activeAgent: agent });
  },
}));
