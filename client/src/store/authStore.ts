import { create } from "zustand";
import { createCookieString } from "../utils/environment";

// Helper function to get token from localStorage or cookies
const getStoredToken = (): string | null => {
  // First check localStorage
  const localToken = localStorage.getItem("access_token");
  const localExpires = localStorage.getItem("access_token_expires");

  if (localToken && localExpires) {
    const expiryDate = new Date(localExpires);
    if (expiryDate > new Date()) {
      return localToken;
    } else {
      // Token expired, clean up
      localStorage.removeItem("access_token");
      localStorage.removeItem("access_token_expires");
    }
  }

  // Fallback to cookie
  const match = document.cookie.match(/access_token=([^;]+)/);
  return match ? match[1] : null;
};

interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  login: (token: string) => void;
  logout: () => void;
  checkAuth: () => boolean;
  getToken: () => string | null;
}

export const useAuthStore = create<AuthState>((set) => ({
  isAuthenticated: false,
  accessToken: null,
  login: (token: string) => {
    const expiryDate = new Date();
    expiryDate.setTime(expiryDate.getTime() + 72 * 60 * 60 * 1000);

    // Save to localStorage as primary storage
    localStorage.setItem("access_token", token);
    localStorage.setItem("access_token_expires", expiryDate.toISOString());

    // Also try to save to cookies (may fail in HTTP environments)
    try {
      const cookieString = createCookieString("access_token", token, {
        expires: expiryDate,
      });
      document.cookie = cookieString;
    } catch (error) {
      console.warn("Failed to set cookie, using localStorage only:", error);
    }

    set({ isAuthenticated: true, accessToken: token });
  },
  logout: () => {
    // Clear localStorage
    localStorage.removeItem("access_token");
    localStorage.removeItem("access_token_expires");

    // Clear the cookie with environment-aware settings
    try {
      const expiredDate = new Date(0); // January 1, 1970
      const cookieString = createCookieString("access_token", "", {
        expires: expiredDate,
      });
      document.cookie = cookieString;
    } catch (error) {
      console.warn("Failed to clear cookie:", error);
    }

    set({ isAuthenticated: false, accessToken: null });
  },
  checkAuth: () => {
    return !!getStoredToken();
  },
  getToken: () => {
    return getStoredToken();
  },
}));
