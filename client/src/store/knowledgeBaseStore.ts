import { create } from "zustand";

export type KnowledgeBase = {
  id: string | undefined;
  file_count?: number;
  name: string | undefined;
};

const isValidKnowledgeBaseRoute = (path: string): boolean => {
  return path === "/" || path.startsWith("/chat");
};

type KnowledgeBaseStore = {
  activeKnowledgeBase: Partial<KnowledgeBase> | null;
  setActiveKnowledgeBase: (
    knowledgeBase: Partial<KnowledgeBase> | null,
    currentPath: string,
  ) => void;
};

export const useKnowledgeBaseStore = create<KnowledgeBaseStore>((set) => ({
  activeKnowledgeBase: null,
  setActiveKnowledgeBase: (
    knowledgeBase: Partial<KnowledgeBase> | null,
    currentPath: string,
  ) => {
    if (!isValidKnowledgeBaseRoute(currentPath)) {
      set({ activeKnowledgeBase: null });
      return;
    }
    set({ activeKnowledgeBase: knowledgeBase });
  },
}));
