import { create } from "zustand";
import { Plot } from "../components/ChatComponent/components/ChatBubble";

interface PlotStore {
  isOpen: boolean;
  activePlot: Plot | null;
  plots: Plot[];
  openSidebar: () => void;
  closeSidebar: () => void;
  setActivePlot: (plot: Plot) => void;
  setPlots: (plots: Plot[]) => void;
}

export const usePlotStore = create<PlotStore>((set) => ({
  isOpen: false,
  activePlot: null,
  plots: [],
  openSidebar: () => set({ isOpen: true }),
  closeSidebar: () => set({ isOpen: false }),
  setActivePlot: (plot) => set({ activePlot: plot, isOpen: true }),
  setPlots: (plots) => {
    set({ plots });
    if (plots.length > 0) {
      set({ isOpen: true, activePlot: plots[0] });
    }
  },
}));
