import { create } from "zustand";
import { persist, StorageValue } from "zustand/middleware";
import { getUser } from "../api";
import { useAuthStore } from "./authStore";

interface User {
  id: string;
  email: string;
  is_active: boolean;
  is_paid: boolean;
  username: string;
  profile_image: string | null;
  created_at: string;
  updated_at: string;
  last_login_at: string | null;
  max_folder: number;
  tokens_left: number;
  last_token_reset: string;
  is_verified: boolean
}

interface UserState {
  user: any;
  isLoading: boolean;
  tokenleft: string;
  fetchUser: () => Promise<void>;
  setTokenLeft: (tokens: string) => void;
  removeUserData: () => void;
}

// const logout = useAuthStore((state) => state.logout);

export const useUserStore = create<UserState>()(
  persist<UserState>(
    (set, get) => ({
      user: null,
      isLoading: false,
      tokenleft: "", // Default token left value
      fetchUser: async () => {
        set({ isLoading: true });
        try {
          const userData = await getUser();
          set({
            user: userData,
            isLoading: false,
            tokenleft: userData.tokens_left,
          });
        } catch (error) {
          useAuthStore.getState().logout();
          get().removeUserData();
          set({ isLoading: false });
        }
      },
      setTokenLeft: (tokens: string) => set({ tokenleft: tokens }), // Set tokenleft from other components
      removeUserData: () => {
        set({ user: null, tokenleft: "" }); // Clear tokenleft too
        sessionStorage.removeItem("user-store");
      },
    }),
    {
      name: "user-store", // Store name
      storage: {
        getItem: (key: string) => {
          const value = sessionStorage.getItem(key);
          return value ? JSON.parse(value) : null;
        },
        setItem: (key: string, value: StorageValue<UserState>) => {
          return sessionStorage.setItem(key, JSON.stringify(value));
        },
        removeItem: (key: string) => {
          sessionStorage.removeItem(key);
        },
      },
    },
  ),
);
