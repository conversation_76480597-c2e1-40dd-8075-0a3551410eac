/**
 * Environment detection utilities for handling HTTP vs HTTPS configurations
 */

/**
 * Determines if the application is running in a production environment (HTTPS)
 * @returns {boolean} True if running in production (HTTPS), false for development (HTTP)
 */
export const isProduction = (): boolean => {
  // Check if we're running in production mode
  if (import.meta.env.MODE === 'production') {
    return true;
  }
  
  // Check if the current protocol is HTTPS
  if (typeof window !== 'undefined' && window.location.protocol === 'https:') {
    return true;
  }
  
  // Check if the API URL uses HTTPS
  const apiUrl = import.meta.env.VITE_SERVER_API_URL;
  if (apiUrl && apiUrl.startsWith('https://')) {
    return true;
  }
  
  return false;
};

/**
 * Gets the appropriate SameSite attribute for cookies based on environment
 * @returns {'Lax' | 'None'} SameSite attribute value
 */
export const getSameSiteAttribute = (): 'Lax' | 'None' => {
  return isProduction() ? 'None' : 'Lax';
};

/**
 * Gets the appropriate Secure attribute for cookies based on environment
 * @returns {boolean} Whether cookies should be secure
 */
export const getSecureAttribute = (): boolean => {
  return isProduction();
};

/**
 * Creates a cookie string with appropriate security attributes for the current environment
 * @param name - Cookie name
 * @param value - Cookie value
 * @param options - Additional cookie options
 * @returns {string} Complete cookie string
 */
export const createCookieString = (
  name: string,
  value: string,
  options: {
    path?: string;
    expires?: Date;
    maxAge?: number;
  } = {}
): string => {
  const { path = '/', expires, maxAge } = options;
  const sameSite = getSameSiteAttribute();
  const secure = getSecureAttribute();
  
  let cookieString = `${name}=${value}; path=${path}; SameSite=${sameSite}`;
  
  if (secure) {
    cookieString += '; Secure';
  }
  
  if (expires) {
    cookieString += `; expires=${expires.toUTCString()}`;
  }
  
  if (maxAge) {
    cookieString += `; max-age=${maxAge}`;
  }
  
  return cookieString;
};
