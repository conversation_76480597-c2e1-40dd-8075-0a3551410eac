import { defineConfig } from "vite";
import { resolve } from "path";
export default defineConfig({
  define: {
    "process.env": {
      NODE_ENV: "production",
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, "src/components/ChatBot/Embed/export.tsx"),
      name: "ChatWidget",
      fileName: (format) => `widget.${format}.js`,
      formats: ["umd"],
    },
    outDir: "dist/chatbot",
    copyPublicDir: false,
    minify: "terser",
    rollupOptions: {
      external: [],
      output: {
        assetFileNames: () => {
          return "";
        },
        inlineDynamicImports: true
      },
    },
  },
});
