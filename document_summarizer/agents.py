import logging
from typing import List, Dict, Any
from abc import ABC, abstractmethod
from document_summarizer.bedrock_utilities import BedrockClaudeLLM, BedrockTitanEmbeddings

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """Abstract base class for all political analysis agents"""
    
    def __init__(self, llm: BedrockClaudeLLM):
        self.llm = llm
    
    @abstractmethod
    def get_name(self) -> str:
        """Return the agent name"""
        pass
    
    @abstractmethod
    def get_query(self) -> str:
        """Return the search query for retrieving relevant documents"""
        pass
    
    @abstractmethod
    def get_prompt(self) -> str:
        """Return the prompt template for this agent"""
        pass
    
    def expand_query(self, query: str, agent_name: str) -> List[str]:
        """Expand query with synonyms for better retrieval"""
        synonyms = {
            'issue_extraction': {
                "major issues": ["public issues", "core issues", "critical issues", "key concerns", "long standing issues", "long standing unresolved problems", "major problems", "major challenges", "major concerns", "key issues"],
                "insufficient": ["inadequate", "lacking", "deficient", "poor", "gaps"],
                "recurring": ["frequent", "persistent", "repeated", "chronic"],
                "need for": ["demand for", "requirement for", "necessity of", "call for"],
                "frequent complaint": ["multiple petitions", "rising concern", "public grievances", "common complaints"],
                "hinder": ["obstruct", "impede", "prevent", "block", "restrict"],
                "poorly": ["badly", "inefficiently", "suboptimally", "miserably"],
                "unfinished": ["pending", "incomplete", "unresolved"],
                "blame": ["fault", "responsibility", "culpability"],
                "frustration": ["discontent", "dissatisfaction", "disappointment", "annoyance"],
                "complaints": ["grievances", "dissatisfaction reports", "objections", "protests"],
                "rising concern": ["growing worry", "increasing anxiety", "escalating issue"],
                "lack of": ["absence of", "shortage of", "dearth of", "scarcity of"],
                "demanding": ["insisting", "calling for", "requiring", "urging"],
                "opposing alliance": ["rival bloc", "anti-government coalition", "opposition front"],
                "town": ["nagar", "kasbah", "urban center", "district headquarters", "municipality"],
                "village": ["hamlet", "rural settlement", "gram panchayat", "gaon", "palli"],
                "local names": ["place names", "geographical names", "area names"],
                "indian city": ["metropolitan area", "urban agglomeration", "major city"],
                "taluka": ["tehsil", "mandal", "sub-district", "block"],
                "rural": ["agrarian", "countryside", "village-based", "non-urban"],
                "distress": ["hardship", "suffering", "misery", "adversity"],
                "challenges": ["obstacles", "difficulties", "hurdles", "problems"],
                "poor": ["substandard", "inferior", "unsatisfactory"],
                "pending": ["awaiting resolution", "unresolved", "ongoing"],
                "inadequate": ["insufficient", "deficient", "unsuitable"],
                "difficult": ["hard", "arduous", "tough", "challenging"],
                "struggle": ["hardship", "fight", "endeavor", "ordeal"],
                "delayed": ["postponed", "deferred", "behind schedule"],
                "mismanaged": ["poorly administered", "inefficiently handled", "bungled"],
                "severe": ["serious", "grave", "critical", "intense"],
                "segment wise issues": ["sector-specific problems", "category-based challenges", "demographic-specific concerns"],
                "middle class": ["middle-income group", "bourgeoisie"],
                "youth and first time votes": ["young voters", "new voters", "first-time electors"],
                "women voters": ["female electorate", "women's suffrage"],
                "major issues face by voters": ["electoral concerns", "voter challenges", "public grievances at polls"],
                "constituency area development": ["local area improvement", "regional development projects", "electoral district progress"],
                "infrastructure": ["public utilities", "facilities", "development projects"],
                "water": ["water supply", "water scarcity", "drinking water", "irrigation"],
                "roads": ["road network", "transportation infrastructure", "connectivity"],
                "agriculture": ["farming issues", "agrarian concerns", "crop production"],
                "healthcare": ["medical facilities", "health services", "public health"],
                "youth": ["young people", "adolescents", "young generation"],
                "education": ["learning facilities", "educational access", "schooling"],
                "employment": ["job opportunities", "livelihood", "unemployment"]
            },
            'human_stories': {
                "key issues and demands": ["community specific core demands", "specific issues and demands", "public demands", "requests", "needs", "wants from", "local demands", "public grievances", "community needs"],
                "town": ["nagar", "kasbah", "urban center", "district headquarters", "municipality"],
                "village": ["hamlet", "rural settlement", "gram panchayat", "gaon", "palli"],
                "local names": ["place names", "geographical names", "area names"],
                "indian city": ["metropolitan area", "urban agglomeration", "major city"],
                "taluka": ["tehsil", "mandal", "sub-district", "block"],
                "rural": ["agrarian", "countryside", "village-based", "non-urban"],
                "demanding": ["insisting", "calling for", "seeking", "urging"],
                "despite": ["notwithstanding", "even though", "in spite of"],
                "not yet": ["still pending", "unfulfilled as of now"],
                "not fullfilled": ["unmet", "unrealized", "broken promises"],
                "public issues": ["societal problems", "community challenges", "citizen concerns"],
                "public demands": ["citizen requests", "community needs", "popular calls"],
                "administrative negligence": ["governmental oversight failures", "bureaucratic neglect", "poor governance"],
                "poor supervision of public works": ["inadequate oversight of projects", "lapses in project management", "deficient monitoring"],
                "multiple petitions": ["numerous appeals", "repeated requests", "collective representations"],
                "frequent complaint": ["recurring grievances", "constant grumbling"],
                "hinder": ["obstruct", "impede", "block"],
                "poorly": ["badly", "inefficiently"],
                "unfinished": ["incomplete", "pending"],
                "blame": ["fault", "responsibility"],
                "frustration": ["discontent", "disappointment"],
                "complaints": ["grievances", "dissatisfaction"],
                "rising concern": ["growing worry", "increasing anxiety"],
                "lack of": ["absence of", "shortage of"],
                "community specific core demands": ["localized essential needs", "group-specific fundamental requirements"],
                "scheduled tribes": ["Adivasi communities", "indigenous tribes"],
                "dominant obc communities": ["influential backward classes", "prominent OBC groups"],
                "upper castes": ["forward castes", "higher social groups"],
                "problems": ["issues", "challenges", "difficulties"],
                "people": ["citizens", "residents", "inhabitants"],
                "citizens": ["public", "residents"],
                "areas": ["regions", "localities", "zones"],
                "villages": ["hamlets", "rural communities"],
                "constituency": ["electoral district", "voting area"],
                "local community": ["neighborhood", "resident group"],
                "protest": ["demonstration", "agitation", "dissent"],
                "quotes": ["statements", "testimonials", "sayings"],
                "local demands": ["community requests", "neighborhood needs"],
                "public grievances community": ["community complaints", "citizen concerns"],
                "needs": ["requirements", "necessities"],
                "water": ["water supply", "drinking water scarcity"],
                "concerns": ["worries", "apprehensions"],
                "petition": ["appeal", "representation", "formal request"],
                "near": ["close to", "in proximity to"],
                "around": ["in the vicinity of", "surrounding"],
                "surrounding": ["nearby", "adjacent"],
                "close to": ["proximate to", "adjoining"],
                "neighbouring": ["adjoining", "contiguous", "proximate"],
                "area": ["region", "locale", "vicinity"]
            },
            'unfulfilled_promises': {
                "community": ["local population", "residents", "group", "social segment"],
                "accident": ["mishap", "incident", "calamity"],
                "town": ["nagar", "kasbah", "urban center", "district headquarters", "municipality"],
                "village": ["hamlet", "rural settlement", "gram panchayat", "gaon", "palli"],
                "local names": ["place names", "geographical names", "area names"],
                "indian city": ["metropolitan area", "urban agglomeration", "major city"],
                "taluka": ["tehsil", "mandal", "sub-district", "block"],
                "rural": ["agrarian", "countryside", "village-based", "non-urban"],
                "killed": ["deceased", "fatalities", "lost lives"],
                "suffer": ["experience hardship", "undergo distress", "bear pain"],
                "date": ["specific date", "day", "timeframe"],
                "family": ["household", "relatives", "kin"],
                "assaulted": ["attacked", "victimized", "abused"],
                "death": ["demise", "fatality", "loss of life"],
                "year old": ["aged", "of age"],
                "individual stories": ["personal accounts", "narratives", "lived experiences"],
                "daily struggles": ["everyday challenges", "routine hardships", "common difficulties"],
                "water issues": ["water scarcity", "drinking water problems", "water supply challenges"],
                "hospital access": ["healthcare availability", "medical facility proximity", "hospital reach"],
                "transport difficulty": ["commute challenges", "transportation problems", "mobility issues"],
                "personal narratives": ["first-person accounts", "biographies", "anecdotes"],
                "emotional impact": ["psychological effect", "sentimental consequences"],
                "family problems": ["domestic issues", "household disputes"],
                "near": ["close to", "in proximity to"],
                "around": ["in the vicinity of", "surrounding"],
                "surrounding": ["nearby", "adjacent"],
                "close to": ["proximate to", "adjoining"],
                "neighbouring": ["adjoining", "contiguous", "proximate"],
                "area": ["region", "locale", "vicinity"]
            },
            'public_demand': {
                "dmk promise": ["dmk electoral pledge", "dmk commitment", "dmk manifesto point"],
                "status": ["current state", "progress", "implementation level", "update"],
                "election promises": ["electoral commitments", "campaign pledges", "manifesto pledges"],
                "dmk’s failures": ["dmk shortcomings", "dmk non-performance", "dmk ineffectiveness"],
                "under-implemented": ["partially executed", "insufficiently carried out", "poorly implemented"],
                "current issues by dmk": ["dmk government problems", "challenges under dmk rule"],
                "pending projects": ["uncompleted initiatives", "unfinished schemes", "awaiting completion"],
                "promises not fullfilled": ["unmet assurances", "broken pledges", "unkept commitments"],
                "failures of dmk government": ["dmk administration shortcomings", "governmental inefficiencies"],
                "broken electoral promises": ["unfulfilled election pledges", "campaign promises breached"],
                "election promises commitments made political parties": ["electoral pledges by political parties", "party manifestos"],
                "dmk aiadmk bjp congress unfulfilled broken incomplete projects promises not kept": ["failure of political parties' promises", "unfulfilled pledges by major parties"],
                "dmk 2021 manifesto": ["dmk election program 2021", "dmk vision document 2021"],
                "unmet promises": ["unrealized pledges", "unfulfilled commitments"],
                "policy failures": ["programmatic setbacks", "implementation flaws"],
                "unfulfilled commitments": ["unkept pledges", "outstanding obligations"],
                "broken assurances": ["violated guarantees", "disregarded promises"],
                "scheme non-delivery": ["programmatic failure", "scheme implementation failure"],
                "announced vs implemented": ["declared versus actualized", "stated versus realized"],
                "near": ["close to", "in proximity to"],
                "around": ["in the vicinity of", "surrounding"],
                "surrounding": ["nearby", "adjacent"],
                "close to": ["proximate to", "adjoining"],
                "neighbouring": ["adjoining", "contiguous", "proximate"],
                "area": ["region", "locale", "vicinity"]
            },
            'manifesto_drafting': {
                "set up": ["establish", "create", "form", "found"],
                "construct": ["build", "erect", "develop", "install"],
                "guarantee": ["assure", "ensure", "warrant", "pledge"],
                "upgrade": ["improve", "modernize", "enhance", "revamp"],
                "plan": ["strategize", "devise", "formulate", "propose"],
                "stop": ["halt", "cease", "prevent", "end"],
                "support": ["aid", "assist", "promote", "sustain"],
                "for every": ["each", "per", "for all"],
                "24x7": ["round the clock", "continuous", "all-day, all-night"],
                "compensation": ["reparation", "reimbursement", "indemnification", "damages"],
                "develop": ["progress", "advance", "foster", "grow"],
                "reform empowerment": ["strengthening reforms", "enabling changes", "empowering initiatives"],
                "manifesto promises suggestions recommendations": ["electoral pledges", "policy proposals", "campaign agenda points", "actionable ideas"],
                "constituency area development": ["local area improvement", "regional development projects", "electoral district progress"],
                "local needs": ["community requirements", "area-specific demands"],
                "community": ["local population", "residents", "group", "social segment"],
                "development solutions": ["growth strategies", "progress initiatives", "improvement plans"],
                "actionable manifesto points": ["implementable policy proposals", "practical election pledges"],
                "infrastructure upgrade proposals": ["infrastructure improvement plans", "utility modernization suggestions"],
                "realistic commitments": ["achievable pledges", "practical promises", "feasible assurances"],
                "local": ["regional", "community-based", "area-specific"],
                "programs": ["schemes", "initiatives", "projects"],
                "access": ["availability", "reach", "entry"],
                "near": ["close to", "in proximity to"],
                "around": ["in the vicinity of", "surrounding"],
                "surrounding": ["nearby", "adjacent"],
                "close to": ["proximate to", "adjoining"],
                "neighbouring": ["adjoining", "contiguous", "proximate"],
                "area": ["region", "locale", "vicinity"]
            }
        }
        
        expanded_queries = [query]
        query_lower = query.lower()
        
        # Get synonyms for the specific agent
        agent_synonyms = synonyms.get(agent_name, {})
        
        for term, syns in agent_synonyms.items():
            if term in query_lower:
                for syn in syns[:2]:  # Limit to 2 synonyms per term
                    expanded_queries.append(query_lower.replace(term, syn))
        
        return expanded_queries  # Return max 3 expanded queries
    
    async def process(self, chunks: List[Any], user_id: str) -> str:
        """Process chunks with this agent for a specific user"""
        if not chunks:
            return f"No relevant content found for {self.get_name()} (User: {user_id})"
        
        # Combine chunk content
        context = "\n\n".join([
            f"{chunk.get('content', str(chunk))}" 
            for i, chunk in enumerate(chunks[:10])
        ])
        logger.info(f"Combined context: {context}")
        
        prompt = self.get_prompt().format(context=context)
        messages = [("user", prompt)]
        logger.info(f"Processing message to llm with prompt: {prompt}")
        try:
            response = self.llm.ainvoke_async(messages)  
            result = response["content"]
            logger.info(f"Response from llm: {result}")
            return result
        except Exception as e:
            logger.error(f"Error processing {self.get_name()} for user {user_id}: {e}")
            return f"Error processing {self.get_name()}: {str(e)}"

class ContentDepthEvaluator:
    """Evaluates content depth and triggers dynamic retrieval if needed"""

    def __init__(self, llm: BedrockClaudeLLM):
        self.llm = llm
        self.min_points_threshold = {
            'issue_extraction': 8,
            'human_stories': 3,
            'unfulfilled_promises': 6,
            'public_demand': 8,
            'manifesto_drafting': 10
        }

    async def evaluate_content_depth(self, agent_name: str, content: str) -> Dict[str, Any]:
        """Evaluate if content has sufficient depth and detail"""
        evaluation_prompt = f"""
        Evaluate the following {agent_name} content for depth and comprehensiveness:

        Content to evaluate:
        {content}

        Evaluation criteria:
        1. Number of distinct points/items covered
        2. Level of detail and specificity in each point
        3. Inclusion of specific location names, people, dates, numbers
        4. Presence of concrete examples and evidence
        5. Coverage of different aspects/dimensions of the topic

        Expected minimum points for {agent_name}: {self.min_points_threshold.get(agent_name, 5)}

        Respond with:
        - SUFFICIENT: if content meets depth requirements
        - INSUFFICIENT: if content needs more detail/points
        - REASON: brief explanation of what's missing

        Format: EVALUATION: [SUFFICIENT/INSUFFICIENT] | REASON: [explanation]
        """

        try:
            response = self.llm.ainvoke_async([("user", evaluation_prompt)])
            result = response["content"]

            # Parse evaluation result
            if "INSUFFICIENT" in result.upper():
                return {
                    "sufficient": False,
                    "reason": result.split("REASON:")[-1].strip() if "REASON:" in result else "Content lacks depth",
                    "needs_expansion": True
                }
            else:
                return {
                    "sufficient": True,
                    "reason": "Content meets depth requirements",
                    "needs_expansion": False
                }
        except Exception as e:
            logger.error(f"Error evaluating content depth: {e}")
            return {"sufficient": True, "needs_expansion": False, "reason": "Evaluation failed"}

class EnhancedRetrieval:
    """Enhanced retrieval with user-specific filtering and dynamic expansion"""

    def __init__(self, weaviate_manager, embedding_model: BedrockTitanEmbeddings):
        self.client = weaviate_manager
        self.embedding_model = embedding_model
        self.top_k = 30
        self.min_similarity = 0.70
        self.expanded_top_k = 50  # For dynamic retrieval
    
    async def retrieve_relevant_chunks(self, query: str, user_id: str, collection_id: str, expanded: bool = False) -> List[Dict]:
        """Retrieve relevant chunks using search_similar_for_user method"""
        try:
            import time

            # Generate embedding for the query using synchronous method
            query_embedding = self.embedding_model.aembed_query(query)
            start_time = time.time()

            # Use expanded limit if dynamic retrieval is requested
            limit = self.expanded_top_k if expanded else self.top_k
            min_sim = 0.65 if expanded else self.min_similarity  # Lower threshold for expanded search

            # Use WeaviateManager's search_similar_for_user method
            chunks = self.client.hybrid_search_for_user(
                query_vector=query_embedding,
                query_text=query,
                user_id=user_id,
                collection_id=collection_id,
                limit=limit
            )
            # Filter by minimum similarity
            filtered_chunks = [
                chunk for chunk in chunks
                if float(chunk.get("score", 0)) >= min_sim
            ]

            end_time = time.time()
            logger.info(f"Retrieved {len(filtered_chunks)} chunks in {end_time - start_time:.2f}s (expanded: {expanded})")

            return filtered_chunks

        except Exception as e:
            logger.error(f"Error retrieving chunks: {e}")
            return []

    async def dynamic_retrieval_for_depth(self, agent_name: str, original_query: str, user_id: str, collection_id: str) -> List[Dict]:
        """Perform dynamic retrieval with expanded queries for better depth"""
        try:
            # Create depth-focused queries based on agent type
            depth_queries = self._generate_depth_queries(agent_name, original_query)

            all_chunks = []
            for query in depth_queries:
                chunks = await self.retrieve_relevant_chunks(query, user_id, collection_id, expanded=True)
                all_chunks.extend(chunks)

            # Remove duplicates based on content similarity
            unique_chunks = self._deduplicate_chunks(all_chunks)

            logger.info(f"Dynamic retrieval for {agent_name}: {len(unique_chunks)} unique chunks from {len(all_chunks)} total")
            return unique_chunks[:self.expanded_top_k]  # Limit final results

        except Exception as e:
            logger.error(f"Error in dynamic retrieval: {e}")
            return []

    def _generate_depth_queries(self, agent_name: str, original_query: str) -> List[str]:
        """Generate additional queries for deeper content retrieval"""
        depth_query_templates = {
            'issue_extraction': [
                "specific problems villages towns localities infrastructure water roads healthcare education employment",
                "community complaints grievances local area issues village level problems",
                "detailed incidents specific locations dates numbers statistics evidence"
            ],
            'human_stories': [
                "personal stories individual experiences family problems community struggles",
                "specific incidents accidents tragedies human impact emotional stories",
                "village names people affected local testimonies personal accounts"
            ],
            'unfulfilled_promises': [
                "broken promises delayed projects incomplete schemes government failures",
                "specific commitments not fulfilled election promises status implementation",
                "project delays bureaucratic issues corruption allegations public protests"
            ],
            'public_demand': [
                "community demands public requests local needs infrastructure requirements",
                "specific demands from villages towns localities development needs",
                "citizen demands policy changes infrastructure improvements social needs"
            ],
            'manifesto_drafting': [
                "development proposals infrastructure plans community solutions local needs",
                "specific recommendations actionable solutions constituency development",
                "realistic commitments local development infrastructure social programs"
            ]
        }

        base_queries = depth_query_templates.get(agent_name, [original_query])
        return [original_query] + base_queries

    def _deduplicate_chunks(self, chunks: List[Dict]) -> List[Dict]:
        """Remove duplicate chunks based on content similarity"""
        if not chunks:
            return []

        unique_chunks = []
        seen_content = set()

        for chunk in chunks:
            content = chunk.get('content', '')
            # Simple deduplication based on first 100 characters
            content_key = content[:100].strip().lower()

            if content_key not in seen_content:
                seen_content.add(content_key)
                unique_chunks.append(chunk)

        return unique_chunks

class LocalizationEnhancer:
    """Enhances content with specific location names and local context"""

    def __init__(self, llm: BedrockClaudeLLM):
        self.llm = llm

    async def extract_location_context(self, content: str, user_id: str) -> str:
        """Extract and enhance location-specific context from content"""
        location_prompt = f"""
        Analyze the following content and extract ALL specific location names and local context:

        Content: {content}

        Extract and list:
        1. Village names, town names, city areas
        2. Panchayat, ward, block, district names
        3. Constituency and assembly segment names
        4. Infrastructure names (roads, bridges, schools, hospitals)
        5. Geographic features (rivers, tanks, canals, lakes)
        6. Specific addresses or landmarks mentioned

        For each location, provide:
        - Exact name as mentioned in the content
        - Type of location (village/town/district/etc.)
        - Context in which it was mentioned
        - Any specific issues or details associated with that location

        Format as a structured list for easy reference.
        """

        try:
            response = self.llm.ainvoke_async([("user", location_prompt)])
            return response["content"]
        except Exception as e:
            logger.error(f"Error extracting location context: {e}")
            return "Location context extraction failed"

class IssueExtractionAgent(BaseAgent):
    """Agent for extracting major public issues"""
    
    def get_name(self) -> str:
        return "issue_extraction"
    
    def get_query(self) -> str:
        return "Public issues, Major public issues, core issues, Critical issues, insufficient, recurring, need for, frequent complaint, hinder, poorly, unfinished, blame, frustration, complaints, rising concern, lack of, demanding, opposing alliance, town, village, local names, indian city, taluka, rural, panchayat, ward, block, district, constituency, assembly, parliamentary, key concerns, Long Standing Issues, Long standing unresolved problems, key concerns, distress, challenges, poor, pending, gaps, inadequate, difficult, struggle, delayed, mismanaged, severe, segment wise issues, middle class, youth and first time votes, women voters, Major issues face by voters, major issues, problems, challenges, concerns, constituency area development, infrastructure, water, roads, agriculture, healthcare, youth, education, employment, specific location names, village names, town names, locality names, area names, regional issues, local problems"
    
    def get_prompt(self) -> str:
        return """You are tasked with identifying major public issues based solely on the given context. Your output should be factual, concise, and strictly grounded in the provided information. Do not include assumptions or invented content.

Instructions:
-Title each issue with a short, descriptive phrase summarizing the problem.
-For each issue, write a brief explanation (1–5 lines) highlighting key facts, impacts, or stakeholder concerns.
-Include as many issues as are supported by the context — don't add generic issues not present in the source.
-Focus on clarity, factual tone, and specificity. Avoid generalizations or exaggeration.
-Group all issues under the heading: "Major Public Issues"

Critical Requirements for Information Depth:
-Extract all specific factual references from the source, including names of villages, towns, localities, constituencies, crop and livelihood types, government schemes, infrastructure names, and community identities (e.g., SC, MBC, fisherfolk, tenant farmers).
-Capture fine-grained local context—such as affected areas, stakeholder testimonies, timeline events, project locations, or specific administrative bodies or officials mentioned.
-Avoid vague generalizations; instead, emphasize grounded, verifiable details (e.g., “Semmankuppam unmanned railway crossing,” “Dalits in Melvadakuthu without burial road,” or “Mahalir Urimai scheme leaving out 50% women in Cuddalore”).
-Include project status (e.g., pending, incomplete, failed) and implementation insights, like delays, corruption allegations, bureaucratic hurdles, or community protest history, if specified.
-Prioritize hyperlocal precision over summary: include real names, incidents, numerical data, and any public grievances, protests, or human stories if explicitly found in the document.
-Mention all forms of social, economic, governance, or ecological impacts, especially where specific demographics (e.g., “banana farmers in Kurinjipadi,” “students of Periyar Government Arts College”) are referenced.
-Capture references to broken promises, neglected communities, delayed schemes, or public demands, even if they are embedded in narrative sections or human-interest anecdotes.

NOTE: Always surface the most pressing and impactful concerns. They must be prioritized over secondary or minor issues.
CRITICAL: BE COMPREHENSIVE AND INCLUDE AS MANY POINTS AS POSSIBLE.

ENHANCED REQUIREMENTS FOR DEPTH AND LOCALIZATION:
-MINIMUM TARGET: Extract at least 8-12 distinct issues if the context supports it.
-MANDATORY LOCALIZATION: Extract and prominently feature ALL specific location names:
  • Village names, town names, city areas, wards, panchayats
  • Block, district, constituency, and assembly segment names
  • Specific infrastructure names (roads, bridges, schools, hospitals, crossings)
  • Geographic features (rivers, tanks, canals, lakes, hills)
-DETAILED EVIDENCE: For each issue, include at least one concrete example with:
  • Specific location name and affected population
  • Quantifiable impact or numerical data
  • Current status or recent developments
  • Names of officials, schemes, or institutions involved

Example Output Format:
Major Public Issues

Neglect of Water Resources and Agriculture  
-Illegal red soil quarrying continues unabated in Kurinjipadi, drastically lowering groundwater levels and damaging fertile agricultural lands. Lakes, which once served as key irrigation sources, are now being illegally encroached upon. Farmers face crashing market prices, lack of MSP, and irrigation failure due to silted canals.

Handloom Sector facing Poverty 
-Kurinjipadi’s traditional weavers face poverty due to lack of government promotion, no market linkage, and absent revival schemes.

[Continue similarly for other issues...]

Relevant Information/ Context:
 {context}

MAKE MINIMAL CHANGES TO THE RETRIEVED CONTEXT AND JUST ORGANISE THE INFORMATION MORE APPROPRIATELY.
"""

class DemandSynthesisAgent(BaseAgent):
    """Agent for synthesizing public demands"""
    
    def get_name(self) -> str:
        return "public_demand"
    
    def get_query(self) -> str:
        return "Key Issues and Demands, community specific core demands, specific issues and demands, town, village, local names, indian city, taluka, rural, panchayat, ward, block, district, constituency, Demanding, despite, not yet, not fullfilled, Key Issues and Demands, public issues, public demands, administrative negligence, and poor supervision of public works, multiple petitions, frequent complaint, hinder, poorly, unfinished, blame, frustration, complaints, rising concern, lack of, community specific core demands, specific issues and demands, scheduled tribes, dominant obc communities, upper castes, problems, public demands, requests, needs, wants from, people, citizens, areas, villages, constituency, local community, protest, quotes, local demands, public grievances community, needs, complaints water, protest, concerns petition, near, around, surrounding, close to, neighbouring, area, specific location names, village names, town names, locality names"
    
    def get_prompt(self) -> str:
        return """Extract all public demands (as many as are available) from the relevant information.

Each demand should:
-Clearly state what is being demanded or improved
-Be linked to a real instance (such as a specific locality, incident, community concern, or quote)
-Use bullet points or short paragraphs for clarity and conciseness. Demands may include suggested actions or policies, but they must be tied to evidence in the context.

Critical Requirements for Information Depth:
-Extract all specific factual references from the source, including names of villages, towns, localities, constituencies, crop and livelihood types, government schemes, infrastructure names, and community identities (e.g., SC, MBC, fisherfolk, tenant farmers).
-Capture fine-grained local context—such as affected areas, stakeholder testimonies, timeline events, project locations, or specific administrative bodies or officials mentioned.
-Avoid vague generalizations; instead, emphasize grounded, verifiable details (e.g., “Semmankuppam unmanned railway crossing,” “Dalits in Melvadakuthu without burial road,” or “Mahalir Urimai scheme leaving out 50% women in Cuddalore”).
-Include project status (e.g., pending, incomplete, failed) and implementation insights, like delays, corruption allegations, bureaucratic hurdles, or community protest history, if specified.
-Prioritize hyperlocal precision over summary: include real names, incidents, numerical data, and any public grievances, protests, or human stories if explicitly found in the document.
-Mention all forms of social, economic, governance, or ecological impacts, especially where specific demographics (e.g., “banana farmers in Kurinjipadi,” “students of Periyar Government Arts College”) are referenced.
-Capture references to broken promises, neglected communities, delayed schemes, or public demands, even if they are embedded in narrative sections or human-interest anecdotes.

NOTE: Always surface the most pressing and impactful concerns. They must be prioritized over secondary or minor issues.
CRITICAL: BE COMPREHESIVE AND INCLUDE AS MANY POINTS AS POSSIBLE.

🧾 Example Output 1: Paragraph Style with Instances
Cement Lorries – Road Accidents and Public Grievance
-Residents of Ariyalur have raised long-standing complaints about cement lorries causing frequent accidents and traffic blocks. Activists point to specific incidents in 2014 and 2016 that resulted in major fatalities. People living along the Ariyalur–Jayankondam stretch suffer from high-decibel horns and dust pollution, demanding strict regulation of lorry routes and parking zones.
Illegal Mining
-Villages such as Panangur, Reddipalayam, and Unjini have reported encroachment by private limestone miners. Locals, along with legal advocates, demand stricter enforcement, demarcation of poramboke land, and action against violations affecting water bodies and public land.

🧾Example Output 2: Thematic Demands (Bullet Format)
Water and Environment
-Ban red soil quarrying to protect groundwater and irrigation sources.
-Desilt feeder canals and restore irrigation flow from Perumal Lake.
-Evict illegal lake encroachments for tank-based farming revival.
Infrastructure Development
-Reconstruct roads in Idukadu to ensure ambulance access.
-Repair potholes near Kurinjipadi station and complete roadworks in Annadhanampettai.
-Build the pending flyover at Vadalur railway gate.

🔒 Constraints:
-Use only the context provided.
-Be factual, neutral, and to the point.
-Do not hallucinate or add inferred demands not explicitly mentioned.

Relevant Information / Context:
{context}

MAKE MINIMAL CHANGES TO THE RETRIEVED CONTEXT AND JUST ORGANISE THE INFORMATION MORE APPROPRIATELY."""

class HumanStoryAgent(BaseAgent):
    """Agent for extracting human stories and personal narratives"""
    
    def get_name(self) -> str:
        return "human_stories"
    
    def get_query(self) -> str:
        return "Community, accident, town, village, local names, indian city, taluka, rural, panchayat, ward, block, district, killed, suffer, date, family, assaulted, death, year old, individual stories, daily struggles, water issues, hospital access, transport difficulty, personal narratives, emotional impact, family problems, near, around, surrounding, close to, neighbouring, area, specific location names, village names, town names, locality names, personal testimonies, individual experiences, human impact stories"
    
    def get_prompt(self) -> str:
        return """You are tasked with writing one or more empathetic human stories based solely on the relevant information provided below.

Each story should center around a **real, specific instance** of suffering or injustice that emotionally affected individuals and their community. If **only one powerful story** is evident, focus on it. If the context contains **multiple strong stories**, you may write more than one. If no impactful story is found in the context, skip this section entirely.

=======================
Required Elements (per story):
=======================

1. Identity Anchor:
   - name, location, occupation, age

2. Emotional Impact:
   - Highlight the dominant emotion (grief, rage, fear, helplessness)
   - Describe physical or behavioral signs of emotion (e.g., "clutching her child’s torn uniform", "his hands shook as he recounted the day")

3. Systemic Failure:
   - Link the suffering to institutional neglect or political inaction
   - Mention duration (e.g., "for 3 years", "since 2021"), petitions ignored, or broken promises

NOTE: Always surface the most pressing and impactful concerns. They must be prioritized over secondary or minor issues and if any of these categories are not specified, you dont have to mention these are not specificed. Make sure the numbering is correct if you adding multiple stories.
CRITICAL: BE COMPREHESIVE AND INCLUDE AS MANY POINTS AS POSSIBLE.

Critical Requirements for Information Depth:
-Extract all specific factual references from the source, including names of villages, towns, localities, constituencies, crop and livelihood types, government schemes, infrastructure names, and community identities (e.g., SC, MBC, fisherfolk, tenant farmers).
-Capture fine-grained local context—such as affected areas, stakeholder testimonies, timeline events, project locations, or specific administrative bodies or officials mentioned.
-Avoid vague generalizations; instead, emphasize grounded, verifiable details (e.g., “Semmankuppam unmanned railway crossing,” “Dalits in Melvadakuthu without burial road,” or “Mahalir Urimai scheme leaving out 50% women in Cuddalore”).
-Include project status (e.g., pending, incomplete, failed) and implementation insights, like delays, corruption allegations, bureaucratic hurdles, or community protest history, if specified.
-Prioritize hyperlocal precision over summary: include real names, incidents, numerical data, and any public grievances, protests, or human stories if explicitly found in the document.
-Mention all forms of social, economic, governance, or ecological impacts, especially where specific demographics (e.g., “banana farmers in Kurinjipadi,” “students of Periyar Government Arts College”) are referenced.
-Capture references to broken promises, neglected communities, delayed schemes, or public demands, even if they are embedded in narrative sections or human-interest anecdotes.

=======================
Constraints:
=======================
- Use only the information provided in the context below.
- Do not invent names, events, or emotions beyond what is given.
- Prioritize vivid, empathetic language, grounded in reality.
- Use short narrative paragraphs for each story.
- If no valid stories are present, return nothing.
- Write in paragraph style

=======================
Example Outputs:
=======================
Example Output 1: Focused Single Story
A Tragedy on the Tracks – The Children Who Never Came Home
On July 8, 2025, the village of Semmamkuppam awoke to horror. A school van crossing an unmanned railway line was hit by a speeding train. Three children died instantly. Schoolbags, lunch boxes, and books lay strewn along the tracks.

One of them was Kavya, 9 years old, who had just learned to write cursive. Her father, a farmhand, sat by the site clutching her tiny shoe, unable to speak. Neighbors wept as they remembered her infectious laughter.

Villagers had repeatedly submitted petitions demanding safety measures—gates, a signal, or an underpass—but officials blamed delays and file movements. The tragedy left not just three families but an entire village shattered by a preventable loss.

Example Output 2: Multiple Stories (if richly supported by context)
Stories from Ariyalur: Crushed by Corruption and Neglect
1. The Man Who Dared to Refuse a Bribe
Parani, a small-time tailor in Cuddalore Town, wanted to build a home for his daughter’s marriage. But when he submitted his house plan, an aide to the Mayor demanded ₹50,000. Instead of paying, Parani recorded the demand and tipped off authorities. The man was caught in a sting operation.
For many like him, silence is survival. But Parani’s courage laid bare the corruption that chokes even the most basic rights — shelter, dignity, and honesty.

2. Assaulted for Speaking Up
In Mettupalayam Road, a young man raised concerns about overflowing garbage bins. Days later, he was assaulted by unknown men, his collarbone broken. Locals whispered it was retaliation. The area had been suffering from erratic garbage clearance, and the youth had simply demanded accountability.
His injury became a symbol of what happens when the poor question power. The bins were removed the next day — but justice for the attacker never came.

3. Crowbars and Coercion in Vannarapalayam
Footage from March 2025 showed Corporation officials threatening elderly residents with crowbars, demanding overdue property taxes. Saravanan, a mechanic, recorded them cutting water lines and shouting at his 80-year-old mother. Two staffers were suspended—but the fear lingers in every knock on the door.

=======================
Relevant Information / Context:
{context}

MAKE MINIMAL CHANGES TO THE RETRIEVED CONTEXT AND JUST ORGANISE THE INFORMATION MORE APPROPRIATELY."""

class UnfulfilledPromisesAgent(BaseAgent):
    """Agent for tracking unfulfilled political promises"""
    
    def get_name(self) -> str:
        return "unfulfilled_promises"
    
    def get_query(self) -> str:
        return "DMK Promise, status, election promises, DMK’s Failures, Under-Implemented, current issues by DMK, pending projects, promises not fullfilled, failures of DMK Government, broken electoral promises, election promises commitments made political parties DMK AIADMK BJP Congress unfulfilled broken incomplete projects promises not kept, DMK 2021 manifesto, unmet promises, policy failures, unfulfilled commitments, broken assurances, scheme non-delivery, announced vs implemented, near, around, surrounding, close to, neighbouring, area, village names, town names, locality names, specific location names, constituency promises, local development projects, infrastructure promises, scheme implementation status, panchayat, ward, block, district"
    
    def get_prompt(self) -> str:
        return """Your task is to extract unfulfilled or partially implemented election promises made by any political parties mentioned in the attached documents. Focus on **constituency-level** issues and development projects that have not been completed, delayed, or failed.

Output Structure:
For each identified promise:
- Start with a **clear heading/title** that summarizes the promise.
- Follow it with a **detailed explanation** describing:
  • What the promise was  
  • Who made it (if available)  
  • The **current status** (e.g., delayed, incomplete, stalled)  
  • Include relevant **evidence** or examples (dates, protests, opposition, public reaction, stalled tenders, etc.)

Guidelines:
- Extract only **documented** unfulfilled promises. Do not speculate or assume.
- If a party is not explicitly linked to a promise, mention it only if contextually justified.
- Be impartial — analyze all parties mentioned without bias or assumption of ruling/opposition roles.
- Highlight intra-party or administrative reasons only if **specifically mentioned** in the text.
- Multiple promises can be included if relevant. Skip the response if no such data exists.
- The number of promises does not have to be limited to any number, write based on what is available in the context.

Critical Requirements for Information Depth:
-Extract all specific factual references from the source, including names of villages, towns, localities, constituencies, crop and livelihood types, government schemes, infrastructure names, and community identities (e.g., SC, MBC, fisherfolk, tenant farmers).
-Capture fine-grained local context—such as affected areas, stakeholder testimonies, timeline events, project locations, or specific administrative bodies or officials mentioned.
-Avoid vague generalizations; instead, emphasize grounded, verifiable details (e.g., “Semmankuppam unmanned railway crossing,” “Dalits in Melvadakuthu without burial road,” or “Mahalir Urimai scheme leaving out 50% women in Cuddalore”).
-Include project status (e.g., pending, incomplete, failed) and implementation insights, like delays, corruption allegations, bureaucratic hurdles, or community protest history, if specified.
-Prioritize hyperlocal precision over summary: include real names, incidents, numerical data, and any public grievances, protests, or human stories if explicitly found in the document.
-Mention all forms of social, economic, governance, or ecological impacts, especially where specific demographics (e.g., “banana farmers in Kurinjipadi,” “students of Periyar Government Arts College”) are referenced.
-Capture references to broken promises, neglected communities, delayed schemes, or public demands, even if they are embedded in narrative sections or human-interest anecdotes.

NOTE: Always surface the most pressing and impactful concerns. They must be prioritized over secondary or minor issues.
CRITICAL: BE COMPREHESIVE AND INCLUDE AS MANY POINTS AS POSSIBLE.

Example output 1:
New Bus Stand Construction
-Initially promised to be developed near the Collector’s Office, the bus stand project was later shifted to M. Pudur. However, construction remains incomplete and has faced public opposition due to poor location choice and environmental concerns.
Mahalir Urimai Thittam (₹1,000 Scheme for Women)
-While the scheme aimed to cover all eligible women, reports indicate that nearly 50% of beneficiaries have been left out, especially in rural and semi-urban areas of Cuddalore.

Example output 2:
1. Vellaru River Check Dam –
-The DMK had promised to construct a check dam across the Vellaru River. However, the project remains stuck in the evaluation stage with no visible progress or implementation.
2. Political Infighting & Project Delays –
-Internal party conflicts are alleged to be stalling local development. Minister M.R.K. Panneerselvam is reportedly delaying the Vellaru dam project to prevent DMK’s expected candidate, Durai Saravanan, from gaining public support. This intra-party rivalry has adversely impacted farmers and the broader public.
3. Perfume Industry for Jasmine Farmers –
 Despite high jasmine flower production in the region, the DMK’s promise to establish a perfume processing unit remains unfulfilled, leaving floriculturists without the promised value-addition and market support.

Example output 3:
Industrial Growth and Local Employment
DMK Promise:
-Create 10 lakh jobs statewide and promote local industrial development in backward districts.
Status in Ariyalur:
-Despite being home to cement industries and limestone mining, local employment generation is minimal. Most jobs in these sectors go to outsiders. There has been no new industrial cluster or government-supported job park in Ariyalur since 2021. Youth migration remains high.
 
Skill Training and Naan Mudhalvan Scheme
DMK Promise:
-Offer industry-ready skill development through Naan Mudhalvan and local training centers.
Status in Ariyalur:
-While Naan Mudhalvan exists statewide, local access is poor. No permanent skill development center has been established. Many rural youth lack digital access or guidance to benefit from such schemes.

**Relevant Information / Context**:
{context}

MAKE MINIMAL CHANGES TO THE RETRIEVED CONTEXT AND JUST ORGANISE THE INFORMATION MORE APPROPRIATELY."""

class ManifestoDraftingAgent(BaseAgent):
    """Agent for drafting manifesto promises"""
    
    def get_name(self) -> str:
        return "manifesto_drafting"
    
    def get_query(self) -> str:
        return "Set up, construct, guarantee, upgrade, plan, stop, support, for every, 24x7, compensation, develop, reform empowerment, manifesto promises suggestions recommendations constituency area development local needs community, development solutions, actionable manifesto points, infrastructure upgrade proposals, realistic commitments, local, programs, access, near, around, surrounding, close to, neighbouring, area, village names, town names, locality names, specific location names, panchayat, ward, block, district, constituency development, local infrastructure, community needs, regional development"
    
    def get_prompt(self) -> str:
        return """Your task is to draft specific and actionable manifesto promises based solely on the possible manifesto promises.

Objective:
Write manifesto promises that directly address the unique problems and needs of the region. Each promise should clearly target a public issue and propose a solution that is relevant, locally grounded, and implementable.

Guidelines:
- Focus only on relevant and localized problems — avoid generic or broad promises.
- Use the information from 'Possible Manifesto Promises' to craft precise, grounded commitments.
- If multiple issues exist, suggest multiple promises. If information is limited, fewer promises are acceptable.
- Each promise should begin with a short, clear title or heading that captures the focus of the promise.
- Follow it with a concise explanation describing what will be done and how it addresses the issue.

Critical Requirements for Information Depth:
-Extract all specific factual references from the source, including names of villages, towns, localities, constituencies, crop and livelihood types, government schemes, infrastructure names, and community identities (e.g., SC, MBC, fisherfolk, tenant farmers).
-Capture fine-grained local context—such as affected areas, stakeholder testimonies, timeline events, project locations, or specific administrative bodies or officials mentioned.
-Avoid vague generalizations; instead, emphasize grounded, verifiable details (e.g., “Semmankuppam unmanned railway crossing,” “Dalits in Melvadakuthu without burial road,” or “Mahalir Urimai scheme leaving out 50% women in Cuddalore”).
-Include project status (e.g., pending, incomplete, failed) and implementation insights, like delays, corruption allegations, bureaucratic hurdles, or community protest history, if specified.
-Prioritize hyperlocal precision over summary: include real names, incidents, numerical data, and any public grievances, protests, or human stories if explicitly found in the document.
-Mention all forms of social, economic, governance, or ecological impacts, especially where specific demographics (e.g., “banana farmers in Kurinjipadi,” “students of Periyar Government Arts College”) are referenced.
-Capture references to broken promises, neglected communities, delayed schemes, or public demands, even if they are embedded in narrative sections or human-interest anecdotes.

NOTE: Always surface the most pressing and impactful concerns. They must be prioritized over secondary or minor issues.
CRITICAL: BE COMPREHESIVE AND INCLUDE AS MANY POINTS AS POSSIBLE.

Example output 1:
1. Maruthamalai a global spiritual and cultural destination  
Recognizing its religious and cultural significance to millions of devotees, we shall promise to initiate steps to grant it special heritage status and implement comprehensive infrastructure development. We will also set up a Maruthamalai Murugan Cultural Centre to promote the temple’s historical, musical, and literary legacy. Our vision is to make Maruthamalai a global spiritual and cultural destination rooted in Tamil identity and tradition.

2. Flood Management in North Selvapuram  
Permanent flood mitigation system for low-lying areas like Bharathi Nagar & Priya Nagar and construction of robust underground and stormwater drainage networks.

Example output 2:
1. Permanent House Site Pattas for Temple Land Residents  
Every family residing on temple-owned land will be granted permanent house-site pattas along with electricity, toilets, and drinking water connections under a time-bound program.

2. New Government Veterinary Hospital  
A fully equipped veterinary hospital will be established in Kurinjipadi to safeguard farmers’ livestock and reduce cattle mortality.

3. Cashew and Sugarcane Farmers’ Revival Package  
Minimum Support Price (MSP) for cashew and red sugarcane will be fixed; direct procurement centers will be opened; subsidies for cultivation inputs will be rolled out.

Possible Manifesto Promises:
{context}

MAKE MINIMAL CHANGES TO THE RETRIEVED CONTEXT AND JUST ORGANISE THE INFORMATION MORE APPROPRIATELY."""

class ReviewAssemblyAgent(BaseAgent):
    """Agent for assembling the final constituency report with user support"""
    
    def get_name(self) -> str:
        return "review_assembly"
    
    def get_query(self) -> str:
        return "village, locality, town, ward, panchayat, block, region, settlement, school, college, hospital, health centre, clinic, road, bridge, railway crossing, bus stand, transport, drainage, sewage, water supply, drinking water, groundwater, irrigation, canal, tank, pond, lake, river, flood, cyclone, erosion, pollution, land acquisition, eviction, compensation, patta, housing, scheme, subsidy, benefit, implementation, delay, pending, unemployment, joblessness, youth, farmer, fisherfolk, student, women, SC, MBC, OBC, community, protest, complaint, grievance, bribe, corruption, accident, tragedy, safety, infrastructure, public service, electricity, power cut, sanitation, toilet, TASMAC, demand, request, proposal, project, allocation, budget, scheme name"
    
    def get_prompt(self) -> str:
        return ""
    
    async def assemble(self, components: Dict[str, str], user_id: str, localised_info: str = None, output_instructions_prompt: str = None) -> str:
        """Assemble all components into a final report for a specific user"""
        
        assembly_prompt = f"""You are a political analyst preparing a **constituency development report**. Follow the below instructions:
---

## Section 1: User Instructions (optional)

User instructions are: {output_instructions_prompt if output_instructions_prompt else "None"}
If user instructions are present, follow it. You may adapt the structure or tone of your output accordingly, but **only override the default format if the user's prompt explicitly specifies a different structure or output goal**.

---

## Section 2: Must-Have Components (always apply unless explicitly overridden by user )

You must generate a constituency development report that is:
- Coherent and logically structured
- Grounded in local realities and document evidence with comprehensive information depth
- Informative, with practical and specific insights including local specificity
- Advocacy-oriented, with a mild but effective empathetic tone
- Written with urgency and actionability in mind

Target Audience:
Policymakers, political stakeholders, local media, informed voters, and the general public.

Critical Requirements for Information Depth:
-Extract all specific factual references from the source, including names of villages, towns, localities, constituencies, crop and livelihood types, government schemes, infrastructure names, and community identities (e.g., SC, MBC, fisherfolk, tenant farmers).
-Capture fine-grained local context—such as affected areas, stakeholder testimonies, timeline events, project locations, or specific administrative bodies or officials mentioned.
-Avoid vague generalizations; instead, emphasize grounded, verifiable details (e.g., “Semmankuppam unmanned railway crossing,” “Dalits in Melvadakuthu without burial road,” or “Mahalir Urimai scheme leaving out 50% women in Cuddalore”).
-Include project status (e.g., pending, incomplete, failed) and implementation insights, like delays, corruption allegations, bureaucratic hurdles, or community protest history, if specified.
-Prioritize hyperlocal precision over summary: include real names, incidents, numerical data, and any public grievances, protests, or human stories if explicitly found in the document.
-Mention all forms of social, economic, governance, or ecological impacts, especially where specific demographics (e.g., “banana farmers in Kurinjipadi,” “students of Periyar Government Arts College”) are referenced.
-Capture references to broken promises, neglected communities, delayed schemes, or public demands, even if they are embedded in narrative sections or human-interest anecdotes.

Output Rules:
-Format the output in plain text, compatible with Microsoft Word (no Markdown or special characters).
-Use the 5 section headings below in the exact order.
-Do not add summaries, conclusions, or extra commentary.
-Leave one blank line between bullets and paragraphs.

NOTE: Always surface the most pressing and impactful concerns. They must be prioritized over secondary or minor issues. Numbering of points should be correct.

📄 Report Structure and Section Guidelines

1. MAJOR PUBLIC ISSUES
-Use a clear, descriptive title for each issue.
-Write 2-6 lines of factual description for each, including specific locations, affected demographics, concrete examples, quantifiable impacts, infrastructure details (road conditions, water supply status, school facilities), and stakeholder concerns where available.
-Prioritize local specificity over general statements - include place names, crop types, specific infrastructure problems, and community-specific challenges.
-Only include issues explicitly mentioned in the context.
-Ensure comprehensive coverage - do not omit any significant issue mentioned in source material.
-Group all issues under the heading: MAJOR PUBLIC ISSUES

2. GROUND LEVEL HUMAN STORY (optional)
-Include this section only if a valid story exists in the context.
-Write a short narrative describing a real case of hardship or injustice.
-If available, include the person's name, age, village, and occupation.
-Focus on sensory details, emotional impact, and underlying systemic or political failures.
-Maintain an empathetic yet factual tone.
-Include specific local context and institutional details that contributed to the situation.
-Group this story under the heading: GROUND LEVEL HUMAN STORY

3. UNFULFILLED DMK PROMISES
-Include only constituency-level promises that are unfulfilled or partially implemented.
For each:
-Begin with a short title summarizing the promise.
-Describe the original promise, who made it (if known), its current status with specific details (e.g., delayed, stalled, incomplete), and consequences.
-Include evidence such as protests, public opposition, media reports, missed deadlines, or specific implementation gaps.
-Include specific scheme names, target areas, and measurable outcomes where mentioned in source.
-Only include verifiable or documented claims.
-Group under the heading: UNFULFILLED DMK PROMISES

4. KEY PUBLIC DEMANDS
-List each demand in bullet or list format.
Every demand must be:
-Linked to a specific location, issue, or affected group mentioned in the source
-Clearly articulated and based on concrete problems identified in the context
-Include specific infrastructure needs, scheme implementations, or service improvements
-Avoid general or unrealistic demands.
-Include demands from all major issue areas covered in the source material.
-Group under the heading: KEY PUBLIC DEMANDS

5. SUGGESTED MANIFESTO PROMISES
For each major issue or demand identified in the source, write a specific, locally relevant manifesto promise.
-Each must begin with a short heading/title.
-Follow with a paragraph explanation detailing:
-The proposed solution with specific implementation details
-The target area, community, or group (be specific about locations and demographics)
-A measurable or tangible outcome with concrete deliverables
-Reference to existing schemes or infrastructure that needs enhancement
-Ensure promises address the full spectrum of issues identified in the source material.
-Avoid generic or vague language - use specific local references and concrete commitments.
-Group under the heading: SUGGESTED MANIFESTO PROMISES

Quality Control Verification:
Before finalizing, verify that your report includes: specific place names mentioned in source, crop/industry details, government scheme names and their implementation status, affected community demographics, infrastructure specifics, and statistical information where available from the source material.

ENHANCED LOCALIZATION REQUIREMENTS:
-MANDATORY: Include specific location names in every section:
  • Village names, town names, city areas, wards, panchayats
  • Block, district, constituency, and assembly segment names
  • Infrastructure names (roads, bridges, schools, hospitals, crossings)
  • Geographic features (rivers, tanks, canals, lakes)
-DEPTH REQUIREMENTS: Aim for 8-12 detailed points per section minimum
-EVIDENCE-BASED: Each point must include concrete examples with location names
-COMPREHENSIVE COVERAGE: Extract all available issues, demands, and promises from the source

NOTE: BE COMPREHENSIVE AND INCLUDE AS MANY DETAILED POINTS AS POSSIBLE WITH SPECIFIC LOCATION NAMES.

## 📥 Relevant Information for Report Generation

Use the following pre-processed components to inform your report content:

1. Major Public Issues:  
{components.get('issue_extraction', 'No data available')}

2. Ground Level Human Story:  
{components.get('human_stories', 'No data available')}

3. Unfulfilled DMK Promises:  
{components.get('unfulfilled_promises', 'No data available')}

4. Key Public Demands:  
{components.get('public_demand', 'No data available')}

5. Suggested Manifesto Promises:
{components.get('manifesto_drafting', 'No data available')}
"""

        # Add localised info section if available
        if localised_info:
            assembly_prompt += f"\n## Localised Information to tailor the report to the user constituency: {localised_info}\n"

        # Add final localization emphasis
        assembly_prompt += """

## FINAL LOCALIZATION CHECK:
Before submitting your report, ensure that:
1. Every section contains specific location names (villages, towns, areas, wards)
2. Each point includes concrete examples with place names
3. Infrastructure and geographic features are specifically named
4. Community demographics are tied to specific locations
5. Government schemes and projects mention exact implementation areas
6. All statistics and data points reference specific localities

CRITICAL: If any section lacks specific location names, enhance it with available geographic context from the source material.
"""

        logger.info(f"Processing message to llm with prompt: {assembly_prompt}")
        try:
            response = self.llm.ainvoke_async([("user", assembly_prompt)])  # Use tuple format
            result = response["content"]
            logger.info(f"Response from llm: {result}")
            return result
        except Exception as e:
            logger.error(f"Error in assembly for user {user_id}: {e}")
            return "Document assembled from components (local processing)"
