from typing import List, Dict, Optional
import json
import boto3
from botocore.exceptions import ClientError
import logging
import time
from app.core.config import settings
from app.core.context import get_user_model

logger = logging.getLogger(__name__) 

class AWSCredentialsBase:
    """Base class for AWS credentials initialization"""
    def __init__(self, region_name: Optional[str] = None):
        self.region_name = region_name or settings.BEDROCK_REGION or "ap-south-1"
        self.aws_access_key_id = settings.BEDROCK_ACCESS_KEY or "********************"
        self.aws_secret_access_key = settings.BEDROCK_SECRET_KEY or "ExPqixMP3pmmntZHTTjXDqfQR1cowvHbLCS0B6iT"

class BedrockTitanEmbeddings(AWSCredentialsBase):
    """Custom Langchain Embeddings class for AWS Bedrock Titan Text Embeddings V2 using boto3"""
    
    def __init__(self, model_id: str = "amazon.titan-embed-text-v2:0", region_name: Optional[str] = None):
        super().__init__(region_name)
        self.model_id = model_id
        
        # Initialize boto3 client
        self.client = boto3.client(
            service_name='bedrock-runtime',
            region_name=self.region_name,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )
        
        # Test connection
        try:
            self._test_connection()
            logger.info(f"✅ AWS Bedrock client initialized for region: {self.region_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AWS Bedrock client: {e}")
            raise e
    
    def _test_connection(self):
        """Test Bedrock connection synchronously"""
        try:
            # Just test if we can create the client - we won't make an actual call here
            logger.info("✅ Bedrock client test successful")
        except Exception as e:
            logger.error(f"❌ Bedrock connection test failed: {e}")
            raise e
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text using Amazon Titan with boto3"""
        try:
            # Prepare request body
            body = json.dumps({
                "inputText": text,
                "embeddingTypes": ["float"]  # Use float embeddings for vector similarity
            })
            
            # Call Bedrock API
            response = self.client.invoke_model(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json"
            )
            
            # Parse response
            response_body = json.loads(response['body'].read())
            
            # Extract float embeddings
            embedding = response_body['embeddingsByType']['float']
            
            return embedding
            
        except ClientError as e:
            logger.error(f"❌ AWS Bedrock API error: {e}")
            raise e
        except Exception as e:
            logger.error(f"❌ Error generating embedding: {e}")
            raise e
    
    def aembed_query(self, text: str) -> List[float]:
        """Embed a single query text (now synchronous)"""
        return self._generate_embedding(text)
    
    def embed_query(self, text: str) -> List[float]:
        """Synchronous wrapper for embedding a single query"""
        return self._generate_embedding(text)
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents - required by LangChain"""
        return self._embed_documents(texts)
    
    def _embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Synchronous implementation for embedding multiple documents"""
        embeddings = []
        
        # Process in batches to avoid overwhelming the API
        batch_size = 5
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            try:
                # Process batch sequentially
                batch_embeddings = []
                for j, text in enumerate(batch):
                    try:
                        embedding = self._generate_embedding(text)
                        batch_embeddings.append(embedding)
                    except Exception as e:
                        logger.error(f"❌ Failed to embed document {i + j}: {e}")
                        # Return zero embedding as fallback
                        batch_embeddings.append([0.0] * 1024)  # Titan V2 returns 1024-dimensional embeddings
                
                embeddings.extend(batch_embeddings)
                
                # Progress logging
                if (i // batch_size + 1) % 5 == 0:
                    logger.info(f"✅ Generated embeddings for {min(i + batch_size, len(texts))}/{len(texts)} documents")
                
                # Add delay between batches to respect rate limits
                if i + batch_size < len(texts):
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"❌ Error processing batch {i//batch_size + 1}: {e}")
                # Add fallback embeddings for the entire batch
                for _ in batch:
                    embeddings.append([0.0] * 1024)
        
        return embeddings

class BedrockClaudeLLM(AWSCredentialsBase):
    """Custom LLM class for AWS Bedrock models (Claude, Mistral) using boto3"""
    
    def __init__(self, model_id: str = "mistral.mistral-large-2402-v1:0", region_name: Optional[str] = None):
        super().__init__(region_name)

        # Try to get model from global context first, fallback to parameter, then default
        context_model = get_user_model()
        if context_model:
            self.model_id = context_model
            logger.info(f"Using model from context: {context_model}")
        else:
            self.model_id = model_id
            logger.info(f"Using model from parameter/default: {model_id}")

        # Set self.model to the same value as self.model_id for compatibility
        self.model = self.model_id
        
        # Override with Bedrock-specific credentials if available
        self.region_name = getattr(settings, 'BEDROCK_REGION', None) or self.region_name
        self.aws_access_key_id = getattr(settings, 'BEDROCK_ACCESS_KEY', None) or self.aws_access_key_id
        self.aws_secret_access_key = getattr(settings, 'BEDROCK_SECRET_KEY', None) or self.aws_secret_access_key
        
        logger.info(f"Region: {self.region_name}, Access Key: {self.aws_access_key_id}, Secret Key: {self.aws_secret_access_key}")

        # Initialize boto3 client
        self.client = boto3.client(
            service_name='bedrock-runtime',
            region_name=self.region_name,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

        # Test connection
        try:
            self._test_connection()
            logger.info(f"✅ AWS Bedrock Claude LLM initialized for region: {self.region_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AWS Bedrock Claude LLM: {e}")
            raise e
    
    def _test_connection(self):
        """Test Bedrock connection synchronously"""
        try:
            logger.info("✅ Bedrock Claude LLM client test successful")
        except Exception as e:
            logger.error(f"❌ Bedrock Claude LLM connection test failed: {e}")
            raise e
    
    def _create_bedrock_request_body(self, messages: List[Dict], temperature: float = 0.3, max_tokens: int = 4000) -> Dict:
        """Create request body for different model types"""

        model_id_lower = self.model_id.lower()

        if "mistral" in model_id_lower:
            return self._create_mistral_request_body(messages, temperature, max_tokens)
        elif "claude" in model_id_lower or "anthropic" in model_id_lower:
            return self._create_claude_request_body(messages, temperature, max_tokens)
        elif "llama" in model_id_lower or "meta" in model_id_lower:
            return self._create_llama_request_body(messages, temperature, max_tokens)
        else:
            # Default fallback
            return self._create_claude_request_body(messages, temperature, max_tokens)


    def _create_llama_request_body(self, messages: List[Dict], temperature: float = 0.3, max_tokens: int = 4000) -> Dict:
        """Create request body for Meta LLaMA 3 models (Bedrock format)"""

        # Build prompt using Meta’s required instruction format
        prompt_parts = ["<|begin_of_text|>"]
        for role, content in messages:
            if role == "system":
                # LLaMA 3 doesn't officially support system role, prepend as user
                prompt_parts.append("<|start_header_id|>user<|end_header_id|>\n" + content.strip() + "\n<|eot_id|>")
            elif role in ("user", "human"):
                prompt_parts.append("<|start_header_id|>user<|end_header_id|>\n" + content.strip() + "\n<|eot_id|>")
            elif role == "assistant":
                prompt_parts.append("<|start_header_id|>assistant<|end_header_id|>\n" + content.strip() + "\n<|eot_id|>")

        # Add final assistant prompt to trigger generation
        prompt_parts.append("<|start_header_id|>assistant<|end_header_id|>\n")

        final_prompt = "\n".join(prompt_parts)

        return {
            "prompt": final_prompt,
            "temperature": temperature,
            "top_p": 0.9,
            "max_gen_len": max_tokens
        }



    def _create_mistral_request_body(self, messages: List[Dict], temperature: float = 0.3, max_tokens: int = 4000) -> Dict:
        """Create request body for Mistral models"""
        mistral_messages = []

        for message in messages:
            role, content = message
            if role == "system":
                mistral_messages.append({"role": "system", "content": content})
            elif role == "human" or role == "user":
                mistral_messages.append({"role": "user", "content": content})
            elif role == "assistant":
                mistral_messages.append({"role": "assistant", "content": content})

        request_body = {
            "messages": mistral_messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        return request_body

    def _create_claude_request_body(self, messages: List[Dict], temperature: float = 0.3, max_tokens: int = 4000) -> Dict:
        """Create request body for Anthropic Claude models"""
        # Convert messages to Claude format
        claude_messages = []
        system_message = ""

        for message in messages:
            role, content = message
            if role == "system":
                system_message = content
            elif role == "human" or role == "user":
                claude_messages.append({"role": "user", "content": content})
            elif role == "assistant":
                claude_messages.append({"role": "assistant", "content": content})

        request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "temperature": temperature,
            "max_tokens": max_tokens,
            "messages": claude_messages
        }

        # Add system message if present
        if system_message:
            request_body["system"] = system_message

        return request_body
    
    def ainvoke_async(self, messages: List[tuple], temperature: float = 0.3, max_tokens: int = 4000, max_retries: int = 2) -> Dict:
        """Invoke Claude model synchronously with retry logic (renamed for compatibility)"""
        return self._invoke_with_retry(messages, temperature, max_tokens, max_retries)
    
    def _invoke_with_retry(self, messages: List[tuple], temperature: float = 0.3, max_tokens: int = 4000, max_retries: int = 2) -> Dict:
        """Invoke Claude model synchronously with retry logic"""
        for attempt in range(max_retries + 1):
            try:
                # Prepare request body
                request_body = self._create_bedrock_request_body(messages, temperature, max_tokens)
                
                # Call Bedrock API
                response = self.client.invoke_model(
                    modelId=self.model_id,
                    contentType="application/json",
                    accept="application/json",
                    body=json.dumps(request_body)
                )
                
                # Parse response
                response_body = json.loads(response['body'].read())

                # Extract content based on model type
                content = self._extract_content_from_response(response_body)

                # Create response object similar to Azure OpenAI format
                return {
                    "content": content,
                    "usage": self._extract_usage_from_response(response_body),
                    "stop_reason": response_body.get('stop_reason', 'end_turn')
                }
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                
                if error_code == 'ThrottlingException' and attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"⚠️ Rate limit hit (attempt {attempt + 1}/{max_retries + 1}), waiting {wait_time}s")
                    time.sleep(wait_time)
                    continue
                elif error_code == 'ValidationException':
                    logger.error(f"❌ Invalid request: {e}")
                    raise e
                elif error_code == 'AccessDeniedException':
                    logger.error(f"❌ Access denied - check permissions: {e}")
                    raise e
                else:
                    logger.error(f"❌ AWS Bedrock API error: {e}")
                    if attempt < max_retries:
                        time.sleep(1)
                        continue
                    raise e
                    
            except Exception as e:
                logger.error(f"❌ Error invoking Claude model: {e}")
                if attempt < max_retries:
                    time.sleep(2)
                    continue
                raise e
        
        raise Exception("Maximum retries exceeded")

    def _extract_content_from_response(self, response_body: Dict) -> str:
        """Extract content from response based on model type"""
        model_id_lower = self.model_id.lower()

        if "mistral" in model_id_lower:
            if 'choices' in response_body and response_body['choices']:
                return response_body['choices'][0]['message']['content']
            elif 'outputs' in response_body and response_body['outputs']:
                return response_body['outputs'][0]['text']
            else:
                return response_body.get('text', str(response_body))
        elif "llama" in self.model_id.lower() or "meta" in self.model_id.lower():
            return response_body.get("generation", "")
        else:
            if 'content' in response_body and response_body['content']:
                return response_body['content'][0]['text']
            return response_body.get('text', str(response_body))


    def _extract_usage_from_response(self, response_body: Dict) -> Dict:
        """Extract usage information from response based on model type"""
        if "mistral" in self.model_id.lower():
            # Mistral usage format
            usage = response_body.get('usage', {})
            return {
                "input_tokens": usage.get('prompt_tokens', 0),
                "output_tokens": usage.get('completion_tokens', 0),
                "total_tokens": usage.get('total_tokens', 0)
            }
        else:
            # Claude usage format
            usage = response_body.get('usage', {})
            return {
                "input_tokens": usage.get('input_tokens', 0),
                "output_tokens": usage.get('output_tokens', 0),
                "total_tokens": usage.get('input_tokens', 0) + usage.get('output_tokens', 0)
            }

    def ainvoke(self, messages: List[tuple], **kwargs) -> Dict:
        """Synchronous wrapper for invoke (maintained for compatibility)"""
        return self._invoke_with_retry(messages, **kwargs)
    
