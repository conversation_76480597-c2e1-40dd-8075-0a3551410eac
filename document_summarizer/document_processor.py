import asyncio
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import PyPDF2
from docx import Document as DocxDocument
from langchain.schema import Document
import uuid

from .bedrock_utilities import BedrockTitanEmbeddings, BedrockClaudeLLM
from .preprocess_document import DocumentPreprocessor
from .semantic_chunker import SemanticChunker
from .weavite_utilities import WeaviateManager
from .hyperlink_generator import hyperlink_generator, SourceReference

logger = logging.getLogger(__name__)

class FileItemChunk:
    """Data class for file chunks with source tracking"""
    def __init__(self, content: str, tokens: int, metadata: Dict[str, Any], source_ref: Optional[SourceReference] = None):
        self.content = content
        self.tokens = tokens
        self.metadata = metadata
        self.source_ref = source_ref
        self.chunk_id = str(uuid.uuid4())[:8]  # Generate unique chunk ID

class DocumentProcessor:
    """Main document processor for semantic chunking and Weaviate storage"""
    
    def __init__(
        self,
        weaviate_manager: Optional[WeaviateManager] = None,
        embedding_model: Optional[BedrockTitanEmbeddings] = None,
        llm_model: Optional[BedrockClaudeLLM] = None
    ):
        self.weaviate_manager = weaviate_manager or WeaviateManager()
        self.embedding_model = embedding_model or BedrockTitanEmbeddings()
        self.llm_model = llm_model or BedrockClaudeLLM()
        self.preprocessor = DocumentPreprocessor(self.llm_model)
        
        # Initialize semantic chunker
        self.semantic_chunker = SemanticChunker(
            embedding_model=self.embedding_model,
            max_chunk_size=1500,
            min_chunk_size=100,
            similarity_threshold=0.75
        )
        
        logger.info("✅ DocumentProcessor initialized")
    
    async def process_file(self, file_path: str, user_id: str, collection_id: str = None) -> Dict[str, Any]:
        """Process a file and store embeddings in Weaviate"""
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_extension = file_path_obj.suffix.lower()
            logger.info(f"Processing file with extension: {file_extension}")
            
            # Process based on file type
            if file_extension == '.pdf':
                chunks = await self.process_pdf_with_semantic_chunking(file_path)
            elif file_extension == '.docx':
                chunks = await self.process_docx_with_semantic_chunking(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")
            
            logger.info(f"Generated {len(chunks)} chunks for file")
            
            # Store in Weaviate
            actual_collection_id = await self.store_chunks_in_weaviate(chunks, user_id, collection_id)
            
            total_tokens = sum(chunk.tokens for chunk in chunks)
            
            return {
                "status": "success",
                "chunks_count": len(chunks),
                "total_tokens": total_tokens,
                "collection_id": actual_collection_id
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing file: {e}")
            raise e
    
    async def process_pdf_with_semantic_chunking(self, file_path: str) -> List[FileItemChunk]:
        """Process PDF file with semantic chunking"""
        try:
            # Extract text from PDF
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                complete_text = ""
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    complete_text += f"\n\nPage {page_num + 1}:\n{page_text}"
            
            # Preprocess the document
            processed_text = await self.preprocessor.preprocess_document(complete_text)
            
            # Create semantic chunks
            semantic_chunks = await self.semantic_chunker.create_semantic_chunks(processed_text)
            
            # Convert to FileItemChunk objects with source references
            chunks = []
            file_name = Path(file_path).name

            for index, chunk in enumerate(semantic_chunks):
                # Create source reference for this chunk
                source_ref = SourceReference(
                    file_id=str(uuid.uuid4())[:8],
                    file_name=file_name,
                    chunk_id=str(uuid.uuid4())[:8],
                    page_number=None,  # PDF page tracking would need more complex logic
                    section_title=None,
                    start_char=None,
                    end_char=None,
                    content_preview=chunk[:100] + "..." if len(chunk) > 100 else chunk
                )

                chunk_obj = FileItemChunk(
                    content=chunk,
                    tokens=self.semantic_chunker.estimate_tokens(chunk),
                    metadata={
                        "chunk_index": index,
                        "source_type": "pdf",
                        "chunking_method": "semantic",
                        "file_path": file_path,
                        "file_name": file_name,
                        "chunk_id": source_ref.chunk_id
                    },
                    source_ref=source_ref
                )

                # Register chunk with hyperlink generator
                hyperlink_generator.register_chunk_source(chunk, source_ref)
                chunks.append(chunk_obj)
            logger.info(f"Generated {len(chunks)} chunks for PDF: {file_path}. Chunks {[chunk.content[:50] for chunk in chunks]}")
            return chunks
            
        except Exception as e:
            logger.error(f"❌ Error processing PDF: {e}")
            raise e
    
    async def process_docx_with_semantic_chunking(self, file_path: str) -> List[FileItemChunk]:
        """Process DOCX file with semantic chunking"""
        try:
            # Extract text from DOCX
            doc = DocxDocument(file_path)
            complete_text = ""
            
            for paragraph in doc.paragraphs:
                complete_text += paragraph.text + "\n"
            
            # Preprocess the document
            processed_text = await self.preprocessor.preprocess_document(complete_text)
            
            # Create semantic chunks
            semantic_chunks = await self.semantic_chunker.create_semantic_chunks(processed_text)
            
            # Convert to FileItemChunk objects with source references
            chunks = []
            file_name = Path(file_path).name

            for index, chunk in enumerate(semantic_chunks):
                # Create source reference for this chunk
                source_ref = SourceReference(
                    file_id=str(uuid.uuid4())[:8],
                    file_name=file_name,
                    chunk_id=str(uuid.uuid4())[:8],
                    page_number=None,  # DOCX page tracking would need more complex logic
                    section_title=self._extract_section_title(chunk),
                    start_char=None,
                    end_char=None,
                    content_preview=chunk[:100] + "..." if len(chunk) > 100 else chunk
                )

                chunk_obj = FileItemChunk(
                    content=chunk,
                    tokens=self.semantic_chunker.estimate_tokens(chunk),
                    metadata={
                        "chunk_index": index,
                        "source_type": "docx",
                        "chunking_method": "semantic",
                        "has_heading": "#" in chunk,
                        "has_bullet_points": "•" in chunk,
                        "file_path": file_path,
                        "file_name": file_name,
                        "chunk_id": source_ref.chunk_id
                    },
                    source_ref=source_ref
                )

                # Register chunk with hyperlink generator
                hyperlink_generator.register_chunk_source(chunk, source_ref)
                chunks.append(chunk_obj)
            logger.info(f"Generated {len(chunks)} chunks for PDF: {file_path}. Chunks {[chunk.content[:50] for chunk in chunks]}")

            
            return chunks
            
        except Exception as e:
            logger.error(f"❌ Error processing DOCX: {e}")
            raise e

    def _extract_section_title(self, chunk: str) -> Optional[str]:
        """Extract section title from chunk content"""
        lines = chunk.split('\n')
        for line in lines[:3]:  # Check first 3 lines
            line = line.strip()
            if line.startswith('#') or (len(line) < 100 and line.isupper()) or line.endswith(':'):
                return line.replace('#', '').strip()
        return None
    
    async def store_chunks_in_weaviate(
        self, 
        chunks: List[FileItemChunk], 
        user_id: str,
        collection_id: str = None
    ) -> str:
        """Store chunks in Weaviate with embeddings"""
        try:
            # Ensure Weaviate connection
            if not self.weaviate_manager.connect():
                raise Exception("Failed to connect to Weaviate")
            
            # Prepare documents for Weaviate
            documents = []
            for chunk in chunks:
                # Include source reference data in metadata
                source_metadata = {}
                if chunk.source_ref:
                    source_metadata = {
                        "source_file_id": chunk.source_ref.file_id,
                        "source_file_name": chunk.source_ref.file_name,
                        "source_chunk_id": chunk.source_ref.chunk_id,
                        "source_page_number": chunk.source_ref.page_number,
                        "source_section_title": chunk.source_ref.section_title,
                        "content_preview": chunk.source_ref.content_preview
                    }

                doc = Document(
                    page_content=chunk.content,
                    metadata={
                        **chunk.metadata,
                        **source_metadata,
                        "tokens": chunk.tokens,
                        "chunk_id": chunk.chunk_id
                    }
                )
                documents.append(doc)
            
            # Generate embeddings
            embeddings = []
            for doc in documents:
                embedding = self.embedding_model.aembed_query(doc.page_content)
                embeddings.append(embedding)
            
            # Create vector store using async method
            actual_collection_id = await self.weaviate_manager.create_vector_store(
                documents=documents,
                embeddings=embeddings,
                user_id=user_id,
                collection_id=collection_id
            )
            
            if actual_collection_id:
                # Register chunks with hyperlink generator for source tracking
                logger.info(f"🔍 Starting chunk registration for {len(chunks)} chunks")
                registered_count = 0
                for i, chunk in enumerate(chunks):
                    logger.info(f"🔍 Checking chunk {i}: has source_ref = {hasattr(chunk, 'source_ref')}")
                    if hasattr(chunk, 'source_ref') and chunk.source_ref:
                        logger.info(f"✅ Registering chunk {i} with source_ref: {chunk.source_ref.file_name}")
                        hyperlink_generator.register_chunk_source(chunk.content, chunk.source_ref)
                        registered_count += 1
                    else:
                        logger.warning(f"❌ Chunk {i} missing source_ref: {chunk.content[:100]}...")

                logger.info(f"✅ Successfully stored {len(chunks)} chunks in Weaviate and registered {registered_count} chunks with hyperlink generator")
                return actual_collection_id
            else:
                raise Exception("Failed to create vector store")
            
        except Exception as e:
            logger.error(f"❌ Error storing chunks in Weaviate: {e}")
            raise e