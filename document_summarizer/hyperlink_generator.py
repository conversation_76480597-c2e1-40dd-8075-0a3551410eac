import logging
import re
from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Any
from dataclasses import dataclass
import uuid
import json

logger = logging.getLogger(__name__)

@dataclass
class SourceReference:
    """Represents a reference to a source document"""
    file_id: str
    file_name: str
    chunk_id: str
    page_number: Optional[int] = None
    section_title: Optional[str] = None
    start_char: Optional[int] = None
    end_char: Optional[int] = None
    content_preview: Optional[str] = None

@dataclass
class HyperlinkData:
    """Represents hyperlink data for a piece of content"""
    hyperlink_id: str
    source_ref: SourceReference
    anchor_text: str
    context: str

class HyperlinkGenerator:
    """Generates hyperlinks for content based on source document references"""
    
    def __init__(self):
        self.hyperlink_registry: Dict[str, HyperlinkData] = {}
        self.content_to_source_map: Dict[str, List[SourceReference]] = {}
    
    def register_chunk_source(self, chunk_content: str, source_ref: SourceReference):
        """Register a chunk with its source reference"""
        content_key = self._normalize_content(chunk_content)
        if content_key not in self.content_to_source_map:
            self.content_to_source_map[content_key] = []
        self.content_to_source_map[content_key].append(source_ref)
        logger.debug(f"Registered chunk source: {source_ref.file_name} - {content_key[:50]}...")
    
    def _normalize_content(self, content: str) -> str:
        """Normalize content for matching"""
        # Remove extra whitespace and normalize
        return re.sub(r'\s+', ' ', content.strip().lower())
    
    def _find_best_source_match(self, text_segment: str) -> Optional[SourceReference]:
        """Find the best source reference for a text segment"""
        normalized_segment = self._normalize_content(text_segment)

        # Try exact match first
        if normalized_segment in self.content_to_source_map:
            return self.content_to_source_map[normalized_segment][0]

        # Try partial matches
        best_match = None
        best_score = 0

        for content_key, source_refs in self.content_to_source_map.items():
            # Calculate similarity score
            if len(normalized_segment) < 10:  # Skip very short segments
                continue

            # Check if segment is contained in the source content
            if normalized_segment in content_key:
                score = len(normalized_segment) / len(content_key)
                if score > best_score:
                    best_score = score
                    best_match = source_refs[0]

            # Check if source content is contained in segment
            elif content_key in normalized_segment:
                score = len(content_key) / len(normalized_segment)
                if score > best_score and score > 0.2:  # Lower threshold
                    best_score = score
                    best_match = source_refs[0]

            # Check for word overlap (more flexible matching)
            else:
                segment_words = set(normalized_segment.split())
                content_words = set(content_key.split())
                common_words = segment_words.intersection(content_words)

                if len(common_words) >= 3:  # At least 3 common words
                    overlap_score = len(common_words) / max(len(segment_words), len(content_words))
                    if overlap_score > best_score and overlap_score > 0.3:
                        best_score = overlap_score
                        best_match = source_refs[0]

        return best_match
    
    def generate_hyperlinks_for_content(self, content: str, content_type: str = "general") -> str:
        """Generate hyperlinks for content and return modified content with hyperlink markers"""
        if not content or not self.content_to_source_map:
            return content
        
        # Split content into sentences/points for hyperlink generation
        sentences = self._split_into_linkable_segments(content)
        modified_content = content
        
        for sentence in sentences:
            if len(sentence.strip()) < 30:  # Skip very short sentences
                continue
                
            source_ref = self._find_best_source_match(sentence)
            if source_ref:
                hyperlink_id = str(uuid.uuid4())[:8]
                
                # Create hyperlink data
                hyperlink_data = HyperlinkData(
                    hyperlink_id=hyperlink_id,
                    source_ref=source_ref,
                    anchor_text=sentence.strip(),
                    context=content_type
                )
                
                self.hyperlink_registry[hyperlink_id] = hyperlink_data
                
                # Add hyperlink marker to the content
                hyperlink_marker = f" [[source:{hyperlink_id}]]"
                
                # Find the sentence in the content and add the marker
                sentence_pattern = re.escape(sentence.strip())
                modified_content = re.sub(
                    f"({sentence_pattern})",
                    f"\\1{hyperlink_marker}",
                    modified_content,
                    count=1
                )
                
                logger.debug(f"Generated hyperlink {hyperlink_id} for content from {source_ref.file_name}")
        
        return modified_content
    
    def _split_into_linkable_segments(self, content: str) -> List[str]:
        """Split content into segments that can be hyperlinked"""
        segments = []

        # Split by bullet points first
        bullet_pattern = r'(?:^|\n)[-•*]\s*([^-•*\n]+(?:\n(?![-•*])[^\n]*)*)'
        bullet_matches = re.findall(bullet_pattern, content, re.MULTILINE)
        for match in bullet_matches:
            clean_match = match.strip()
            if len(clean_match) > 20:  # Only include substantial bullet points
                segments.append(clean_match)

        # Split remaining content by sentences
        remaining_content = re.sub(bullet_pattern, '', content, flags=re.MULTILINE)

        # Split by sentences (more comprehensive)
        sentence_pattern = r'(?<=[.!?])\s+'
        sentences = re.split(sentence_pattern, remaining_content)

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20 and not sentence.startswith('#'):  # Skip headers and short sentences
                segments.append(sentence)

        # Also try splitting by lines for cases where sentences aren't properly punctuated
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if (len(line) > 30 and
                not line.startswith('#') and
                not line.startswith('-') and
                not line.startswith('*') and
                not line.startswith('•')):
                segments.append(line)

        # Remove duplicates while preserving order
        seen = set()
        unique_segments = []
        for segment in segments:
            normalized = self._normalize_content(segment)
            if normalized not in seen:
                seen.add(normalized)
                unique_segments.append(segment)

        return unique_segments
    
    def get_hyperlink_data(self, hyperlink_id: str) -> Optional[HyperlinkData]:
        """Get hyperlink data by ID"""
        return self.hyperlink_registry.get(hyperlink_id)
    
    def get_all_hyperlinks(self) -> Dict[str, Dict[str, Any]]:
        """Get all hyperlinks as a serializable dictionary"""
        result = {}
        for hyperlink_id, hyperlink_data in self.hyperlink_registry.items():
            result[hyperlink_id] = {
                "hyperlink_id": hyperlink_data.hyperlink_id,
                "source_ref": {
                    "file_id": hyperlink_data.source_ref.file_id,
                    "file_name": hyperlink_data.source_ref.file_name,
                    "chunk_id": hyperlink_data.source_ref.chunk_id,
                    "page_number": hyperlink_data.source_ref.page_number,
                    "section_title": hyperlink_data.source_ref.section_title,
                    "start_char": hyperlink_data.source_ref.start_char,
                    "end_char": hyperlink_data.source_ref.end_char,
                    "content_preview": hyperlink_data.source_ref.content_preview
                },
                "anchor_text": hyperlink_data.anchor_text,
                "context": hyperlink_data.context
            }
        return result
    
    def clear_registry(self):
        """Clear all registered hyperlinks and sources"""
        self.hyperlink_registry.clear()
        self.content_to_source_map.clear()
        logger.info("Cleared hyperlink registry")

# Global instance
hyperlink_generator = HyperlinkGenerator()
