import asyncio
import logging
from typing import List, Optional
from pathlib import Path
import uuid

from document_summarizer.document_processor import DocumentProcessor
from document_summarizer.summarise import PoliticalAnalysisAgents
from document_summarizer.weavite_utilities import WeaviateManager
from document_summarizer.bedrock_utilities import BedrockTitanEmbeddings, BedrockClaudeLLM

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentAnalysisPipeline:
    """Complete pipeline for document analysis with political insights"""
    
    def __init__(self):

        # Initialize core components
        self.weaviate_manager = WeaviateManager()
        self.embedding_model = BedrockTitanEmbeddings()
        self.llm_model = BedrockClaudeLLM()
        
        # Initialize document processor
        self.document_processor = DocumentProcessor(
            weaviate_manager=self.weaviate_manager,
            embedding_model=self.embedding_model,
            llm_model=self.llm_model
        )
        
        # Initialize political analysis agents
        self.analysis_agents = PoliticalAnalysisAgents(
            weaviate_client=self.weaviate_manager
        )
        
        logger.info("✅ Document Analysis Pipeline initialized")
    
    async def process_documents(self, document_paths: List[str], user_id: str, collection_id: str = None, file_metadata: dict = None) -> str:
        """Process multiple documents and return the collection ID"""
        
        for i, doc_path in enumerate(document_paths):
            logger.info(f"🔄 Processing document {i+1}/{len(document_paths)}: {doc_path}")

            try:
                # Get file metadata for this document path
                current_file_metadata = file_metadata.get(doc_path) if file_metadata else None
                result = await self.document_processor.process_file(doc_path, user_id, collection_id, current_file_metadata)
                if result["status"] == "success":
                    collection_id = result["collection_id"]  # Use the returned collection_id for subsequent docs
                    logger.info(f"✅ Document processed: {result['chunks_count']} chunks, {result['total_tokens']} tokens")
                else:
                    logger.error(f"❌ Failed to process {doc_path}")
            except Exception as e:
                logger.error(f"❌ Error processing {doc_path}: {e}")
                continue
        
        return collection_id
    
    async def generate_analysis_report(self, file_ids: List[str], profile: Optional[dict] = None) -> str:
        """Generate comprehensive political analysis report"""
        if not file_ids:
            return "No documents were successfully processed."
        
        logger.info("🚀 Starting political analysis report generation...")
        
        try:
            # Generate enhanced report using all processed documents
            report = await self.analysis_agents.generate_enhanced_report(file_ids, profile)
            logger.info("✅ Political analysis report generated successfully")
            return report
        except Exception as e:
            logger.error(f"❌ Failed to generate analysis report: {e}")
            return f"Error generating report: {str(e)}"
    
    async def run_analysis(self, user_id: str, collection_id: str, document_paths: List[str] = None, output_instructions_prompt: str = None, important_keywords: str = None, file_metadata: dict = None) -> str:
        """Run the complete analysis pipeline"""
        # IMPORTANT: output_instructions_prompt must contain {} to format the components in later function
        logger.info("🚀 Starting Political Analysis Pipeline")
        
        actual_collection_id = collection_id
        # Process documents
        if document_paths:
            actual_collection_id = await self.process_documents(document_paths, user_id, collection_id, file_metadata)
        
        if not actual_collection_id:
            raise Exception("Failed to process any documents")
        
        # Generate analysis report
        logger.info("📊 Generating political analysis report...")
        report = await self.analysis_agents.generate_enhanced_report(user_id, actual_collection_id, output_instructions_prompt, important_keywords)
        
        logger.info("✅ Analysis pipeline completed successfully!")
        return report
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self.weaviate_manager, 'close'):
            self.weaviate_manager.close()
        logger.info("🧹 Pipeline cleanup completed")

async def main():
    """Example usage of the complete pipeline"""
    
    # Document paths to process
    document_paths = [
        "D:\Desktop\AI Planet\Sources-20250715T121132Z-1-001-20250719T175202Z-1-001\Sources-20250715T121132Z-1-001\Sources\Ariyalur\Ariyalur Assembly Constituency.docx",
        "D:\Desktop\AI Planet\Sources-20250715T121132Z-1-001-20250719T175202Z-1-001\Sources-20250715T121132Z-1-001\Sources\Ariyalur\Ariyalur.docx"    ]

    
    # Initialize pipeline
    pipeline = DocumentAnalysisPipeline()
    
    try:
        # Run complete pipeline
        report = await pipeline.run_analysis(
            document_paths=document_paths,
            user_id="user123",
            collection_id=uuid.uuid4(),
            output_instructions_prompt="Generate a comprehensive analysis report.",
            important_keywords="water shortage, dam leakage"
        )
        
        print("📋 ANALYSIS REPORT PREVIEW:")
        print("=" * 50)
        print(report[:1000] + "..." if len(report) > 1000 else report)
        print("=" * 50)
        
    except Exception as e:
        logger.error(f"❌ Pipeline execution failed: {e}")
    finally:
        # Cleanup
        pipeline.cleanup()

if __name__ == "__main__":
    # Run the pipeline
    asyncio.run(main())
