import re
import logging
from typing import Optional
from .bedrock_utilities import BedrockClaudeLLM

logger = logging.getLogger(__name__)

class DocumentPreprocessor:
    """Document preprocessor for language detection and translation"""
    
    def __init__(self, bedrock_llm: Optional[BedrockClaudeLLM] = None):
        self.bedrock_llm = bedrock_llm or BedrockClaudeLLM()
        logger.info("✅ DocumentPreprocessor initialized")
    
    def detect_language(self, text: str) -> str:
        """Detect if text is Tamil or English using Unicode range"""
        # Tamil Unicode range: U+0B80-U+0BFF
        tamil_regex = re.compile(r'[\u0B80-\u0BFF]')
        if tamil_regex.search(text):
            return "tamil"
        return "english"
    
    async def translate_tamil_to_english(self, tamil_text: str) -> str:
        """Translate Tamil text to English using Claude via Bedrock"""
        try:
            messages = [
                ("system", "You are a professional Tamil to English translator. Preserve formatting tags like headings and bullet points. Maintain the original structure and meaning."),
                ("user", f"Translate this Tamil text to English while preserving all formatting:\n\n{tamil_text}")
            ]
            
            response = self.bedrock_llm.ainvoke_async(messages, temperature=0.3, max_tokens=2000)
            result = response.get('content', tamil_text)
            logger.info(f"✅ Translation completed successfully, transflated text is \n {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Translation failed: {e}")
            return tamil_text  # Return original text if translation fails
    
    async def preprocess_document(self, content: str) -> str:
        """Preprocess document by detecting language and translating if needed"""
        language = self.detect_language(content)
        logger.info(f"Detected language: {language}")
        
        if language == "tamil":
            return await self.translate_tamil_to_english(content)
        
        return content
