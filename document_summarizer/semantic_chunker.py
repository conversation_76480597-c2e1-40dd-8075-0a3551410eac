import logging
import re
import hashlib
import os
from typing import List, Async<PERSON>enerator
from pathlib import Path
import PyPDF2
from docx import Document as DocxDocument
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter, SentenceTransformersTokenTextSplitter
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

from document_summarizer.bedrock_utilities import BedrockTitanEmbeddings

try:
    import tiktoken
except ImportError:
    tiktoken = None

logger = logging.getLogger(__name__)

class SemanticChunker:
    """Document processor using Semantic Chunking based on embedding similarity."""
    
    def __init__(
        self,
        embedding_model=None,
        max_chunk_size: int = 1500,
        min_chunk_size: int = 200,
        similarity_threshold: float = 0.7
    ):
        self.embedding_model = embedding_model or BedrockTitanEmbeddings()
        self.max_chunk_size = max_chunk_size
        self.min_chunk_size = min_chunk_size # Used as a target for semantic chunks
        self.similarity_threshold = similarity_threshold

        # For initial splitting into smaller units (e.g., sentences)
        self.sentence_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,  # Smaller chunk size for initial semantic units
            chunk_overlap=50,
            length_function=len,
            separators=["\n\n", "\n", ". ", "? ", "! ", "; ", ": ", " ", ""]
        )

        # The RecursiveCharacterTextSplitter for fallback/compatibility if semantic chunking fails or for very long documents
        self.recursive_text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=max_chunk_size,
            chunk_overlap=250,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base") if tiktoken else None
            if self.tokenizer:
                logger.info("✅ Tokenizer loaded successfully")
        except Exception as e:
            self.tokenizer = None
            logger.warning(f"⚠️ Failed to load tokenizer: {e}")
            logger.warning("💡 Falling back to word count estimation")
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text += f"\n[PAGE {page_num + 1}]\n{page_text}\n"
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to extract page {page_num + 1} from {file_path}: {e}")
                        continue

                if not text.strip():
                    logger.warning(f"⚠️ No text extracted from PDF: {file_path}")
                else:
                    logger.info(f"✅ Extracted {len(text)} characters from PDF: {os.path.basename(file_path)}")

                return text
        except Exception as e:
            logger.error(f"❌ Failed to process PDF {file_path}: {e}")
            return ""
    
    def extract_text_from_docx(self, file_path: str) -> str:
        try:
            doc = DocxDocument(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    if len(paragraph.text) < 100 and (paragraph.text.isupper() or paragraph.text.istitle()):
                        text += f"\n[HEADING]\n{paragraph.text}\n"
                    else:
                        text += f"{paragraph.text}\n"

            if not text.strip():
                logger.warning(f"⚠️ No text extracted from DOCX: {file_path}")
            else:
                logger.info(f"✅ Extracted {len(text)} characters from DOCX: {os.path.basename(file_path)}")

            return text
        except Exception as e:
            logger.error(f"❌ Failed to process DOCX {file_path}: {e}")
            return ""

    def extract_text_from_txt(self, file_path: str) -> str:
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    text = file.read()
                    if text.strip():
                        logger.info(f"✅ Extracted {len(text)} characters from TXT: {os.path.basename(file_path)} (encoding: {encoding})")
                        return text
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"❌ Failed to process TXT {file_path} with {encoding}: {e}")
                continue

        logger.error(f"❌ Failed to read TXT file with any encoding: {file_path}")
        return ""
    
    def clean_text(self, text: str) -> str:
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = re.sub(r'[^\w\s.,!?;:()\-\[\]"\n]', '', text)
        return text.strip()
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count"""
        return len(self.tokenizer.encode(text)) if self.tokenizer else len(text.split())

    def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generates embeddings for a list of texts asynchronously."""
        embeddings = []
        for text in texts:
            embedding =  self.embedding_model.embed_query(text)
            embeddings.append(embedding)
        return embeddings
    
    async def create_chunks(self, text: str, file_path: str) -> List[Document]:
        cleaned_text = self.clean_text(text)
        
        # Step 1: Split into smaller semantic units (e.g., sentences or small paragraphs)
        # Using the sentence_splitter for this initial granular splitting
        small_units = self.sentence_splitter.split_text(cleaned_text)
        
        if not small_units:
            logger.warning(f"⚠️ No small units generated for semantic chunking from {file_path}. Falling back to recursive text splitter.")
            return self.create_recursive_chunks(cleaned_text, file_path) # Fallback

        try:
            # Step 2: Get embeddings for each small unit
            unit_embeddings =  self._get_embeddings(small_units)

            if len(unit_embeddings) < 2: # Cannot perform similarity if less than 2 units
                logger.warning(f"⚠️ Insufficient units ({len(unit_embeddings)}) for semantic chunking from {file_path}. Falling back to recursive text splitter.")
                return self.create_recursive_chunks(cleaned_text, file_path) # Fallback

            # Step 3: Calculate cosine similarity between adjacent unit embeddings
            similarities = []
            for i in range(len(unit_embeddings) - 1):
                sim = cosine_similarity([unit_embeddings[i]], [unit_embeddings[i+1]])[0][0]
                similarities.append(sim)

            # Step 4: Identify semantic breaks
            break_points = [i for i, sim in enumerate(similarities) if sim < self.similarity_threshold]
            
            # Step 5: Aggregate small units into semantic chunks
            semantic_chunks = []
            current_chunk_start_index = 0

            for bp in break_points:
                current_chunk_text = " ".join(small_units[current_chunk_start_index : bp + 1])
                # Ensure the chunk is not excessively large
                if len(current_chunk_text) > self.max_chunk_size:
                    # If the semantic chunk is too large, fall back to recursive splitting for this segment
                    logger.info(f"💡 Semantic chunk exceeded max_chunk_size. Recursively splitting sub-segment.")
                    sub_chunks = self.recursive_text_splitter.split_text(current_chunk_text)
                    semantic_chunks.extend(sub_chunks)
                elif len(current_chunk_text) >= self.min_chunk_size:
                    semantic_chunks.append(current_chunk_text)
                else:
                    # If the chunk is too small, try to merge with the next one if possible
                    # This simple merge is for cases where semantic breaks are too frequent for small units
                    if bp + 1 < len(small_units):
                        next_unit = small_units[bp + 1]
                        merged_text = current_chunk_text + " " + next_unit
                        if len(merged_text) <= self.max_chunk_size:
                            small_units[bp + 1] = merged_text # Merge by modifying the next unit
                            logger.debug(f"Merged small chunk due to min_chunk_size constraint.")
                        else:
                            # If merging makes it too big, just add the current small chunk if it's not empty
                            if current_chunk_text.strip():
                                semantic_chunks.append(current_chunk_text)
                    else: # Last small chunk, add if not empty
                        if current_chunk_text.strip():
                            semantic_chunks.append(current_chunk_text)

                current_chunk_start_index = bp + 1

            # Add the last chunk
            final_chunk_text = " ".join(small_units[current_chunk_start_index:])
            if final_chunk_text.strip():
                if len(final_chunk_text) > self.max_chunk_size:
                     logger.info(f"💡 Final semantic chunk exceeded max_chunk_size. Recursively splitting sub-segment.")
                     sub_chunks = self.recursive_text_splitter.split_text(final_chunk_text)
                     semantic_chunks.extend(sub_chunks)
                else:
                    semantic_chunks.append(final_chunk_text)

            # If no semantic chunks were created or they are too small/large, fall back to recursive
            if not semantic_chunks:
                 logger.warning(f"⚠️ No effective semantic chunks created for {file_path}. Falling back to recursive text splitter.")
                 return self.create_recursive_chunks(cleaned_text, file_path)

            # Post-process semantic chunks to ensure they meet size criteria if possible
            final_processed_chunks = []
            current_combined_chunk = ""
            for chunk in semantic_chunks:
                if self.estimate_tokens(current_combined_chunk + chunk) <= self.max_chunk_size and \
                   len(current_combined_chunk + chunk) <= self.max_chunk_size:
                    current_combined_chunk += (" " + chunk if current_combined_chunk else chunk)
                else:
                    if current_combined_chunk.strip():
                        final_processed_chunks.append(current_combined_chunk)
                    current_combined_chunk = chunk
            if current_combined_chunk.strip():
                final_processed_chunks.append(current_combined_chunk)

            # One final pass to break down any chunks that are still too large
            # This can happen if a semantic boundary was not found for a very long section
            oversized_chunks = []
            for chunk in final_processed_chunks:
                if len(chunk) > self.max_chunk_size:
                    logger.info(f"💡 Post-processing: Breaking down an oversized semantic chunk with recursive splitter.")
                    oversized_chunks.extend(self.recursive_text_splitter.split_text(chunk))
                else:
                    oversized_chunks.append(chunk)
            
            # Filter out empty chunks that might result from splitting
            final_semantic_chunks = [c for c in oversized_chunks if c.strip()]

            if not final_semantic_chunks:
                logger.warning(f"⚠️ Semantic chunking resulted in no chunks for {file_path}. Falling back to recursive text splitter.")
                return self.create_recursive_chunks(cleaned_text, file_path)

            chunks_to_process = final_semantic_chunks

        except Exception as e:
            logger.error(f"❌ Error during semantic chunking for {file_path}: {e}. Falling back to recursive text splitter.")
            chunks_to_process = self.recursive_text_splitter.split_text(cleaned_text) # Fallback

        documents = []
        file_name = Path(file_path).name
        doc_type = Path(file_path).suffix.lower()
        
        for i, chunk in enumerate(chunks_to_process):
            token_count = self.estimate_tokens(chunk)
            
            section_type = "content"
            if "[PAGE" in chunk:
                section_type = "page_header"
            elif "[HEADING]" in chunk:
                section_type = "heading"
            
            page_number = None
            page_match = re.search(r'\[PAGE (\d+)\]', chunk)
            if page_match:
                page_number = int(page_match.group(1))
            
            doc = Document(
                page_content=chunk,
                metadata={
                    "source": file_name,
                    "chunk_id": i,
                    "doc_type": doc_type,
                    "section_type": section_type,
                    "page_number": page_number,
                    "total_chunks": len(chunks_to_process),
                    "character_count": len(chunk),
                    "token_count": token_count
                }
            )
            documents.append(doc)
        
        return documents

    def create_recursive_chunks(self, text: str, file_path: str) -> List[Document]:
        """A dedicated method for recursive splitting as a fallback or for direct use."""
        cleaned_text = self.clean_text(text)
        chunks = self.recursive_text_splitter.split_text(cleaned_text)
        
        documents = []
        file_name = Path(file_path).name
        doc_type = Path(file_path).suffix.lower()
        
        for i, chunk in enumerate(chunks):
            token_count = self.estimate_tokens(chunk)
            
            section_type = "content"
            if "[PAGE" in chunk:
                section_type = "page_header"
            elif "[HEADING]" in chunk:
                section_type = "heading"
            
            page_number = None
            page_match = re.search(r'\[PAGE (\d+)\]', chunk)
            if page_match:
                page_number = int(page_match.group(1))
            
            doc = Document(
                page_content=chunk,
                metadata={
                    "source": file_name,
                    "chunk_id": i,
                    "doc_type": doc_type,
                    "section_type": section_type,
                    "page_number": page_number,
                    "total_chunks": len(chunks),
                    "character_count": len(chunk),
                    "token_count": token_count
                }
            )
            documents.append(doc)
        
        return documents
    
    async def create_semantic_chunks(self, text: str) -> List[str]:
        """
        Public method to create semantic chunks.
        This method is made async to accommodate the aembed_query call.
        """
        cleaned_text = self.clean_text(text)
        
        # Step 1: Split into smaller semantic units
        small_units = self.sentence_splitter.split_text(cleaned_text)
        
        if not small_units:
            logger.warning(f"⚠️ No small units generated for semantic chunking. Returning empty list.")
            return []

        try:
            # Step 2: Get embeddings for each small unit
            unit_embeddings =  self._get_embeddings(small_units)

            if len(unit_embeddings) < 2:
                logger.warning(f"⚠️ Insufficient units ({len(unit_embeddings)}) for semantic chunking. Returning single unit if exists.")
                return [small_units[0]] if small_units else []

            # Step 3: Calculate cosine similarity between adjacent unit embeddings
            similarities = []
            for i in range(len(unit_embeddings) - 1):
                sim = cosine_similarity([unit_embeddings[i]], [unit_embeddings[i+1]])[0][0]
                similarities.append(sim)

            # Step 4: Identify semantic breaks
            break_points = [i for i, sim in enumerate(similarities) if sim < self.similarity_threshold]
            
            # Step 5: Aggregate small units into semantic chunks
            semantic_chunks_list = []
            current_chunk_start_index = 0

            for bp in break_points:
                current_chunk_text = " ".join(small_units[current_chunk_start_index : bp + 1])
                # Ensure the chunk is not excessively large
                if len(current_chunk_text) > self.max_chunk_size:
                    # If the semantic chunk is too large, fall back to recursive splitting for this segment
                    logger.info(f"💡 Semantic sub-chunk exceeded max_chunk_size in create_semantic_chunks. Recursively splitting.")
                    sub_chunks = self.recursive_text_splitter.split_text(current_chunk_text)
                    semantic_chunks_list.extend(sub_chunks)
                elif len(current_chunk_text) >= self.min_chunk_size:
                    semantic_chunks_list.append(current_chunk_text)
                else:
                    # Try to merge small chunks
                    if bp + 1 < len(small_units):
                        next_unit = small_units[bp + 1]
                        merged_text = current_chunk_text + " " + next_unit
                        if len(merged_text) <= self.max_chunk_size:
                            small_units[bp + 1] = merged_text
                            logger.debug(f"Merged small chunk in create_semantic_chunks.")
                        else:
                            if current_chunk_text.strip():
                                semantic_chunks_list.append(current_chunk_text)
                    else:
                        if current_chunk_text.strip():
                            semantic_chunks_list.append(current_chunk_text)
                current_chunk_start_index = bp + 1

            # Add the last chunk
            final_chunk_text = " ".join(small_units[current_chunk_start_index:])
            if final_chunk_text.strip():
                if len(final_chunk_text) > self.max_chunk_size:
                    logger.info(f"💡 Final semantic sub-chunk exceeded max_chunk_size in create_semantic_chunks. Recursively splitting.")
                    sub_chunks = self.recursive_text_splitter.split_text(final_chunk_text)
                    semantic_chunks_list.extend(sub_chunks)
                else:
                    semantic_chunks_list.append(final_chunk_text)

            # Post-process to ensure chunks are within size limits and combine if too small
            final_processed_chunks = []
            current_combined_chunk = ""
            for chunk in semantic_chunks_list:
                if self.estimate_tokens(current_combined_chunk + chunk) <= self.max_chunk_size and \
                   len(current_combined_chunk + chunk) <= self.max_chunk_size:
                    current_combined_chunk += (" " + chunk if current_combined_chunk else chunk)
                else:
                    if current_combined_chunk.strip():
                        final_processed_chunks.append(current_combined_chunk)
                    current_combined_chunk = chunk
            if current_combined_chunk.strip():
                final_processed_chunks.append(current_combined_chunk)
            
            # Final pass to break down any chunks that are still too large
            oversized_chunks = []
            for chunk in final_processed_chunks:
                if len(chunk) > self.max_chunk_size:
                    logger.info(f"💡 Post-processing: Breaking down an oversized semantic chunk in create_semantic_chunks.")
                    oversized_chunks.extend(self.recursive_text_splitter.split_text(chunk))
                else:
                    oversized_chunks.append(chunk)

            return [c for c in oversized_chunks if c.strip()]

        except Exception as e:
            logger.error(f"❌ Error during semantic chunking in create_semantic_chunks: {e}. Falling back to recursive text splitter.")
            return self.recursive_text_splitter.split_text(cleaned_text)
    
    async def process_documents(self, file_paths: List[str]) -> List[Document]:
        all_documents = []
        processed_files = 0
        skipped_files = []

        for file_path in file_paths:
            file_path = Path(file_path)

            if not file_path.exists():
                logger.warning(f"⚠️ File not found: {file_path}")
                skipped_files.append(str(file_path))
                continue

            file_ext = file_path.suffix.lower()

            text = ""
            if file_ext == '.pdf':
                text = self.extract_text_from_pdf(str(file_path))
            elif file_ext == '.docx':
                text = self.extract_text_from_docx(str(file_path))
            elif file_ext == '.txt':
                text = self.extract_text_from_txt(str(file_path))
            else:
                logger.warning(f"⚠️ Unsupported file format: {file_path}")
                skipped_files.append(str(file_path))
                continue

            if text.strip():
                # Call the async create_chunks
                documents = await self.create_chunks(text, str(file_path))
                all_documents.extend(documents)
                processed_files += 1
            else:
                logger.warning(f"⚠️ No text extracted from: {file_path}")
                skipped_files.append(str(file_path))

        logger.info(f"✅ Processed {processed_files} files successfully")
        logger.info(f"📊 Generated {len(all_documents)} total chunks")

        if skipped_files:
            logger.warning(f"⚠️ Skipped {len(skipped_files)} files")

        unique_documents = self._deduplicate_chunks(all_documents)

        if len(unique_documents) != len(all_documents):
            logger.info(f"🔄 Removed {len(all_documents) - len(unique_documents)} duplicate chunks")

        return unique_documents

    def _deduplicate_chunks(self, documents: List[Document]) -> List[Document]:
        """Remove duplicate chunks using full content hash."""
        seen_hashes = set()
        unique_docs = []

        for doc in documents:
            content_hash = hashlib.md5(doc.page_content.encode('utf-8')).hexdigest()

            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_docs.append(doc)

        return unique_docs
