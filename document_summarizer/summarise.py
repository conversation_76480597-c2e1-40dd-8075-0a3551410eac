import logging
import asyncio
from typing import Dict, List, Optional
from document_summarizer.agents import DemandSynthesisAgent, EnhancedRetrieval, HumanStoryAgent, IssueExtractionAgent, ManifestoDraftingAgent, ReviewAssemblyAgent, UnfulfilledPromisesAgent, ContentDepthEvaluator
from document_summarizer.bedrock_utilities import BedrockClaudeLLM, BedrockTitanEmbeddings
from document_summarizer.hyperlink_generator import hyperlink_generator
import weaviate
import random


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PoliticalAnalysisAgents:
    """Enhanced main class for managing all political analysis agents"""
    
    def __init__(self, weaviate_client: Optional[weaviate.Client] = None):
        self.llm = BedrockClaudeLLM()
        self.embedding_model = BedrockTitanEmbeddings()
        
        # Ensure weaviate_client is connected if it's a WeaviateManager
        if weaviate_client and hasattr(weaviate_client, 'connect'):
            if not weaviate_client.client:  # Check if not already connected
                weaviate_client.connect()
        
        self.retrieval = EnhancedRetrieval(weaviate_client, self.embedding_model)
        self.depth_evaluator = ContentDepthEvaluator(self.llm)

        # Initialize all agents
        self.agents = {
            'issue_extraction': IssueExtractionAgent(self.llm),
            'human_stories': HumanStoryAgent(self.llm),
            'unfulfilled_promises': UnfulfilledPromisesAgent(self.llm),
            'public_demand': DemandSynthesisAgent(self.llm),
            'manifesto_drafting': ManifestoDraftingAgent(self.llm),
            'review_assembly': ReviewAssemblyAgent(self.llm)
        }
        
        logger.info("✅ All political analysis agents initialized")
    
    async def retrieve_agent_specific_chunks(self, agent_name: str, user_id: str, collection_id: str, important_keywords: str = None) -> List[Dict]:
        """Retrieve chunks specific to an agent's query"""
        if not self.retrieval:
            logger.warning("No retrieval system configured")
            return []
        
        agent = self.agents[agent_name]
        base_query = agent.get_query() + " " + important_keywords if important_keywords else agent.get_query()
        
        # Get expanded queries with synonyms from the agent
        synonym_queries = agent.expand_query(base_query, agent_name)
        
        # Split each synonym query into multiple focused queries
        expanded_queries = []
        for query in synonym_queries:
            split_queries = self._split_query_into_multiple(query)
            expanded_queries.extend(split_queries)
        
        all_chunks = []
        
        for query in expanded_queries:
            chunks = await self.retrieval.retrieve_relevant_chunks(
                query=query, 
                user_id=user_id,
                collection_id=collection_id
            )
            all_chunks.extend(chunks)
            
        logger.info(f"Retrieved chunks: {all_chunks} for agent {agent_name}")
        
        return all_chunks 

    def _split_query_into_multiple(self, base_query: str) -> List[str]:
        """Split a large base query into 3-4 focused queries"""
        # Split by sentences and group them
        sentences = [s.strip() for s in base_query.split(',') if s.strip()]
        random.shuffle(sentences)
        
        if len(sentences) <= 3:
            logger.info("Query is not split")
            return [base_query]  # Return original if already short
        
        # Group sentences into 3-4 queries
        queries = []
        chunk_size = max(1, len(sentences) // 3)
        
        for i in range(0, len(sentences), chunk_size):
            chunk = sentences[i:i + chunk_size]
            if chunk:
                query = '. '.join(chunk)
                if not query.endswith('.'):
                    query += '.'
                queries.append(query)
        
        # Ensure we don't have more than 4 queries
        if len(queries) > 4:
            # Merge last queries if too many
            merged_last = '. '.join(queries[3:])
            queries = queries[:3] + [merged_last]
        logger.info(f"Split query into {len(queries)} queries. Original: {base_query}. Split into: {queries}")
        return queries
    
    async def process_agent_with_retrieval(self, agent_name: str, user_id: str, collection_id: str, important_keywords: str = None) -> str:
        """Process agent with enhanced retrieval and depth evaluation"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")

        # Retrieve agent-specific chunks
        relevant_chunks = await self.retrieve_agent_specific_chunks(agent_name, user_id, collection_id, important_keywords)

        if not relevant_chunks:
            logger.warning(f"No relevant chunks found for {agent_name}")
            return f"No relevant content found for {agent_name}"

        # Process with the agent
        agent = self.agents[agent_name]
        initial_result = await agent.process(relevant_chunks, user_id)

        # Evaluate content depth
        depth_evaluation = await self.depth_evaluator.evaluate_content_depth(agent_name, initial_result)

        if depth_evaluation.get('needs_expansion', False):
            logger.info(f"Content depth insufficient for {agent_name}. Performing dynamic retrieval...")

            # Perform dynamic retrieval for more content
            expanded_chunks = await self.retrieval.dynamic_retrieval_for_depth(
                agent_name, agent.get_query(), user_id, collection_id
            )

            if expanded_chunks:
                # Combine original and expanded chunks, removing duplicates
                all_chunks = relevant_chunks + expanded_chunks
                unique_chunks = self.retrieval._deduplicate_chunks(all_chunks)

                # Re-process with expanded content
                enhanced_result = await agent.process(unique_chunks[:20], user_id)  # Limit to top 20
                logger.info(f"Enhanced result generated for {agent_name} with {len(unique_chunks)} chunks")
                return enhanced_result

        return initial_result
    
    async def generate_enhanced_report(self, user_id: str, collection_id: str, output_instructions_prompt: str, important_keywords: str = None) -> str:
        """Generate report with enhanced retrieval for each agent"""
        logger.info("🚀 Starting enhanced political analysis...")
        
        components = {}
        processing_agents = [name for name in self.agents.keys() if name != 'review_assembly']
        
        # Process agents in order, with manifesto_drafting last to use other outputs
        ordered_agents = ['issue_extraction', 'human_stories', 'unfulfilled_promises', 'public_demand', 'manifesto_drafting']
        
        for agent_name in ordered_agents:
            if agent_name not in processing_agents:
                continue
                
            logger.info(f"Processing {agent_name} with enhanced retrieval...")
            try:
                if agent_name == 'manifesto_drafting':
                    # Special handling for manifesto_drafting agent
                    result = await self.process_manifesto_drafting_agent(user_id, collection_id, important_keywords, components)
                else:
                    result = await self.process_agent_with_retrieval(agent_name, user_id, collection_id, important_keywords)
                
                components[agent_name] = result
                logger.info(f"✅ Completed {agent_name}")
            except Exception as e:
                logger.error(f"❌ Failed to process {agent_name}: {e}")
                components[agent_name] = f"Error processing {agent_name}: {str(e)}"
        
        # Final assembly
        logger.info("Assembling final report...")
        try:
            # Retrieve localised information for review_assembly agent
            logger.info("Retrieving localised information for review assembly...")
            # localised_chunks = await self.retrieve_agent_specific_chunks('review_assembly', user_id, collection_id, important_keywords)
            
            # if localised_chunks:
            #     localised_info = "\n\n".join([
            #         f"{chunk.get('content', str(chunk))}" 
            #         for chunk in localised_chunks[:10]
            #     ])
            # else:
            #     localised_info = "No localised information available"
            
            final_report = await self.agents['review_assembly'].assemble(components, user_id, output_instructions_prompt)

            # Generate hyperlinks for the final report
            logger.info("🔗 Adding hyperlinks to final report...")
            final_report_with_hyperlinks = hyperlink_generator.generate_hyperlinks_for_content(
                final_report,
                content_type="political_analysis"
            )

            hyperlink_count = len(hyperlink_generator.get_all_hyperlinks())
            logger.info(f"✅ Political analysis complete! Added {hyperlink_count} hyperlinks.")

            return final_report_with_hyperlinks
        except Exception as e:
            logger.error(f"❌ Failed to assemble final report: {e}")
            return f"Error assembling final report: {str(e)}"

    async def process_manifesto_drafting_agent(self, user_id: str, collection_id: str, important_keywords: str, components: Dict[str, str]) -> str:
        """Special processing for manifesto_drafting agent with issue_extraction and public_demand outputs"""
        agent_name = 'manifesto_drafting'
        
        # Retrieve agent-specific chunks
        relevant_chunks = await self.retrieve_agent_specific_chunks(agent_name, user_id, collection_id, important_keywords)
        
        if not relevant_chunks:
            logger.warning(f"No relevant chunks found for {agent_name}")
            context = "No relevant content found"
        else:
            # Combine chunk content
            context = "\n\n".join([
                f"{chunk.get('content', str(chunk))}" 
                for i, chunk in enumerate(relevant_chunks[:10])
            ])
        
        # Get outputs from issue_extraction and public_demand agents
        public_issues_and_demands = f"""
Issue Extraction Output:
{components.get('issue_extraction', 'No issue extraction data available')}

Public Demand Output:
{components.get('public_demand', 'No public demand data available')}
"""
        
        # Get the agent and format the prompt with both context and previous outputs
        agent = self.agents[agent_name]
        prompt = agent.get_prompt().format(
            # public_issues_and_demands=public_issues_and_demands,
            context=context
        )
        
        messages = [("user", prompt)]
        logger.info(f"Processing manifesto_drafting agent with combined inputs")
        
        try:
            response = agent.llm.ainvoke_async(messages)
            return response["content"]
        except Exception as e:
            logger.error(f"Error processing {agent_name} for user {user_id}: {e}")
            return f"Error processing {agent_name}: {str(e)}"
