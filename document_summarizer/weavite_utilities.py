import weaviate
import logging
from typing import List, Dict, Optional, Any
from langchain.docstore.document import Document
from document_summarizer.bedrock_utilities import BedrockTitanEmbeddings

logger = logging.getLogger(__name__)

class WeaviateManager:
    def __init__(self, cluster_url: str = None, api_key: str = None):
        self.client = None
        self.collection_name = "PoliticalDocuments"
        self.vector_store = None
        
        # Default configuration - can be overridden
        self.cluster_url = cluster_url or "http://13.232.193.27:3010/"
        self.api_key = api_key
    
    def connect(self):
        """Connect to Weaviate using the old client API pattern"""
        try:
            timeout_config = (500, 500)
            if self.api_key:
                self.client = weaviate.Client(
                    url=self.cluster_url,
                    auth_client_secret=weaviate.AuthApiKey(self.api_key),
                    timeout_config=timeout_config
                )
            else:
                self.client = weaviate.Client(url=self.cluster_url,  timeout_config=timeout_config)
            
            # Test connection
            schema = self.client.schema.get()
            logger.info(f"✅ Connected to Weaviate at {self.cluster_url}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Weaviate: {e}")
            return False
    
    def create_collection(self, user_id: str, collection_name: str = None):
        """Create a Weaviate collection/class if it doesn't exist"""
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            class_name = collection_name or self.collection_name
            
            # Check if class already exists
            existing_schema = self.client.schema.get()
            existing_classes = [cls['class'] for cls in existing_schema.get('classes', [])]
            
            if class_name in existing_classes:
                logger.info(f"✅ Collection '{class_name}' already exists")
                return True
            
            # Define the class schema
            class_schema = {
                "class": class_name,
                "description": "Collection for storing political documents with user and collection isolation",
                "vectorizer": "none",  # We'll provide our own vectors
                "properties": [
                    {
                        "name": "content",
                        "dataType": ["text"],
                        "description": "The main content of the document"
                    },
                    {
                        "name": "user_id",
                        "dataType": ["string"],
                        "description": "User identifier for data isolation"
                    },
                    {
                        "name": "collection_id",
                        "dataType": ["string"],
                        "description": "Collection identifier for grouping documents"
                    },
                    {
                        "name": "source",
                        "dataType": ["string"],
                        "description": "Source of the document"
                    },
                    {
                        "name": "metadata",
                        "dataType": ["text"],
                        "description": "Additional metadata as JSON string"
                    }
                ]
            }
            
            # Create the class
            self.client.schema.create_class(class_schema)
            logger.info(f"✅ Created collection '{class_name}' for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create collection: {e}")
            return False
        
    async def create_vector_store(self, documents: List[Document], embeddings: List[List[float]], 
                                 user_id: str = "dummy_user_123", collection_id: str = None) -> Optional[str]:
        """Create vector store by adding documents with embeddings to Weaviate"""
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            if len(documents) != len(embeddings):
                raise ValueError("Number of documents must match number of embeddings")
            
            # Always generate a unique collection_id for this batch if not provided
            if not collection_id:
                import uuid
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                collection_id = f"{user_id}_{timestamp}_{str(uuid.uuid4())[:8]}"
            
            # Clean the collection_id - remove any extra quotes and whitespace
            collection_id = str(collection_id).strip().strip("'\"")
            
            logger.info(f"Creating vector store with clean collection_id: {collection_id}")
            
            # Ensure collection exists
            self.create_collection(user_id)
            
            # Batch insert documents
            with self.client.batch as batch:
                batch.batch_size = 5
                
                for i, (doc, embedding) in enumerate(zip(documents, embeddings)):
                    # Prepare the data object with properly cleaned strings
                    data_object = {
                        "content": str(doc.page_content),
                        "user_id": str(user_id).strip().strip("'\""),
                        "collection_id": str(collection_id).strip().strip("'\""),  # Clean quotes
                        "source": str(doc.metadata.get("source", "")).strip().strip("'\""),
                        "metadata": str(doc.metadata),
                        "document_index": int(i)
                    }
                    
                    # Add to batch with vector
                    batch.add_data_object(
                        data_object=data_object,
                        class_name=self.collection_name,
                        vector=embedding
                    )
                    
                    if (i + 1) % 50 == 0:
                        logger.info(f"Processed {i + 1}/{len(documents)} documents")
            
            logger.info(f"✅ Successfully added {len(documents)} documents for user {user_id}, collection {collection_id}")
            return collection_id
        
        except Exception as e:
            logger.error(f"❌ Failed to create vector store: {e}")
            return None

    def search_similar_for_user(self, query_vector: List[float], 
                               user_id: str, collection_id: str, 
                               limit: int = 10) -> List[Dict]:
        """Fixed search that handles both clean and quoted collection_ids"""
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            if not user_id or not collection_id:
                raise ValueError("Both user_id and collection_id are required for search")
            
            # Clean the input collection_id
            clean_collection_id = str(collection_id).strip().strip("'\"")
            
            # Try multiple variations to handle existing data with quotes
            collection_id_variations = [
                clean_collection_id,           # Clean version
                f"'{clean_collection_id}'",    # With single quotes (existing data)
                f'"{clean_collection_id}"',    # With double quotes (just in case)
            ]
            
            logger.info(f"Searching for collection_id variations: {collection_id_variations}")
            
            for variation in collection_id_variations:
                where_filter = {
                    "operator": "And",
                    "operands": [
                        {
                            "path": ["user_id"],
                            "operator": "Equal",
                            "valueString": user_id
                        },
                        {
                            "path": ["collection_id"],
                            "operator": "Equal",
                            "valueString": variation
                        }
                    ]
                }
                
                result = (
                    self.client.query
                    .get(self.collection_name, ["content", "user_id", "collection_id", "source", "metadata", "document_index"])
                    .with_near_vector({"vector": query_vector})
                    .with_where(where_filter)
                    .with_limit(limit)
                    .with_additional(["distance"])
                    .do()
                )
                
                # Extract results
                documents = []
                if "data" in result and "Get" in result["data"] and self.collection_name in result["data"]["Get"]:
                    for item in result["data"]["Get"][self.collection_name]:
                        documents.append({
                            "content": item.get("content", ""),
                            "user_id": item.get("user_id", ""),
                            "collection_id": item.get("collection_id", ""),
                            "source": item.get("source", ""),
                            "metadata": item.get("metadata", ""),
                            "document_index": item.get("document_index", 0),
                            "distance": item.get("_additional", {}).get("distance", 1.0)
                        })
                
                if documents:
                    logger.info(f"✅ Found {len(documents)} documents using collection_id: '{variation}'")
                    logger.info(f"First document: {documents[0]}")
                    return documents
                else:
                    logger.info(f"No results for collection_id variation: '{variation}'")
            
            logger.warning(f"No documents found for any collection_id variation for user {user_id}")
            return []
        
        except Exception as e:
            logger.error(f"❌ Failed to search similar documents: {e}")
            return []
    
    def hybrid_search_for_user(self, query_vector: List[float], query_text: str,
                              user_id: str, collection_id: str, 
                              limit: int = 10, alpha: float = 0.7) -> List[Dict]:
        """
        Perform hybrid search combining vector similarity and keyword search
        Both user_id and collection_id are required to ensure proper document isolation
        alpha: 0.0 = pure keyword search, 1.0 = pure vector search, 0.7 = balanced hybrid
        """
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            if not user_id or not collection_id:
                raise ValueError("Both user_id and collection_id are required for search")
            
            # Clean the input collection_id
            clean_collection_id = str(collection_id).strip().strip("'\"")
            
            # Try multiple variations to handle existing data with quotes
            collection_id_variations = [
                clean_collection_id,           # Clean version
                f"'{clean_collection_id}'",    # With single quotes (existing data)
                f'"{clean_collection_id}"',    # With double quotes (just in case)
            ]
            
            for variation in collection_id_variations:
                where_filter = {
                    "operator": "And",
                    "operands": [
                        {
                            "path": ["user_id"],
                            "operator": "Equal",
                            "valueString": user_id
                        },
                        {
                            "path": ["collection_id"],
                            "operator": "Equal",
                            "valueString": variation
                        }
                    ]
                }
                
                result = (
                    self.client.query
                    .get(self.collection_name, ["content", "user_id", "collection_id", "source", "metadata", "document_index"])
                    .with_hybrid(
                        query=query_text,
                        vector=query_vector,
                        alpha=alpha
                    )
                    .with_where(where_filter)
                    .with_limit(limit)
                    .with_additional(["score"])
                    .do()
                )
                
                # Extract results
                documents = []
                if "data" in result and "Get" in result["data"] and self.collection_name in result["data"]["Get"]:
                    for item in result["data"]["Get"][self.collection_name]:
                        documents.append({
                            "content": item.get("content", ""),
                            "user_id": item.get("user_id", ""),
                            "collection_id": item.get("collection_id", ""),
                            "source": item.get("source", ""),
                            "metadata": item.get("metadata", ""),
                            "document_index": item.get("document_index", 0),
                            "score": item.get("_additional", {}).get("score", 0.0)
                        })
                
                if documents:
                    logger.info(f"✅ Hybrid search found {len(documents)} documents using collection_id: '{variation}'")
                    logger.info(f"First document: {documents[0]}")

                    return documents
            
            logger.warning(f"No documents found for any collection_id variation in hybrid search")
            return []
        
        except Exception as e:
            logger.error(f"❌ Failed to perform hybrid search: {e}")
            return []

    def list_user_collections(self, user_id: str) -> List[Dict]:
        """List all collections for a specific user"""
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            # Get all unique collection_ids for the user
            result = (
                self.client.query
                .aggregate(self.collection_name)
                .with_where({
                    "path": ["user_id"],
                    "operator": "Equal",
                    "valueString": user_id
                })
                .with_group_by(["collection_id"])
                .do()
            )
            
            collections = []
            if "data" in result and "Aggregate" in result["data"]:
                for group in result["data"]["Aggregate"][self.collection_name]:
                    collections.append({
                        "collection_id": group.get("groupedBy", {}).get("value", ""),
                        "count": group.get("meta", {}).get("count", 0)
                    })
            
            logger.info(f"✅ Found {len(collections)} collections for user {user_id}")
            return collections
            
        except Exception as e:
            logger.error(f"❌ Failed to list user collections: {e}")
            return []

    def delete_user_collection(self, user_id: str, collection_id: str) -> Optional[Dict]:
        """Delete documents for a specific user and collection (both required for safety)"""
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            if not user_id or not collection_id:
                raise ValueError("Both user_id and collection_id are required for deletion")
            
            # Build the where filter - BOTH user_id AND collection_id are required
            where_filter = {
                "operator": "And",
                "operands": [
                    {
                        "path": ["user_id"],
                        "operator": "Equal",
                        "valueString": user_id
                    },
                    {
                        "path": ["collection_id"],
                        "operator": "Equal",
                        "valueString": collection_id
                    }
                ]
            }
            
            # First, count how many documents will be deleted
            count_result = (
                self.client.query
                .aggregate(self.collection_name)
                .with_where(where_filter)
                .with_meta_count()
                .do()
            )
            
            document_count = 0
            if "data" in count_result and "Aggregate" in count_result["data"]:
                document_count = count_result["data"]["Aggregate"][self.collection_name][0]["meta"]["count"]
            
            if document_count == 0:
                logger.warning(f"⚠️ No documents found for user {user_id}, collection {collection_id}")
                return {"deleted_count": 0, "status": "no_documents_found"}
            
            # Delete objects
            result = self.client.batch.delete_objects(
                class_name=self.collection_name,
                where=where_filter
            )
            
            logger.info(f"✅ Deleted {document_count} documents for user {user_id}, collection {collection_id}")
            return {
                "deleted_count": document_count,
                "status": "success",
                "result": result
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to delete user collection: {e}")
            return None

    def delete_all_user_data(self, user_id: str) -> Optional[Dict]:
        """
        Delete ALL data for a user across all collections (use with extreme caution!)
        Separate method for bulk deletion to make the dangerous operation explicit
        """
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            if not user_id:
                raise ValueError("user_id is required for deletion")
            
            # Build the where filter for user only
            where_filter = {
                "path": ["user_id"],
                "operator": "Equal",
                "valueString": user_id
            }
            
            # First, count how many documents will be deleted
            count_result = (
                self.client.query
                .aggregate(self.collection_name)
                .with_where(where_filter)
                .with_meta_count()
                .do()
            )
            
            document_count = 0
            if "data" in count_result and "Aggregate" in count_result["data"]:
                document_count = count_result["data"]["Aggregate"][self.collection_name][0]["meta"]["count"]
            
            if document_count == 0:
                logger.warning(f"⚠️ No documents found for user {user_id}")
                return {"deleted_count": 0, "status": "no_documents_found"}
            
            # Get list of collections that will be affected
            collections = self.list_user_collections(user_id)
            collection_ids = [col["collection_id"] for col in collections]
            
            # Delete objects
            result = self.client.batch.delete_objects(
                class_name=self.collection_name,
                where=where_filter
            )
            
            logger.info(f"✅ Deleted ALL {document_count} documents for user {user_id} across {len(collection_ids)} collections")
            return {
                "deleted_count": document_count,
                "collections_affected": collection_ids,
                "status": "success",
                "result": result
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to delete all user data: {e}")
            return None
        
    def debug_collection_data(self, user_id: str, collection_id: str = None) -> Dict:
        """Debug method to check what data exists in Weaviate"""
        try:
            if not self.client:
                raise Exception("Not connected to Weaviate. Call connect() first.")
            
            # First, get all documents for the user to see what collection_ids exist
            user_filter = {
                "path": ["user_id"],
                "operator": "Equal",
                "valueString": user_id
            }
            
            result = (
                self.client.query
                .get(self.collection_name, ["user_id", "collection_id", "content"])
                .with_where(user_filter)
                .with_limit(100)  # Increase limit to see more data
                .do()
            )
            
            debug_info = {
                "total_docs_for_user": 0,
                "unique_collection_ids": set(),
                "collection_id_types": set(),
                "sample_docs": []
            }
            
            if "data" in result and "Get" in result["data"] and self.collection_name in result["data"]["Get"]:
                docs = result["data"]["Get"][self.collection_name]
                debug_info["total_docs_for_user"] = len(docs)
                
                for doc in docs:
                    cid = doc.get("collection_id")
                    debug_info["unique_collection_ids"].add(str(cid))
                    debug_info["collection_id_types"].add(type(cid).__name__)
                    
                    # Store first 5 docs as samples
                    if len(debug_info["sample_docs"]) < 5:
                        debug_info["sample_docs"].append({
                            "user_id": doc.get("user_id"),
                            "collection_id": repr(doc.get("collection_id")),  # Use repr to see exact value
                            "collection_id_type": type(doc.get("collection_id")).__name__,
                            "content_preview": doc.get("content", "")[:100] + "..."
                        })
            
            # Convert set to list for JSON serialization
            debug_info["unique_collection_ids"] = list(debug_info["unique_collection_ids"])
            debug_info["collection_id_types"] = list(debug_info["collection_id_types"])
            
            logger.info(f"Debug info: {debug_info}")
            
            # If specific collection_id provided, test the exact filter
            if collection_id:
                logger.info(f"Testing search for specific collection_id: {repr(collection_id)}")
                
                # Test the exact filter that's failing
                where_filter = {
                    "operator": "And",
                    "operands": [
                        {
                            "path": ["user_id"],
                            "operator": "Equal",
                            "valueString": user_id
                        },
                        {
                            "path": ["collection_id"],
                            "operator": "Equal",
                            "valueString": collection_id
                        }
                    ]
                }
                
                filter_result = (
                    self.client.query
                    .get(self.collection_name, ["user_id", "collection_id", "content"])
                    .with_where(where_filter)
                    .with_limit(10)
                    .do()
                )
                
                filter_count = 0
                if "data" in filter_result and "Get" in filter_result["data"] and self.collection_name in filter_result["data"]["Get"]:
                    filter_count = len(filter_result["data"]["Get"][self.collection_name])
                
                debug_info["filter_test"] = {
                    "searched_collection_id": repr(collection_id),
                    "found_docs": filter_count,
                    "collection_id_in_unique_list": str(collection_id) in debug_info["unique_collection_ids"]
                }
            
            return debug_info
            
        except Exception as e:
            logger.error(f"❌ Debug failed: {e}")
            return {"error": str(e)}