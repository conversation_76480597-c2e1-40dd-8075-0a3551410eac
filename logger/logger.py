import logging
import json
import time
from functools import wraps
from typing import Callable
from fastapi import Request, Response
from datetime import datetime
import inspect  # For checking if a function is async

# Configure logger with a cleaner format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("api")

class RequestLogger:
    @staticmethod
    def log_route(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            """Wrapper for async functions."""
            # Get start time
            start_time = time.time()

            # Initialize log data
            log_data = {
                'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S'),
                'endpoint': func.__name__,
            }

            # Extract request details
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            if not request:
                for arg in kwargs.values():
                    if isinstance(arg, Request):
                        request = arg
                        break

            # Add request details if available
            if request:
                log_data.update({
                    'method': request.method,
                    'path': str(request.url.path),
                    'client_ip': request.client.host if request.client else 'unknown'
                })

            try:
                # Execute the route handler
                response = await func(*args, **kwargs)

                # Calculate execution time
                execution_time = time.time() - start_time

                # Add success information to log
                log_data.update({
                    'status': 'success',
                    'duration': f'{execution_time:.3f}s',
                    'status_code': getattr(response, 'status_code', 200)
                })

                # Log successful request
                logger.info(json.dumps(log_data, indent=None, separators=(',', ':')))

                return response

            except Exception as e:
                # Calculate execution time
                execution_time = time.time() - start_time

                # Extract error details
                error_detail = str(e)
                if hasattr(e, 'detail'):
                    error_detail = str(e.detail)

                # Add error information to log
                log_data.update({
                    'status': 'error',
                    'error': error_detail,
                    'duration': f'{execution_time:.3f}s',
                    'status_code': getattr(e, 'status_code', 500)
                })

                # Log error in a cleaner format
                logger.error(json.dumps(log_data, indent=None, separators=(',', ':')))

                # Re-raise the exception
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            """Wrapper for sync functions."""
            # Get start time
            start_time = time.time()

            # Initialize log data
            log_data = {
                'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S'),
                'endpoint': func.__name__,
            }

            # Extract request details
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            if not request:
                for arg in kwargs.values():
                    if isinstance(arg, Request):
                        request = arg
                        break

            # Add request details if available
            if request:
                log_data.update({
                    'method': request.method,
                    'path': str(request.url.path),
                    'client_ip': request.client.host if request.client else 'unknown'
                })

            try:
                # Execute the route handler
                response = func(*args, **kwargs)

                # Calculate execution time
                execution_time = time.time() - start_time

                # Add success information to log
                log_data.update({
                    'status': 'success',
                    'duration': f'{execution_time:.3f}s',
                    'status_code': getattr(response, 'status_code', 200)
                })

                # Log successful request
                logger.info(json.dumps(log_data, indent=None, separators=(',', ':')))

                return response

            except Exception as e:
                # Calculate execution time
                execution_time = time.time() - start_time

                # Extract error details
                error_detail = str(e)
                if hasattr(e, 'detail'):
                    error_detail = str(e.detail)

                # Add error information to log
                log_data.update({
                    'status': 'error',
                    'error': error_detail,
                    'duration': f'{execution_time:.3f}s',
                    'status_code': getattr(e, 'status_code', 500)
                })

                # Log error in a cleaner format
                logger.error(json.dumps(log_data, indent=None, separators=(',', ':')))

                # Re-raise the exception
                raise

        # Determine if the function is async or sync
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
