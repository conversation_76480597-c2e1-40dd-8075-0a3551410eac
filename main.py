from fastapi import FastAPI
from routes.api.router import router as api_router
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from routes.api.v1.pdf_parser import router as upload_router
from routes.api.v1.chat import router as chat_router
from routes.api.v1.url import router as url_router
from routes.api.v1.embed_bot import router as embedbot_router
from routes.api.v1.public.public_embedbot import router as public_embedbot_router
from routes.api.v1.document_summariser import router as document_summarizer_router
from routes.api.v1.user_model import router as user_model_router
from app.core.config import settings

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app() -> FastAPI:
    """
    Create and configure the FastAPI app.
    """
    app = FastAPI(
        title="AI PLANET KNOWLEDGEHUB",
        description="THE AI PLANET KNOWLEDGEHUB API",
        version="1.0.0",
        debug=True,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.GOOGLE_SECRET_KEY,
    )
    @app.get("/")
    async def health_check():
        return {"status": "healthy", "service": "AI PLANET KNOWLEDGEHUB API"}

    # Include API routers
    app.include_router(api_router)
    app.include_router(upload_router)
    app.include_router(chat_router)
    app.include_router(url_router)
    app.include_router(embedbot_router)
    app.include_router(public_embedbot_router)
    app.include_router(document_summarizer_router)
    app.include_router(user_model_router)

    # Add startup and shutdown events if needed
    @app.on_event("startup")
    async def startup_event():
        logging.info("Starting up the application...")

    @app.on_event("shutdown")
    async def shutdown_event():
        logging.info("Shutting down the application...")

    return app


# Create the app instance at the top level
app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",  # Use the `app` instance
        host=settings.HOST,
        port=settings.PORT,
        log_level="debug",
        reload=settings.RELOAD,
    )
