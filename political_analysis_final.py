#!/usr/bin/env python3
"""
Political Analysis Pipeline - Modified for AWS Bedrock Embeddings
Uses Amazon Titan Text Embeddings V2 model instead of Azure OpenAI
"""

import os
import logging
import subprocess
import sys
import argparse
import glob
import hashlib
from typing import List, Dict, Any, Optional
import re
from pathlib import Path
import json
import requests
import time
import boto3
from botocore.exceptions import ClientError
import numpy as np
import aioboto3
import asyncio

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('political_analysis.log')
    ]
)
logger = logging.getLogger(__name__)

# =============================================================================
# 1. ENVIRONMENT SETUP & INSTALLATIONS
# =============================================================================

def create_requirements_file():
    """Create requirements.txt file for easy installation."""
    requirements = [
        "langchain==0.1.0",
        "weaviate-client==4.12.0",
        "sentence-transformers==2.2.2",
        "PyPDF2==3.0.1",
        "python-docx==0.8.11",
        "litellm==1.28.0",
        "openai==1.12.0",
        "tiktoken==0.5.2",
        "rank-bm25==0.2.2",
        "numpy==1.24.3",
        "pandas==2.0.3",
        "requests==2.31.0",
        "boto3>=1.26.0",
        "botocore>=1.29.0",
        "aioboto3>=12.0.0"
    ]

    try:
        with open("requirements.txt", "w") as f:
            f.write("\n".join(requirements))
        logger.info("✅ Created requirements.txt file")
    except Exception as e:
        logger.error(f"❌ Failed to create requirements.txt: {e}")

def install_packages():
    """Install required packages with proper error handling."""
    packages = [
        "langchain==0.1.0", "weaviate-client==4.12.0", "sentence-transformers==2.2.2",
        "PyPDF2==3.0.1", "python-docx==0.8.11", "litellm==1.28.0", "openai==1.12.0",
        "tiktoken==0.5.2", "rank-bm25==0.2.2", "numpy==1.24.3", "pandas==2.0.3", 
        "requests==2.31.0", "boto3>=1.26.0", "botocore>=1.29.0", "aioboto3>=12.0.0"
    ]

    print("📦 Installing dependencies - this may take a while...")
    failed_packages = []

    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package],
                                stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
            logger.info(f"✅ Installed {package}")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install {package}: {e}")
            failed_packages.append(package)

    if failed_packages:
        logger.error(f"❌ Failed to install: {', '.join(failed_packages)}")
        print(f"❌ Installation failed for: {', '.join(failed_packages)}")
        print("💡 Try running: pip install -r requirements.txt")
        return False

    print("✅ All dependencies installed successfully!")
    return True

# =============================================================================
# 2. API KEYS & CONFIGURATIONS
# =============================================================================

def setup_environment():
    # AWS Bedrock Configuration for both Embeddings and LLM
    # These can be set via environment variables or AWS credentials file
    # os.environ["AWS_ACCESS_KEY_ID"] = "your_aws_access_key"
    # os.environ["AWS_SECRET_ACCESS_KEY"] = "your_aws_secret_key"
    # os.environ["AWS_DEFAULT_REGION"] = "us-east-1"  # or your preferred region
    
    # Weaviate Configuration
    global WEAVIATE_HTTP_HOST, WEAVIATE_GRPC_HOST, WEAVIATE_API_KEY
    WEAVIATE_HTTP_HOST = "weaviate.aimarketplace.co"
    WEAVIATE_GRPC_HOST = "************"
    WEAVIATE_API_KEY = "5d92168b96ea4b9bb553b02cd14157b7"

# =============================================================================
# 3. IMPORTS
# =============================================================================

try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.schema import Document
    from langchain_community.vectorstores import Weaviate
    from langchain_community.retrievers import BM25Retriever
    from langchain.embeddings.base import Embeddings
    import weaviate
    from weaviate.classes.init import Auth
    import PyPDF2
    from docx import Document as DocxDocument
    import tiktoken
    from rank_bm25 import BM25Okapi

    logger.info("✅ All dependencies imported successfully")

except ImportError as e:
    logger.error(f"❌ Import error: {e}")
    print("❌ Missing dependencies. Please install them first:")
    print("   pip install -r requirements.txt")
    sys.exit(1)

# =============================================================================
# 4. SYSTEM PROMPTS (unchanged)
# =============================================================================

ANALYSIS_PROMPTS = {
    "major_issues": """Extract and summarize the main issues faced by the public in this constituency/area. Present each issue as a bullet point with subheadings and explanations. Each point should describe key problems faced by people in different areas/villages within the constituency. Use specific stories and incidents as supporting evidence where available. Focus on area-specific problems mentioned in the documents.

Retrieved Context:
{context}

Analysis:""",

    "unfulfilled_promises": """Identify all political parties mentioned in the documents and list their unfulfilled election promises in this constituency. For each party found in the documents, present their unfulfilled promises as numbered list with subheadings for each promise, clearly stating what was promised and its current unfulfilled status. Include evidence where available. Do not assume party roles - analyze all parties mentioned impartially.

Retrieved Context:
{context}

Analysis:""",

    "public_demands": """Extract all explicit and recurring demands raised by the public in different areas of this constituency that need to be addressed. Present each demand as a subheading with bullet points and explanations. Base this on surveys, feedback, public meetings, or documented evidence from the context. Focus on area-specific demands.

Retrieved Context:
{context}

Analysis:""",

    "manifesto_suggestions": """Based on the area-specific issues and unmet public demands identified, recommend actionable and realistic campaign promises for the upcoming manifesto. Organize into Development-Focused initiatives. Use this exact format: one subheading for each promise, followed by one bullet point explaining the promise. Each promise should be clear, concise, and address the specific needs of different areas within the constituency.

Format:
### Promise Title
- Detailed explanation of the promise and its benefits

Retrieved Context:
{context}

Analysis:""",

    "synthesis": """You must produce your answer using the following four numbered sections, and use the following section headings exactly as given. Do not add any extra sections, text, or summaries outside this format.

Use these exact headings in this order:

1. [AREA NAME] Assembly Segment – Major Issues
2. Unfulfilled Political Party Promises in [AREA NAME] Constituency
3. Key Public Demands to be addressed
4. Suggested Manifesto Promises

For area name: Extract the main constituency/area name from the context (e.g., "Bhuvanagiri", "Ariyalur", etc.). Use the actual area name mentioned in the documents.

Section Content Requirements:
- Section 1: List each major local issue as bullet points with subheadings and explanations, organized by different areas/villages within the constituency
- Section 2: For each political party mentioned in documents, list their unfulfilled promises with subheadings for each party and their specific promises/status
- Section 3: Each demand as subheading with bullet points and explanations, organized by area-specific needs
- Section 4: Development-focused manifesto promises with format: one subheading per promise, followed by one bullet point explanation addressing area-specific requirements

Important:
- Extract actual political party names from the documents (DMK, AIADMK, BJP, Congress, etc.)
- Do not use placeholder terms like "ruling party" or "opposition party" unless directly mentioned in source
- Analyze all parties mentioned impartially based on document evidence
- Focus on area-centric analysis rather than party-centric analysis

Sections:
{sections}

Final Report:"""
}

# =============================================================================
# 5. AWS BEDROCK EMBEDDINGS CLASS
# =============================================================================

class BedrockTitanEmbeddings(Embeddings):
    """Custom Langchain Embeddings class for AWS Bedrock Titan Text Embeddings V2 using aioboto3"""
    
    def __init__(self, model_id: str = "amazon.titan-embed-text-v2:0", region_name: str = "us-east-1"):
        self.model_id = model_id
        self.region_name = region_name
        # Store AWS credentials from environment or default
        self.aws_access_key_id = os.environ.get("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.environ.get("AWS_SECRET_ACCESS_KEY")
        self.aws_session_token = os.environ.get("AWS_SESSION_TOKEN")  # Optional for temporary credentials
        
        # Test connection
        try:
            self._test_connection()
            logger.info(f"✅ AWS Bedrock client initialized for region: {region_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AWS Bedrock client: {e}")
            raise e
    
    def _test_connection(self):
        """Test Bedrock connection synchronously"""
        try:
            client = boto3.client(
                service_name='bedrock-runtime',
                region_name=self.region_name,
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                aws_session_token=self.aws_session_token
            )
            # Just test if we can create the client - we won't make an actual call here
            logger.info("✅ Bedrock client test successful")
        except Exception as e:
            logger.error(f"❌ Bedrock connection test failed: {e}")
            raise e
    
    async def _generate_embedding_async(self, text: str) -> List[float]:
        """Generate embedding for a single text using Amazon Titan with aioboto3"""
        session = aioboto3.Session()
        
        try:
            async with session.client(
                service_name='bedrock-runtime',
                region_name=self.region_name,
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                aws_session_token=self.aws_session_token
            ) as client:
                
                # Prepare request body
                body = json.dumps({
                    "inputText": text,
                    "embeddingTypes": ["float"]  # Use float embeddings for vector similarity
                })
                
                # Call Bedrock API
                response = await client.invoke_model(
                    body=body,
                    modelId=self.model_id,
                    accept="application/json",
                    contentType="application/json"
                )
                
                # Parse response
                response_body = json.loads(await response['body'].read())
                
                # Extract float embeddings
                embedding = response_body['embeddingsByType']['float']
                
                return embedding
                
        except ClientError as e:
            logger.error(f"❌ AWS Bedrock API error: {e}")
            raise e
        except Exception as e:
            logger.error(f"❌ Error generating embedding: {e}")
            raise e
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents using async operations"""
        return asyncio.run(self._embed_documents_async(texts))
    
    async def _embed_documents_async(self, texts: List[str]) -> List[List[float]]:
        """Async implementation for embedding multiple documents"""
        embeddings = []
        
        # Process in batches to avoid overwhelming the API
        batch_size = 5
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            # Create tasks for concurrent processing
            tasks = [self._generate_embedding_async(text) for text in batch]
            
            try:
                # Process batch concurrently
                batch_embeddings = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Handle results and exceptions
                for j, result in enumerate(batch_embeddings):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Failed to embed document {i + j}: {result}")
                        # Return zero embedding as fallback
                        embeddings.append([0.0] * 1024)  # Titan V2 returns 1024-dimensional embeddings
                    else:
                        embeddings.append(result)
                
                # Progress logging
                if (i // batch_size + 1) % 5 == 0:
                    logger.info(f"✅ Generated embeddings for {min(i + batch_size, len(texts))}/{len(texts)} documents")
                
                # Add delay between batches to respect rate limits
                if i + batch_size < len(texts):
                    await asyncio.sleep(1)
                    
            except Exception as e:
                logger.error(f"❌ Batch processing failed for batch {i // batch_size + 1}: {e}")
                # Add zero embeddings for failed batch
                for _ in batch:
                    embeddings.append([0.0] * 1024)
        
        logger.info(f"✅ Generated embeddings for all {len(texts)} documents")
        return embeddings
    
    def embed_query(self, text: str) -> List[float]:
        """Embed a single query text"""
        return asyncio.run(self._generate_embedding_async(text))

# =============================================================================
# 6. WEAVIATE CONNECTION (unchanged)
# =============================================================================

class WeaviateManager:
    def __init__(self):
        self.client = None
        self.collection_name = "PoliticalDocuments"
        
    def connect(self):
        try:
            self.client = weaviate.connect_to_custom(
                http_host=WEAVIATE_HTTP_HOST,
                http_port=443,
                http_secure=True,
                grpc_host=WEAVIATE_GRPC_HOST,
                grpc_port=443,
                grpc_secure=True,
                auth_credentials=Auth.api_key(WEAVIATE_API_KEY)
            )
            return self.client.is_ready()
        except:
            return False
    
    def create_collection(self):
        try:
            if self.client.collections.exists(self.collection_name):
                return self.client.collections.get(self.collection_name)
            
            collection = self.client.collections.create(
                name=self.collection_name,
                description="Political documents for analysis"
            )
            return collection
        except:
            return None
    
    def close(self):
        if self.client:
            self.client.close()

# =============================================================================
# 7. DOCUMENT PROCESSING (unchanged)
# =============================================================================

class DocumentProcessor:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1500,
            chunk_overlap=250,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
            logger.info("✅ Tokenizer loaded successfully")
        except Exception as e:
            self.tokenizer = None
            logger.warning(f"⚠️ Failed to load tokenizer: {e}")
            logger.warning("💡 Falling back to word count estimation")
            print("⚠️ Warning: Tokenizer not available, using word count estimation")
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text += f"\n[PAGE {page_num + 1}]\n{page_text}\n"
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to extract page {page_num + 1} from {file_path}: {e}")
                        continue

                if not text.strip():
                    logger.warning(f"⚠️ No text extracted from PDF: {file_path}")
                else:
                    logger.info(f"✅ Extracted {len(text)} characters from PDF: {os.path.basename(file_path)}")

                return text
        except Exception as e:
            logger.error(f"❌ Failed to process PDF {file_path}: {e}")
            return ""
    
    def extract_text_from_docx(self, file_path: str) -> str:
        try:
            doc = DocxDocument(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    if len(paragraph.text) < 100 and (paragraph.text.isupper() or paragraph.text.istitle()):
                        text += f"\n[HEADING]\n{paragraph.text}\n"
                    else:
                        text += f"{paragraph.text}\n"

            if not text.strip():
                logger.warning(f"⚠️ No text extracted from DOCX: {file_path}")
            else:
                logger.info(f"✅ Extracted {len(text)} characters from DOCX: {os.path.basename(file_path)}")

            return text
        except Exception as e:
            logger.error(f"❌ Failed to process DOCX {file_path}: {e}")
            return ""

    def extract_text_from_txt(self, file_path: str) -> str:
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    text = file.read()
                    if text.strip():
                        logger.info(f"✅ Extracted {len(text)} characters from TXT: {os.path.basename(file_path)} (encoding: {encoding})")
                        return text
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"❌ Failed to process TXT {file_path} with {encoding}: {e}")
                continue

        logger.error(f"❌ Failed to read TXT file with any encoding: {file_path}")
        return ""
    
    def clean_text(self, text: str) -> str:
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = re.sub(r'[^\w\s.,!?;:()\-\[\]"\n]', '', text)
        return text.strip()
    
    def create_chunks(self, text: str, file_path: str) -> List[Document]:
        cleaned_text = self.clean_text(text)
        chunks = self.text_splitter.split_text(cleaned_text)
        
        documents = []
        file_name = Path(file_path).name
        doc_type = Path(file_path).suffix.lower()
        
        for i, chunk in enumerate(chunks):
            token_count = len(self.tokenizer.encode(chunk)) if self.tokenizer else len(chunk.split())
            
            section_type = "content"
            if "[PAGE" in chunk:
                section_type = "page_header"
            elif "[HEADING]" in chunk:
                section_type = "heading"
            
            page_number = None
            page_match = re.search(r'\[PAGE (\d+)\]', chunk)
            if page_match:
                page_number = int(page_match.group(1))
            
            doc = Document(
                page_content=chunk,
                metadata={
                    "source": file_name,
                    "chunk_id": i,
                    "doc_type": doc_type,
                    "section_type": section_type,
                    "page_number": page_number,
                    "total_chunks": len(chunks),
                    "character_count": len(chunk),
                    "token_count": token_count
                }
            )
            documents.append(doc)
        
        return documents
    
    def process_documents(self, file_paths: List[str]) -> List[Document]:
        all_documents = []
        processed_files = 0
        skipped_files = []

        for file_path in file_paths:
            file_path = Path(file_path)

            # Normalize file path and check existence
            if not file_path.exists():
                logger.warning(f"⚠️ File not found: {file_path}")
                skipped_files.append(str(file_path))
                continue

            # Normalize extension (case insensitive)
            file_ext = file_path.suffix.lower()

            # Extract text based on file type
            text = ""
            if file_ext == '.pdf':
                text = self.extract_text_from_pdf(str(file_path))
            elif file_ext == '.docx':
                text = self.extract_text_from_docx(str(file_path))
            elif file_ext == '.txt':
                text = self.extract_text_from_txt(str(file_path))
            else:
                logger.warning(f"⚠️ Unsupported file format: {file_path} (extension: {file_ext})")
                skipped_files.append(str(file_path))
                continue

            if text.strip():
                documents = self.create_chunks(text, str(file_path))
                all_documents.extend(documents)
                processed_files += 1
            else:
                logger.warning(f"⚠️ No text extracted from: {file_path}")
                skipped_files.append(str(file_path))

        # Log summary
        logger.info(f"✅ Processed {processed_files} files successfully")
        logger.info(f"📊 Generated {len(all_documents)} total chunks")

        if skipped_files:
            logger.warning(f"⚠️ Skipped {len(skipped_files)} files:")
            for file in skipped_files:
                logger.warning(f"  - {file}")

        # Deduplicate chunks using full content hash
        unique_documents = self._deduplicate_chunks(all_documents)

        if len(unique_documents) != len(all_documents):
            logger.info(f"🔄 Removed {len(all_documents) - len(unique_documents)} duplicate chunks")

        return unique_documents

    def _deduplicate_chunks(self, documents: List[Document]) -> List[Document]:
        """Remove duplicate chunks using full content hash."""
        seen_hashes = set()
        unique_docs = []

        for doc in documents:
            # Create hash of full content
            content_hash = hashlib.md5(doc.page_content.encode('utf-8')).hexdigest()

            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_docs.append(doc)

        return unique_docs

# =============================================================================
# 8. EMBEDDINGS GENERATION - MODIFIED FOR BEDROCK
# =============================================================================

class EmbeddingsManager:
    def __init__(self, aws_region: str = "us-east-1"):
        try:
            self.embeddings = BedrockTitanEmbeddings(
                model_id="amazon.titan-embed-text-v2:0",
                region_name=aws_region
            )
            logger.info("✅ AWS Bedrock Titan embeddings initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Bedrock embeddings: {e}")
            self.embeddings = None

    def create_vector_store(self, documents: List[Document], weaviate_client) -> Weaviate:
        try:
            if not self.embeddings:
                logger.error("❌ Embeddings not available")
                return None

            logger.info("🔄 Creating vector store with Bedrock embeddings...")
            vector_store = Weaviate.from_documents(
                documents=documents,
                embedding=self.embeddings,
                client=weaviate_client,
                index_name="PoliticalDocuments",
                text_key="content",
                by_text=False
            )
            logger.info("✅ Vector store created successfully")
            return vector_store
        except Exception as e:
            logger.error(f"❌ Failed to create vector store: {e}")
            return None

# =============================================================================
# 9. RETRIEVER SETUP (unchanged)
# =============================================================================

class HybridRetriever:
    def __init__(self, vector_store, documents: List[Document]):
        self.vector_store = vector_store
        self.documents = documents

        try:
            self.bm25_retriever = BM25Retriever.from_documents(documents)
            self.bm25_retriever.k = 10
        except:
            self.bm25_retriever = None

        if vector_store:
            self.semantic_retriever = vector_store.as_retriever(search_kwargs={"k": 10})
        else:
            self.semantic_retriever = None

    def expand_query(self, query: str) -> List[str]:
        synonyms = {
            "issues": ["problems", "challenges", "concerns"],
            "promises": ["commitments", "pledges", "assurances"],
            "demands": ["requests", "needs", "requirements"],
            "employment": ["jobs", "work", "livelihood"],
            "infrastructure": ["facilities", "development", "services"],
            "healthcare": ["medical", "health services", "hospitals"],
            "education": ["schools", "learning", "academic"]
        }

        expanded_queries = [query]
        for term, syns in synonyms.items():
            if term in query.lower():
                for syn in syns[:2]:
                    expanded_queries.append(query.lower().replace(term, syn))

        return expanded_queries[:3]

    def retrieve_for_analysis(self, analysis_type: str) -> List[Document]:
        query_map = {
            "major_issues": "major issues problems challenges faced by public people areas villages constituency local",
            "unfulfilled_promises": "election promises commitments made political parties DMK AIADMK BJP Congress unfulfilled broken incomplete",
            "public_demands": "public demands requests needs wants from people citizens areas villages constituency local community",
            "manifesto_suggestions": "manifesto promises suggestions recommendations constituency area development local needs community"
        }

        query = query_map.get(analysis_type, analysis_type)
        expanded_queries = self.expand_query(query)

        all_results = []
        for expanded_query in expanded_queries:
            if self.semantic_retriever:
                try:
                    semantic_results = self.semantic_retriever.get_relevant_documents(expanded_query)
                    all_results.extend(semantic_results)
                except:
                    pass

            if self.bm25_retriever:
                try:
                    bm25_results = self.bm25_retriever.get_relevant_documents(expanded_query)
                    all_results.extend(bm25_results)
                except:
                    pass

        # Remove duplicates using full content hash
        unique_results = []
        seen_hashes = set()

        for doc in all_results:
            # Use full content hash for better deduplication
            content_hash = hashlib.md5(doc.page_content.encode('utf-8')).hexdigest()
            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_results.append(doc)

        # Basic reranking: prioritize longer, more informative chunks
        unique_results.sort(key=lambda x: len(x.page_content), reverse=True)

        return unique_results[:10]

# =============================================================================
# 10. BEDROCK CLAUDE LLM CLASS
# =============================================================================

class BedrockClaudeLLM:
    """Custom LLM class for AWS Bedrock Claude using aioboto3"""
    
    def __init__(self, model_id: str = "anthropic.claude-3-7-sonnet-20250219-v1:0"):
        self.model_id = model_id
        # Store AWS credentials from environment or default
        self.region_name = os.environ.get("BEDROCK_REGION")
        self.aws_access_key_id = os.environ.get("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.environ.get("AWS_SECRET_ACCESS_KEY")
        
        # Test connection
        try:
            self._test_connection()
            logger.info(f"✅ AWS Bedrock Claude LLM initialized for region: {region_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AWS Bedrock Claude LLM: {e}")
            raise e
    
    def _test_connection(self):
        """Test Bedrock connection synchronously"""
        try:
            client = boto3.client(
                service_name='bedrock-runtime',
                region_name=self.region_name,
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
            )
            logger.info("✅ Bedrock Claude LLM client test successful")
        except Exception as e:
            logger.error(f"❌ Bedrock Claude LLM connection test failed: {e}")
            raise e
    
    def _create_bedrock_request_body(self, messages: List[Dict], temperature: float = 0.3, max_tokens: int = 4000) -> Dict:
        """Create request body for Anthropic Claude models"""
        # Convert messages to Claude format
        claude_messages = []
        system_message = ""
        
        for message in messages:
            role, content = message
            if role == "system":
                system_message = content
            elif role == "human" or role == "user":
                claude_messages.append({"role": "user", "content": content})
            elif role == "assistant":
                claude_messages.append({"role": "assistant", "content": content})
        
        request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "temperature": temperature,
            "max_tokens": max_tokens,
            "messages": claude_messages
        }
        
        # Add system message if present
        if system_message:
            request_body["system"] = system_message
            
        return request_body
    
    async def ainvoke_async(self, messages: List[tuple], temperature: float = 0.3, max_tokens: int = 4000, max_retries: int = 2) -> Dict:
        """Invoke Claude model asynchronously with retry logic"""
        session = aioboto3.Session()
        
        for attempt in range(max_retries + 1):
            try:
                async with session.client(
                    service_name='bedrock-runtime',
                    region_name=self.region_name,
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                ) as client:
                    
                    # Prepare request body
                    request_body = self._create_bedrock_request_body(messages, temperature, max_tokens)
                    
                    # Call Bedrock API
                    response = await client.invoke_model(
                        modelId=self.model_id,
                        contentType="application/json",
                        accept="application/json",
                        body=json.dumps(request_body)
                    )
                    
                    # Parse response
                    response_body = json.loads(await response['body'].read())
                    
                    # Extract content
                    content = response_body['content'][0]['text']
                    
                    # Create response object similar to Azure OpenAI format
                    return {
                        "content": content,
                        "usage": {
                            "input_tokens": response_body.get('usage', {}).get('input_tokens', 0),
                            "output_tokens": response_body.get('usage', {}).get('output_tokens', 0),
                            "total_tokens": response_body.get('usage', {}).get('input_tokens', 0) + response_body.get('usage', {}).get('output_tokens', 0)
                        },
                        "stop_reason": response_body.get('stop_reason', 'end_turn')
                    }
                    
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                
                if error_code == 'ThrottlingException' and attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"⚠️ Rate limit hit (attempt {attempt + 1}/{max_retries + 1}), waiting {wait_time}s")
                    await asyncio.sleep(wait_time)
                    continue
                elif error_code == 'ValidationException':
                    logger.error(f"❌ Invalid request: {e}")
                    raise e
                elif error_code == 'AccessDeniedException':
                    logger.error(f"❌ Access denied - check permissions: {e}")
                    raise e
                else:
                    logger.error(f"❌ AWS Bedrock API error: {e}")
                    if attempt < max_retries:
                        await asyncio.sleep(1)
                        continue
                    raise e
                    
            except Exception as e:
                logger.error(f"❌ Error invoking Claude model: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(2)
                    continue
                raise e
        
        raise Exception("Maximum retries exceeded")
    
    def ainvoke(self, messages: List[tuple], **kwargs) -> Dict:
        """Synchronous wrapper for async invoke"""
        return asyncio.run(self.ainvoke_async(messages, **kwargs))

# =============================================================================
# 11. ANALYSIS AGENTS - MODIFIED FOR BEDROCK CLAUDE
# =============================================================================

class PoliticalAnalysisAgent:
    def __init__(self, retriever: HybridRetriever, aws_region: str = "us-east-1"):
        self.retriever = retriever
        self.llm = BedrockClaudeLLM(
            model_id="anthropic.claude-3-7-sonnet-20250219-v1:0",
            region_name=aws_region
        )

    def analyze_section(self, section_type: str) -> str:
        try:
            relevant_docs = self.retriever.retrieve_for_analysis(section_type)

            if not relevant_docs:
                return f"No relevant information found for {section_type} analysis."

            context = "\n\n".join([
                f"Document: {doc.metadata.get('source', 'Unknown')}\n"
                f"Content: {doc.page_content}"
                for doc in relevant_docs
            ])

            prompt = ANALYSIS_PROMPTS[section_type].format(context=context)
            response = self._call_bedrock_claude(prompt)

            return response if response else f"Analysis failed for {section_type}"
        except Exception as e:
            logger.error(f"❌ Error analyzing {section_type}: {str(e)}")
            return f"Error analyzing {section_type}: {str(e)}"

    def _call_bedrock_claude(self, prompt: str, max_retries: int = 2) -> Optional[str]:
        """Call AWS Bedrock Claude API with retry logic and better error handling."""
        try:
            messages = [("user", prompt)]
            response = self.llm.ainvoke(messages, temperature=0.3, max_tokens=4000)
            
            # Check for truncation warning
            if response.get("stop_reason") == "max_tokens":
                logger.warning("⚠️ Response may be truncated due to max_tokens limit")
            
            return response["content"]
            
        except Exception as e:
            logger.error(f"❌ Error calling Bedrock Claude: {e}")
            return None

    def generate_full_report(self) -> str:
        sections = {}
        analysis_types = ["major_issues", "unfulfilled_promises", "public_demands", "manifesto_suggestions"]

        logger.info("🔄 Starting analysis sections...")
        for analysis_type in analysis_types:
            logger.info(f"🔄 Analyzing {analysis_type}...")
            sections[analysis_type] = self.analyze_section(analysis_type)
            logger.info(f"✅ Completed {analysis_type} analysis")

        # Synthesize final report
        logger.info("🔄 Synthesizing final report...")
        sections_text = "\n\n".join([f"{k.upper()}:\n{v}" for k, v in sections.items()])
        synthesis_prompt = ANALYSIS_PROMPTS["synthesis"].format(sections=sections_text)
        final_report = self._call_bedrock_claude(synthesis_prompt)

        return final_report if final_report else self._manual_synthesis(sections)

    def _manual_synthesis(self, sections: dict) -> str:
        return f"""
POLITICAL ANALYSIS REPORT
========================

Assembly Segment [Constituency Analysis]
This analysis covers the major political issues, promises, demands, and recommendations
for the constituency based on comprehensive document analysis.

Major Issues [Grouped with headings and descriptive paragraphs]
{sections.get('major_issues', 'Analysis not available')}

Unfulfilled Promises by Ruling Party [Promise | Status table]
{sections.get('unfulfilled_promises', 'Analysis not available')}

Key Public Demands [Bulleted list with evidence]
{sections.get('public_demands', 'Analysis not available')}

Suggested Manifesto Promises [List with justification]
{sections.get('manifesto_suggestions', 'Analysis not available')}

---
Report generated using AWS Bedrock Claude 3.7 Sonnet with Titan embeddings
All analysis based on retrieved document evidence
"""

# =============================================================================
# 12. MAIN PIPELINE - FULLY BEDROCK POWERED
# =============================================================================

class PoliticalAnalysisPipeline:
    def __init__(self, aws_region: str = "us-east-1"):
        self.weaviate_manager = WeaviateManager()
        self.doc_processor = DocumentProcessor()
        self.embeddings_manager = EmbeddingsManager(aws_region=aws_region)
        self.aws_region = aws_region

    def run_pipeline(self, document_paths: List[str]) -> str:
        try:
            # Process documents
            logger.info("🔄 Processing documents...")
            documents = self.doc_processor.process_documents(document_paths)
            if not documents:
                return "No documents processed successfully"

            # Try Weaviate connection
            vector_store = None
            logger.info("🔄 Connecting to Weaviate...")
            if self.weaviate_manager.connect():
                logger.info("✅ Connected to Weaviate")
                collection = self.weaviate_manager.create_collection()
                if collection:
                    logger.info("✅ Weaviate collection ready")
                    # Create vector store with Bedrock embeddings
                    vector_store = self.embeddings_manager.create_vector_store(
                        documents, 
                        self.weaviate_manager.client
                    )
                else:
                    logger.warning("⚠️ Failed to create Weaviate collection")
            else:
                logger.warning("⚠️ Failed to connect to Weaviate, using BM25 only")

            # Setup retriever
            logger.info("🔄 Setting up hybrid retriever...")
            retriever = HybridRetriever(vector_store, documents)

            # Initialize analysis agent with AWS region
            logger.info("🔄 Initializing analysis agent...")
            agent = PoliticalAnalysisAgent(retriever, aws_region=self.aws_region)

            # Generate report
            logger.info("🔄 Generating political analysis report...")
            report = agent.generate_full_report()

            # Cleanup
            self.weaviate_manager.close()

            logger.info("✅ Analysis pipeline completed successfully")
            return report
        except Exception as e:
            logger.error(f"❌ Pipeline failed: {str(e)}")
            self.weaviate_manager.close()
            return f"Pipeline failed: {str(e)}"

def find_documents(directory: str = ".") -> List[str]:
    """Find all supported document files in directory."""
    supported_extensions = [".pdf", ".docx", ".txt"]
    found_files = []

    for ext in supported_extensions:
        # Case insensitive search
        pattern = os.path.join(directory, f"*{ext}")
        found_files.extend(glob.glob(pattern))
        # Also search uppercase
        pattern = os.path.join(directory, f"*{ext.upper()}")
        found_files.extend(glob.glob(pattern))

    # Remove duplicates and normalize paths
    found_files = list(set([os.path.normpath(f) for f in found_files]))

    logger.info(f"Found {len(found_files)} document files")
    for file in found_files:
        logger.info(f"  - {file}")

    return found_files

def get_document_paths() -> List[str]:
    """Get document paths from command line args or auto-discover."""
    parser = argparse.ArgumentParser(description="Political Analysis Pipeline with AWS Bedrock")
    parser.add_argument("--docs", nargs="+", help="Document file paths")
    parser.add_argument("--dir", default=".", help="Directory to scan for documents")
    parser.add_argument("--install", action="store_true", help="Install dependencies")
    parser.add_argument("--region", default="us-east-1", help="AWS region for Bedrock (default: us-east-1)")

    args = parser.parse_args()

    if args.install:
        create_requirements_file()
        if install_packages():
            print("✅ Dependencies installed. Please run the script again.")
        sys.exit(0)

    if args.docs:
        # Use provided document paths
        document_paths = args.docs
        logger.info(f"Using provided documents: {document_paths}")
    else:
        # Auto-discover documents in directory
        document_paths = find_documents(args.dir)
        if not document_paths:
            print(f"❌ No supported documents found in {args.dir}")
            print("Supported formats: PDF, DOCX, TXT")
            sys.exit(1)

    # Validate files exist
    existing_files = []
    for path in document_paths:
        if os.path.exists(path):
            existing_files.append(path)
        else:
            logger.warning(f"⚠️ File not found: {path}")

    if not existing_files:
        logger.error("❌ No valid document files found")
        sys.exit(1)

    return existing_files, args.region

def check_aws_credentials():
    """Check if AWS credentials are properly configured."""
    try:
        # Try to create a bedrock client to test credentials
        session = aioboto3.Session()
        
        # Test async client creation
        async def test_client():
            try:
                async with session.client('bedrock-runtime', region_name='us-east-1') as client:
                    logger.info("✅ AWS credentials found and aioboto3 client test successful")
                    return True
            except Exception as e:
                logger.error(f"❌ aioboto3 client test failed: {e}")
                return False
        
        # Run the async test
        result = asyncio.run(test_client())
        return result
        
    except Exception as e:
        logger.error(f"❌ AWS credentials not found or invalid: {e}")
        print("❌ AWS credentials not configured!")
        print("💡 Please configure AWS credentials using one of these methods:")
        print("   1. AWS CLI: aws configure")
        print("   2. Environment variables: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY")
        print("   3. IAM roles (if running on EC2)")
        print("   4. AWS credentials file (~/.aws/credentials)")
        print("\n🔑 Required permissions for Bedrock:")
        print("   - bedrock:InvokeModel")
        print("   - bedrock:ListFoundationModels")
        return False

def main():
    """Main execution function with improved error handling."""
    try:
        # Setup environment
        setup_environment()

        # Check AWS credentials
        if not check_aws_credentials():
            sys.exit(1)

        # Create requirements file for convenience
        create_requirements_file()

        # Get document paths and AWS region
        document_paths, aws_region = get_document_paths()

        print(f"📁 Processing {len(document_paths)} documents...")
        for path in document_paths:
            print(f"  - {os.path.basename(path)}")

        print(f"🌍 Using AWS region: {aws_region}")
        print(f"🤖 Using embeddings: Amazon Titan Text Embeddings V2")
        print(f"🧠 Using LLM: Claude 3.7 Sonnet via AWS Bedrock")

        # Run pipeline
        pipeline = PoliticalAnalysisPipeline(aws_region=aws_region)
        report = pipeline.run_pipeline(document_paths)

        # Save to file
        output_file = "political_analysis_output.md"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(report)

        print(f"✅ Analysis complete! Output saved to: {output_file}")
        print(f"📊 Used AWS Bedrock with Titan embeddings + Claude 3.7 Sonnet in region: {aws_region}")

    except KeyboardInterrupt:
        logger.info("⏹️ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Pipeline failed: {e}")
        print(f"❌ Analysis failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()