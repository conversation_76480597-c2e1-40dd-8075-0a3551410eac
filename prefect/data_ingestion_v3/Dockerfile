FROM python:3.9-slim
# Set the working directory in the container
WORKDIR /usr/src/app

# Install system dependencies
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y git libmagic-dev poppler-utils tesseract-ocr libreoffice pandoc

# Install dependencies
COPY requirements.txt /usr/src/app/
RUN pip install -r requirements.txt

# Copy the application code
COPY src/ /usr/src/app/