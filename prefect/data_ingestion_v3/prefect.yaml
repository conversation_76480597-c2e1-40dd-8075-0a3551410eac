deployments:
  - name: chat_with_kb_v4
    version: 0.2
    tags:
      - kb-file-url-2.3.2
    description: Data ingestion using Semantic Chunking with LLamaparser/Docling
    entrypoint: prefect/data_ingestion_v3/src/flow.py:execute_data_pipeline
    work_pool:
      name: knowledge-base
      job_variables:
        image: afwtech/genflow:kb-file-url-2.3.2
        volumes:
         - /tmp/kb_files:/tmp/kb_files
      work_queue_name:
    parameters: {}
    schedules: []
    pull:
      - prefect.deployments.steps.git_clone:
          repository: https://github.com/aiplanethub/CHAT-WITH-KNOWLEDGEBASE.git
          branch: stage
          access_token: ****************************************