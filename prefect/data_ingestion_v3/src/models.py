from pydantic import BaseModel
from typing import List, Dict, Optional
from enum import Enum


class ChunkingConfig(BaseModel):
    """Configuration for chunking."""

    type: str
    kwargs: Dict


class RetrieverConfig(BaseModel):
    """Configuration for retriever."""

    type: str
    kwargs: Dict


class EmbeddingConfig(BaseModel):
    """Configuration for embedding."""

    type: str
    kwargs: Dict

class ExtractionLibrary(Enum):
    LLAMA_PARSER = "llamaparser"
    DOCLING = "docling"

