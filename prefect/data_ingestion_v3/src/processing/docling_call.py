import requests
from langchain.schema import Document
import time
from pathlib import Path

def process_pdf(file_path: str):
    # Endpoint URLs
    base_url = "https://8080-01jjrmvrnmnsx05mdrrq7y35bq.cloudspaces.litng.ai/conversion-jobs"
    
    try:
        # Step 1: Submit the document for conversion
        with open(file_path, 'rb') as file:
            payload = {
                "extract_tables_as_images": True,  # Convert boolean to string ('true'/'false')
                "image_resolution_scale": 1
             }
            response = requests.post(
                base_url,
                headers={"accept": "application/json"},
                data=payload,
                files={"document": file}
            )
        
        # Check if the request was successful
        if response.status_code != 200:
            raise Exception(f"Failed to submit the document: {response.text}")
        
        response_data = response.json()
        job_id = response_data.get("job_id")
        
        if not job_id:
            raise Exception("Job ID not found in the response.")
        
        print(f"Job submitted successfully. Job ID: {job_id}")
        
        # Step 2: Poll the job status
        status = "IN_PROGRESS"
        while status == "IN_PROGRESS":
            time.sleep(15)  # Wait for 15 seconds before checking the status
            status_response = requests.get(
                f"{base_url}/{job_id}",
                headers={"accept": "application/json"}
            )
            
            if status_response.status_code != 200:
                raise Exception(f"Failed to fetch job status: {status_response.text}")
            
            status_data = status_response.json()
            status = status_data.get("status")
            
            print(f"Job Status: {status}")
            
            if status == "FAILURE":
                error_message = status_data.get("error", "Unknown error occurred.")
                raise Exception(f"Job failed with error: {error_message}")
        
        if status == "SUCCESS":
            result = status_data.get("result")
            document_list = []
            path = Path(file_path)
            document = Document(
                page_content=result.get("markdown"),
                metadata={
                    "source": str(file_path),
                    "page_number": 1,
                    "filetype": path.suffix,
                    "file_directory": str(file_path),
                    "filename": path.name,
                        # Include additional metadata as needed
                    },
            )
            document_list.append(document)
            print(f"Job completed successfully! Job ID: {job_id}")
            return document_list
        else:
            raise Exception("Unexpected status: {status}")
    
    except Exception as e:
        raise Exception(f"An error occurred: {e}")
