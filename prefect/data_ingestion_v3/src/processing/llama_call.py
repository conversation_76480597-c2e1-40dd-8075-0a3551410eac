from llama_parse import <PERSON>lamaParse
from llama_index.core import SimpleDirectoryReader
from langchain.schema import Document
import pandas as pd
import os
from pathlib import Path
from prefect.blocks.system import String

api_key = String.load("llamaparser-api-key")

def csv_to_markdown(file_path):
    df = pd.read_csv(file_path)
    return df.to_markdown()

# Function to convert Excel to Markdown (reads first sheet)
def excel_to_markdown(file_path):
    df = pd.read_excel(file_path, sheet_name=0)
    return df.to_markdown()



def process_pdf(file_path: str):
    try:
        parser = LlamaParse(api_key=str(api_key.value), result_type="markdown")  # Extract as Markdown

        # Define extractors for different file types
        file_extractor = {
            ".pdf": parser,
            ".docx": parser,
            ".pptx": parser,
            ".txt": parser,
            ".html": parser,
            ".md": parser,
            ".csv": csv_to_markdown,
            ".xls": excel_to_markdown,
            ".xlsx": excel_to_markdown,
            ".xlsm": excel_to_markdown,
            ".xlsb": excel_to_markdown
        }
        ext = os.path.splitext(file_path)[1].lower()
        print("Hello")
        # Validate supported file format
        
        if ext in file_extractor:
            document = SimpleDirectoryReader(input_files=[file_path], file_extractor=file_extractor).load_data()
            document_list = []
            # print(document.to_markdown())
            for doc in document:
                document = Document(
                    page_content=doc.text,
                    metadata={
                        "source": doc.metadata["file_path"],
                        "page_number": 1,
                        "filetype": doc.metadata["file_type"],
                        "file_directory": str(file_path),
                        "filename": doc.metadata["file_name"],
                    },
                )
                document_list.append(document)
        
            return document_list

        else:
            raise ValueError(f"❌ Unsupported file format: {ext}")
    except Exception as e:
        raise Exception(f"Failed to process the document: {e}")