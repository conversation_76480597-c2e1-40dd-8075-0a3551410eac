from typing import List, Optional, Dict, Any
from pathlib import Path
from prefect import flow, task
from prefect.logging import get_run_logger
from prefect_aws.s3 import S3Bucket
import os
import uuid
import requests

from langchain.schema import Document
from langchain.text_splitter import TextSplitter
import weaviate
from processing.docling_call import process_pdf as docling_process_file
from processing.llama_call import process_pdf as llama_process_file
# from processing.url import process_url as core_process_url
from models import ChunkingConfig, RetrieverConfig, EmbeddingConfig, ExtractionLibrary
from utils import import_class, clean_metadata


s3_bucket_block: S3Bucket = S3Bucket.load("s3-chat-with-pdf-v3")


@task
def send_webhook_notification(
    url: str, 
    status: str, 
    message: str, 
    api_key: Optional[str] = None, 
    **kwargs: Any
):
    """
    Send a notification to a webhook URL with dynamic data.

    Args:
        url (str): Webhook endpoint URL.
        status (str): Status of the flow (e.g., "Running", "Completed", "Failed").
        message (str): Custom message to send.
        api_key (str, optional): API key for authentication. Defaults to None.
        kwargs (dict): Additional data to include in the payload.
    """
    logger = get_run_logger()
    headers = {}
    if api_key:
        headers["Authorization"] = api_key

    payload = {
        "status": status,
        "message": message,
        **kwargs,  # Include additional data from kwargs
    }

    try:
        logger.info(f"Kwargs: {kwargs}")
        response = requests.post(url, headers=headers, json=payload, allow_redirects=False)
        response.raise_for_status()
        #print(response.json())
        logger.info(f"Webhook notification sent successfully: {response.json()}")
    except requests.RequestException as e:
        logger.info(f"Kwargs: {kwargs}")
        logger.error(f"Failed to send webhook notification: {e}")



@task
def read_file_from_s3(object_name: str , bucket_name: Optional[str] = None):
    filename = Path(object_name).stem
    file_ext = Path(object_name).suffix
    #print(f"Filename: {filename}; FileExt: {file_ext}")
    parent_folder = "/tmp/kb_files"
    fp = f"{parent_folder}/{filename}{file_ext}"

    if not os.path.exists(parent_folder):
        os.makedirs(parent_folder)

    if bucket_name:
        s3_bucket_block.bucket_name = bucket_name
        
    filename = s3_bucket_block.download_object_to_path(from_path=object_name, to_path=fp)

    return fp




@task
def process_file(fp: str, extraction_library: ExtractionLibrary, fast_mode: bool) -> List[Document]:
    if extraction_library == ExtractionLibrary.LLAMA_PARSER:
        return llama_process_file(fp, fast_mode)
    elif extraction_library == ExtractionLibrary.DOCLING:
        return docling_process_file(fp)




# @task
# def process_url(url: str) -> List[Document]:
#     return core_process_url(url)

@task
def chunk_output(output: List[Document], chunking_config: ChunkingConfig, file_id: str) -> List[Document]:
    splitter_class: TextSplitter = import_class(f"langchain.text_splitter.{chunking_config.type}")
    text_splitter = splitter_class(**chunking_config.kwargs)
    chunked_docs = []
    for doc_index, doc in enumerate(output):
        content = doc.page_content
        if not content.strip():
            continue

        # Add page number if available
        page_number = doc.metadata.get("page_number", doc_index)

        chunks = text_splitter.split_text(content)

        for chunk_index, chunk in enumerate(chunks):
            unique_id = str(uuid.uuid4())  
            chunked_docs.append(Document(
                page_content=chunk,
                metadata={
                    "source": doc.metadata.get("source", "Unknown"),
                    "last_modified": doc.metadata.get("last_modified", "Unknown"),
                    "filetype": doc.metadata.get("filetype", "Unknown"),
                    "languages": doc.metadata.get("languages", ["Unknown"]),
                    "page_number": page_number,
                    "file_directory": doc.metadata.get("file_directory", "Unknown"),
                    "filename": doc.metadata.get("filename", "Unknown"),
                    "unique_id": unique_id,
                    "file_id": file_id,
                }
            ))
    
    return chunked_docs

@task
def generate_embedding(text:Document , embedding):
    return embedding.embed_query(text.page_content)


@task
def load_into_vectordb(
    chunked_output: List[Document],
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    file_id: str,
) -> None:
    try:
 
        if embedding_config.type == "AzureOpenAIEmbeddings":
            from  langchain_openai import AzureOpenAIEmbeddings
            embedding = AzureOpenAIEmbeddings(
                openai_api_key=embedding_config.kwargs.get("openai_api_key"),
                azure_deployment=embedding_config.kwargs.get("azure_deployment"),
                azure_endpoint=embedding_config.kwargs.get("azure_endpoint"),
                openai_api_version=embedding_config.kwargs.get("openai_api_version"),
                chunk_size=2048,
            )
            
        else:
            embedding_class = import_class(f"langchain_community.embeddings.{embedding_config.type}")
            embedding = embedding_class(**embedding_config.kwargs)

        index_name = retriever_config.kwargs.get("index_name")

        client = weaviate.Client(
            url=retriever_config.kwargs.get("weaviate_url"),
            auth_client_secret=weaviate.AuthApiKey(api_key=retriever_config.kwargs.get("weaviate_api_key"))
        ) 

        for chunk in chunked_output:
            embedding_vector = generate_embedding(chunk , embedding)
            data_object = {
                "text": chunk.page_content,
                "metadata": chunk.metadata,
                "file_id": file_id
            }
            client.data_object.create(  
                data_object=data_object,
                class_name=index_name,
                vector=embedding_vector
            )
    except Exception as e:
        raise ValueError(f"Failed to load data into VectorDB: {str(e)}")
    # vectorstore_class = import_class(f"langchain_community.vectorstores.{retriever_config.type}")
    # vectorstore_class.from_documents(documents=chunked_output, embedding=embedding, **retriever_config.kwargs)
    


@flow
def execute_data_pipeline(
    chunking_config: ChunkingConfig,
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    webhook_config: Dict[str, Any],
    sources: List[Dict[str, Any]],
):
    webhook_url = webhook_config.get("callback_url")
    webhook_api_key = webhook_config.get("api_key")


    for source in sources:
        source_kwargs = source.get("kwargs", {})
        try:
            source_type = source.get("type")
            source_name = source.get("source_name")
            fast_mode = source_kwargs.get("fast_mode", False)
            file_id = source_kwargs.get("file_id", "")
            send_webhook_notification(
                webhook_url, "Processing", f"Pipeline execution started for {source_type}: {source_name}.", webhook_api_key, **source_kwargs
            )

            extracted_data = None

            # if source_type == "url":
            #     extracted_data = process_url(url=source_name)
            if source_type == "aws_object":
                fp = read_file_from_s3(object_name=source_name, bucket_name=source_kwargs.get("bucket_name", None))
                extraction_library_str = source_kwargs.get("extraction_library", "llamaparser")
                extraction_library = ExtractionLibrary(extraction_library_str)
                extracted_data = process_file(fp=fp, extraction_library= extraction_library, fast_mode=fast_mode)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            chunked_output = chunk_output(output=extracted_data, chunking_config=chunking_config , file_id=file_id)

            for doc in chunked_output:
                clean_metadata(doc.metadata)

            load_into_vectordb(
                chunked_output=chunked_output, 
                retriever_config=retriever_config, 
                embedding_config=embedding_config,
                file_id=file_id
            )

            send_webhook_notification(
                webhook_url, "Success", f"Processed {source_type}: {source_name}", webhook_api_key, **source_kwargs
            )

        except Exception as e:
            send_webhook_notification(
                webhook_url, "Failed", f"Pipeline execution failed: {str(e)}", webhook_api_key, **source_kwargs
            )
            raise

if __name__ == "__main__":
    cc = ChunkingConfig(type="RecursiveCharacterTextSplitter", kwargs={"chunk_size": 4000, "chunk_overlap": 200})

    rc = RetrieverConfig(
        type="Weaviate",
        kwargs={
            "weaviate_url": "https://weaviate.aimarketplace.co",
            "weaviate_api_key": "********************************",
            "index_name": "Take_me_to_space_5",
        },
    )

    ec = EmbeddingConfig(
        type="AzureOpenAIEmbeddings",
        kwargs={
            "openai_api_key": "8VatSchgvZZMw8u62uhKnJgN8M56SciALrbYvLRqDqEMvd3Scj0hJQQJ99BBACYeBjFXJ3w3AAABACOGlV7v",
            "azure_deployment": "text-embedding-3-small", 
            "azure_endpoint": "https://tunkeyproduct.openai.azure.com/", 
            "openai_api_version": "2023-05-15"
        }
    )
    webhook_config = {
        "callback_url": "http://127.0.0.1:8000/api/v1/folder/file/update-status",
        "api_key": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.oRUNgBhgNIvp4PRglgbKz48D_P6FcUCujGkfJnWyuz0",
    }

    sources = [
        {"type": "aws_object", "source_name": "rfp_agent/c84ef7ac-4e8a-4a0d-a7fc-0c2cd2dd5def/Microsoft Azure Government Datasheet.PDF", "kwargs": {"file_id": "s3-chat-with-pdf" , "fast_mode": True}},
        {"type": "aws_object", "source_name": "uploads/af920640-39eb-466e-9ac8-c97d8ec96cad/Abara-LMS-Brochure-2024.pdf", "kwargs": {"file_id": "abara-lms" , "fast_mode": True}},

    ]

    execute_data_pipeline(
        chunking_config=cc, 
        retriever_config=rc, 
        embedding_config=ec, 
        webhook_config=webhook_config, 
        sources=sources
    )