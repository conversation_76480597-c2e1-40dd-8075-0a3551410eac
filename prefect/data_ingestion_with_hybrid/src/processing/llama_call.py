import requests
import time
import json
from langchain.schema import Document
from pathlib import Path

from prefect.blocks.system import String

api_key = String.load("llamaparser-api-key")


# API endpoints
UPLOAD_URL = "https://api.cloud.llamaindex.ai/api/parsing/upload"
STATUS_URL = "https://api.cloud.llamaindex.ai/api/parsing/job/{}"
RESULT_URL = "https://api.cloud.llamaindex.ai/api/parsing/job/{}/result/json"

def process_pdf(file_path, fast_mode=False):
    """
    Uploads a file, checks processing status, and retrieves parsed content.
    Raises an error if any step fails.
    """
    headers = {
        "Authorization": f"Bearer {api_key.value}",
        "accept": "application/json"
    }

    # Upload the file
    try:
        with open(file_path, "rb") as file:
            files = {"file": file}  # Only pass the file object, no need for file_path tuple
            if fast_mode:
                response = requests.post(UPLOAD_URL, headers=headers, files=files, data={"fast_mode": "true"})
            else:
                response = requests.post(UPLOAD_URL, headers=headers, files=files)
            response_data = response.json()

        if response.status_code != 200:
            raise RuntimeError(f"Upload failed: {response_data}")

        job_id = response_data.get("id")
        if not job_id:
            raise RuntimeError("Failed to retrieve Job ID from response.")

        print(f"Upload successful! Job ID: {job_id}")
    except Exception as e:
        raise RuntimeError(f"Error uploading file: {e}")

    # Check job status
    try:
        while True:
            response = requests.get(STATUS_URL.format(job_id), headers=headers)
            response_data = response.json()
            status = response_data.get("status")

            if not status:
                raise RuntimeError("Invalid status response from API.")

            print(f"Job Status: {status}")

            if status == "SUCCESS":
                break
            elif status == "FAILED":
                raise RuntimeError("Parsing failed!")

            time.sleep(5)
    except Exception as e:
        raise RuntimeError(f"Error checking job status: {e}")

    # Retrieve parsed result
    try:
        response = requests.get(RESULT_URL.format(job_id), headers=headers)

        if response.status_code != 200:
            raise RuntimeError(f"Failed to retrieve result: {response.text}")

        result_data = response.json()
        document = result_data.get("pages")
        document_list = []
        path = Path(file_path)
        for doc in document:
            # print(doc)
            document = Document(
                page_content=doc['md'] if 'md' in doc else doc['text'],
                metadata={
                    "source": str(file_path),
                    "page_number": doc['page'],
                    "filetype": path.suffix,
                    "file_directory": str(file_path),
                    "filename": path.name,
                },
            )
            document_list.append(document)
        return document_list
    except Exception as e:
        raise RuntimeError(f"Error retrieving parsed result: {e}")


