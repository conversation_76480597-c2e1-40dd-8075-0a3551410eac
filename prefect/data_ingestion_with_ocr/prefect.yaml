deployments:
  - name: chat_with_kb_v3
    version: 0.2
    tags:
      - kb-file-url-2.6.1
    description: Data ingestion with llamparser and docling
    entrypoint: prefect/data_ingestion_with_ocr/src/flow.py:execute_data_pipeline
    work_pool:
      name: knowledge-base
      job_variables:
        image: afwtech/genflow:kb-file-url-2.6.1
        volumes:
         - /tmp/kb_files:/tmp/kb_files
      work_queue_name:
    parameters: {}
    schedules: []
    pull:
      - prefect.deployments.steps.git_clone:
          repository: https://github.com/aiplanethub/CHAT-WITH-KNOWLEDGEBASE.git
          branch: stage
          access_token: ****************************************