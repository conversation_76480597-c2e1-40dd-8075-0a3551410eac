from typing import List, Optional, Dict, Any
from pathlib import Path
from prefect import flow, task
from prefect.logging import get_run_logger
from prefect_aws.s3 import S3Bucket
from prefect.blocks.system import JSON
import os
import uuid
import requests

from langchain.schema import Document
from langchain.text_splitter import TextSplitter

from processing.docling_call import process_pdf as docling_process_file
from processing.llama_call import process_pdf as llama_process_file

# from processing.url import process_url as core_process_url
from models import ChunkingConfig, RetrieverConfig, EmbeddingConfig, ExtractionLibrary
from utils import import_class, clean_metadata

# Load max file size from Prefect block
file_config = JSON.load("file-config")
MAX_FILE_SIZE_PREFECT = file_config.value.get("max_file_size")

s3_bucket_block: S3Bucket = S3Bucket.load("s3-chat-with-pdf-v3")


@task
def send_webhook_notification(
    url: str, status: str, message: str, api_key: Optional[str] = None, **kwargs: Any
):
    """
    Send a notification to a webhook URL with dynamic data.

    Args:
        url (str): Webhook endpoint URL.
        status (str): Status of the flow (e.g., "Running", "Completed", "Failed").
        message (str): Custom message to send.
        api_key (str, optional): API key for authentication. Defaults to None.
        kwargs (dict): Additional data to include in the payload.
    """
    logger = get_run_logger()
    headers = {}
    if api_key:
        headers["Authorization"] = api_key

    payload = {
        "status": status,
        "message": message,
        **kwargs,  # Include additional data from kwargs
    }

    try:
        logger.info(f"Kwargs: {kwargs}")
        response = requests.post(
            url, headers=headers, json=payload, allow_redirects=False
        )
        response.raise_for_status()
        # print(response.json())
        logger.info(f"Webhook notification sent successfully: {response.json()}")
    except requests.RequestException as e:
        logger.info(f"Kwargs: {kwargs}")
        logger.error(f"Failed to send webhook notification: {e}")


@task
def read_file_from_s3(object_name: str, bucket_name: Optional[str] = None):
    logger = get_run_logger()
    filename = Path(object_name).stem
    file_ext = Path(object_name).suffix
    # print(f"Filename: {filename}; FileExt: {file_ext}")
    parent_folder = "/tmp/kb_files"
    fp = f"{parent_folder}/{filename}{file_ext}"

    if not os.path.exists(parent_folder):
        os.makedirs(parent_folder)

    if bucket_name:
        s3_bucket_block.bucket_name = bucket_name

    # Get file size before downloading
    s3_client = s3_bucket_block._get_s3_client()
    try:
        response = s3_client.head_object(
            Bucket=s3_bucket_block.bucket_name, Key=object_name
        )
        file_size = response["ContentLength"]

        if file_size > MAX_FILE_SIZE_PREFECT:
            error_msg = f"File size ({file_size / (1024*1024):.2f}MB) exceeds maximum allowed size ({MAX_FILE_SIZE_PREFECT / (1024*1024)}MB)"
            logger.error(error_msg)
            raise ValueError(error_msg)

    except s3_client.exceptions.ClientError as e:
        error_msg = f"Error checking file size: {str(e)}"
        logger.error(error_msg)
        raise s3_client.exceptions.ClientError(e.response, e.operation_name) from e

    filename = s3_bucket_block.download_object_to_path(
        from_path=object_name, to_path=fp
    )
    return fp


@task
def process_file(fp: str, extraction_library: ExtractionLibrary) -> List[Document]:
    if extraction_library == ExtractionLibrary.LLAMA_PARSER:
        return llama_process_file(fp)
    elif extraction_library == ExtractionLibrary.DOCLING:
        return docling_process_file(fp)


# @task
# def process_url(url: str) -> List[Document]:
#     return core_process_url(url)


@task
def chunk_output(
    output: List[Document], chunking_config: ChunkingConfig
) -> List[Document]:
    splitter_class: TextSplitter = import_class(
        f"langchain.text_splitter.{chunking_config.type}"
    )
    text_splitter = splitter_class(**chunking_config.kwargs)
    chunked_docs = []
    for doc_index, doc in enumerate(output):
        content = doc.page_content
        if not content.strip():
            continue

        # Add page number if available
        page_number = doc.metadata.get("page_number", doc_index)

        chunks = text_splitter.split_text(content)

        for chunk_index, chunk in enumerate(chunks):
            unique_id = str(uuid.uuid4())
            chunked_docs.append(
                Document(
                    page_content=chunk,
                    metadata={
                        "source": doc.metadata.get("source", "Unknown"),
                        "last_modified": doc.metadata.get("last_modified", "Unknown"),
                        "filetype": doc.metadata.get("filetype", "Unknown"),
                        "languages": doc.metadata.get("languages", ["Unknown"]),
                        "page_number": page_number,
                        "file_directory": doc.metadata.get("file_directory", "Unknown"),
                        "filename": doc.metadata.get("filename", "Unknown"),
                        "unique_id": unique_id,
                    },
                )
            )

    return chunked_docs


@task
def load_into_vectordb(
    chunked_output: List[Document],
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
) -> None:

    if embedding_config.type == "AzureOpenAIEmbeddings":
        from langchain_openai import AzureOpenAIEmbeddings

        embedding = AzureOpenAIEmbeddings(
            openai_api_key=embedding_config.kwargs.get("openai_api_key"),
            azure_deployment=embedding_config.kwargs.get("azure_deployment"),
            azure_endpoint=embedding_config.kwargs.get("azure_endpoint"),
            openai_api_version=embedding_config.kwargs.get("openai_api_version"),
            chunk_size=2048,
        )

    else:
        embedding_class = import_class(
            f"langchain_community.embeddings.{embedding_config.type}"
        )
        embedding = embedding_class(**embedding_config.kwargs)

    vectorstore_class = import_class(
        f"langchain_community.vectorstores.{retriever_config.type}"
    )
    vectorstore_class.from_documents(
        documents=chunked_output, embedding=embedding, **retriever_config.kwargs
    )


@flow
def execute_data_pipeline(
    chunking_config: ChunkingConfig,
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    webhook_config: Dict[str, Any],
    sources: List[Dict[str, Any]],
):
    webhook_url = webhook_config.get("callback_url")
    webhook_api_key = webhook_config.get("api_key")

    for source in sources:
        source_kwargs = source.get("kwargs", {})
        try:
            source_type = source.get("type")
            source_name = source.get("source_name")
            send_webhook_notification(
                webhook_url,
                "Processing",
                f"Pipeline execution started for {source_type}: {source_name}.",
                webhook_api_key,
                **source_kwargs,
            )

            extracted_data = None

            # if source_type == "url":
            #     extracted_data = process_url(url=source_name)
            if source_type == "aws_object":
                fp = read_file_from_s3(
                    object_name=source_name,
                    bucket_name=source_kwargs.get("bucket_name", None),
                )
                extraction_library_str = source_kwargs.get(
                    "extraction_library", "llamaparser"
                )
                extraction_library = ExtractionLibrary(extraction_library_str)
                extracted_data = process_file(
                    fp=fp, extraction_library=extraction_library
                )
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            chunked_output = chunk_output(
                output=extracted_data, chunking_config=chunking_config
            )

            for doc in chunked_output:
                clean_metadata(doc.metadata)

            load_into_vectordb(
                chunked_output=chunked_output,
                retriever_config=retriever_config,
                embedding_config=embedding_config,
            )

            send_webhook_notification(
                webhook_url,
                "Success",
                f"Processed {source_type}: {source_name}",
                webhook_api_key,
                **source_kwargs,
            )

        except Exception as e:
            send_webhook_notification(
                webhook_url,
                "Failed",
                f"Pipeline execution failed: {str(e)}",
                webhook_api_key,
                **source_kwargs,
            )
            raise


if __name__ == "__main__":
    cc = ChunkingConfig(
        type="RecursiveCharacterTextSplitter",
        kwargs={"chunk_size": 4000, "chunk_overlap": 200},
    )

    rc = RetrieverConfig(
        type="Weaviate",
        kwargs={
            "weaviate_url": "https://weaviate.aimarketplace.co",
            "weaviate_api_key": "********************************",
            "index_name": "Test_hello_01_2323",
        },
    )

    ec = EmbeddingConfig(
        type="AzureOpenAIEmbeddings",
        kwargs={
            "openai_api_key": "8VatSchgvZZMw8u62uhKnJgN8M56SciALrbYvLRqDqEMvd3Scj0hJQQJ99BBACYeBjFXJ3w3AAABACOGlV7v",
            "azure_deployment": "text-embedding-3-small",
            "azure_endpoint": "https://tunkeyproduct.openai.azure.com/",
            "openai_api_version": "2023-05-15",
        },
    )

    # ec = EmbeddingConfig(
    #     type="OpenAIEmbeddings",
    #     kwargs={
    #         "api_key": "********************************************************************************************************************************************************************",
    #     }
    # )

    # {"type": "AzureOpenAIEmbeddings", "kwargs": {"openai_api_key": "********************************", "azure_deployment": "text-embed-stack", "azure_endpoint": "https://genaistack.openai.azure.com/", "openai_api_version": "2024-05-13"}}

    webhook_config = {
        "callback_url": "http://127.0.0.1:8000/api/v1/folder/file/update-status",
        "api_key": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.oRUNgBhgNIvp4PRglgbKz48D_P6FcUCujGkfJnWyuz0",
    }

    sources = [
        {
            "type": "aws_object",
            "source_name": "uploads/e9fd57be-d7c4-4e4a-bdb7-51e05f11eb65/table_sample.pdf",
            "kwargs": {"file_id": "s3-chat-with-pdf"},
        },
        # {"type": "aws_object", "source_name": "uploads/230a02ed-c30c-4902-88fb-5b73be932401/3657bb27-e1f1-47a9-af7e-79992937948a.pdf.pdf", "kwargs": {"file_id": "s3-chat-with-pdf"}},
    ]

    execute_data_pipeline(
        chunking_config=cc,
        retriever_config=rc,
        embedding_config=ec,
        webhook_config=webhook_config,
        sources=sources,
    )
    # execute_data_pipeline(
    #     object_name="uploads/230a02ed-c30c-4902-88fb-5b73be932401/3657bb27-e1f1-47a9-af7e-79992937948a.pdf.pdf", chunking_config=cc, retriever_config=rc, embedding_config=ec , webhook_config = webhook_config
    # )
