deployments:
  - name: chat_with_kb_v5
    version: 0.2
    tags:
      - kb-file-url-2.6.1
    description: Data ingestion from URL with self hosted Crawlee API
    entrypoint: prefect/data_ingestion_with_url/src/flow.py:execute_data_pipeline
    work_pool:
      name: knowledge-base
      job_variables:
        image: afwtech/genflow:kb-file-url-2.6.1
        volumes:
         - /tmp/kb_files:/tmp/kb_files
      work_queue_name:
    parameters: {}
    schedules: []
    pull:
      - prefect.deployments.steps.git_clone:
          repository: https://github.com/aiplanethub/CHAT-WITH-KNOWLEDGEBASE.git
          branch: stage
          access_token: ****************************************