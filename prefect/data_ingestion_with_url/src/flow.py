from typing import List, Optional, Dict, Any
from pathlib import Path
from prefect import flow, task
from prefect.logging import get_run_logger
import os
import uuid
import requests

from langchain.schema import Document
from langchain.text_splitter import TextSplitter

from processing.url import core_process_url
from models import ChunkingConfig, RetrieverConfig, EmbeddingConfig
from utils import import_class, clean_metadata



@task
def send_webhook_notification(
    url: str, status: str, message: str, api_key: Optional[str] = None, **kwargs: Any
):
    """
    Send a notification to a webhook URL with dynamic data.

    Args:
        url (str): Webhook endpoint URL.
        status (str): Status of the flow (e.g., "Running", "Completed", "Failed").
        message (str): Custom message to send.
        api_key (str, optional): API key for authentication. Defaults to None.
        kwargs (dict): Additional data to include in the payload.
    """
    logger = get_run_logger()
    headers = {}
    if api_key:
        headers["Authorization"] = api_key

    payload = {
        "status": status,
        "message": message,
        **kwargs,  # Include additional data from kwargs
    }

    try:
        logger.info(f"Kwargs: {kwargs}")
        response = requests.post(
            url, headers=headers, json=payload, allow_redirects=False
        )
        response.raise_for_status()
        print(response.json())
        logger.info(f"Webhook notification sent successfully: {response.json()}")
    except requests.RequestException as e:
        logger.info(f"Kwargs: {kwargs}")
        logger.error(f"Failed to send webhook notification: {e}")


@task
def process_url(
    url: str,
    crawler_type: str = "cheerio",
    max_requests: int = 10,
    crawl_links: bool = True,
    enqueue_strategy: str = "same-domain",
) -> List[Document]:
    """
    Process a URL by sending it to the web crawler API and converting the response into Document objects.

    Args:
        url (str): The URL to crawl
        crawler_type (str): Type of crawler ("cheerio" or "playwright")
        max_requests (int): Maximum number of pages to crawl
        crawl_links (bool): Whether to follow and crawl links
        enqueue_strategy (str): Strategy for enqueueing links ("same-domain" or "same-hostname" or "all")

    Returns:
        List[Document]: List of Document objects containing the scraped content
    """
    logger = get_run_logger()
    try:
        return core_process_url(
            url=url,
            crawler_type=crawler_type,
            max_requests=max_requests,
            crawl_links=crawl_links,
            enqueue_strategy=enqueue_strategy,
        )
    except requests.RequestException as e:
        logger.error(f"Error making request to crawler API: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error processing URL {url}: {str(e)}")
        raise


@task
def chunk_output(
    output: List[Document], chunking_config: ChunkingConfig
) -> List[Document]:
    splitter_class: TextSplitter = import_class(
        f"langchain.text_splitter.{chunking_config.type}"
    )
    text_splitter = splitter_class(**chunking_config.kwargs)
    chunked_docs = []
    for doc_index, doc in enumerate(output):
        content = doc.page_content
        if not content.strip():
            continue

        # Add page number if available
        page_number = doc.metadata.get("page_number", doc_index)

        chunks = text_splitter.split_text(content)

        for chunk_index, chunk in enumerate(chunks):
            unique_id = str(uuid.uuid4())
            chunked_docs.append(
                Document(
                    page_content=chunk,
                    metadata={
                        "source": doc.metadata.get("source", "Unknown"),
                        "last_modified": doc.metadata.get("last_modified", "Unknown"),
                        "filetype": doc.metadata.get("filetype", "Unknown"),
                        "languages": doc.metadata.get("languages", ["Unknown"]),
                        "page_number": page_number,
                        "file_directory": doc.metadata.get("file_directory", "Unknown"),
                        "filename": doc.metadata.get("filename", "Unknown"),
                        "unique_id": unique_id,
                    },
                )
            )

    return chunked_docs


@task
def load_into_vectordb(
    chunked_output: List[Document],
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
) -> None:
    if embedding_config.type == "AzureOpenAIEmbeddings":
        from  langchain_openai import AzureOpenAIEmbeddings
        embedding = AzureOpenAIEmbeddings(
            openai_api_key=embedding_config.kwargs.get("openai_api_key"),
            azure_deployment=embedding_config.kwargs.get("azure_deployment"),
            azure_endpoint=embedding_config.kwargs.get("azure_endpoint"),
            openai_api_version=embedding_config.kwargs.get("openai_api_version"),
            chunk_size=2048,
        )
        
    else:
        embedding_class = import_class(f"langchain_community.embeddings.{embedding_config.type}")
        embedding = embedding_class(**embedding_config.kwargs)

    vectorstore_class = import_class(
        f"langchain_community.vectorstores.{retriever_config.type}"
    )
    vectorstore_class.from_documents(
        documents=chunked_output, embedding=embedding, **retriever_config.kwargs
    )


@flow
def execute_data_pipeline(
    chunking_config: ChunkingConfig,
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    webhook_config: Dict[str, Any],
    sources: List[Dict[str, Any]],
):
    webhook_url = webhook_config.get("callback_url")
    webhook_api_key = webhook_config.get("api_key")

    for source in sources:
        source_kwargs = source.get("kwargs", {})
        try:
            source_type = source.get("type")
            source_name = source.get("source_name")
            send_webhook_notification(
                webhook_url,
                "Processing",
                f"Pipeline execution started for {source_type}: {source_name}.",
                webhook_api_key,
                **source_kwargs,
            )

            extracted_data = process_url(
                url=source_name,
                crawler_type=source_kwargs.get("crawler_type", "cheerio"),
                max_requests=source_kwargs.get("max_requests", 10),
                crawl_links=source_kwargs.get("crawl_links", True),
                enqueue_strategy=source_kwargs.get(
                    "enqueue_strategy", "same-domain"
                ),
            )

            chunked_output = chunk_output(
                output=extracted_data, chunking_config=chunking_config
            )

            for doc in chunked_output:
                clean_metadata(doc.metadata)

            load_into_vectordb(
                chunked_output=chunked_output,
                retriever_config=retriever_config,
                embedding_config=embedding_config,
            )

            send_webhook_notification(
                webhook_url,
                "Success",
                f"Processed {source_type}: {source_name}",
                webhook_api_key,
                **source_kwargs,
            )

        except Exception as e:
            send_webhook_notification(
                webhook_url,
                "Failed",
                f"Pipeline execution failed: {str(e)}",
                webhook_api_key,
                **source_kwargs,
            )
            raise


if __name__ == "__main__":

    # Your existing pipeline code can remain below
    cc = ChunkingConfig(
        type="RecursiveCharacterTextSplitter",
        kwargs={"chunk_size": 4000, "chunk_overlap": 200},
    )

    rc = RetrieverConfig(
        type="Weaviate",
        kwargs={
            "weaviate_url": "https://weaviate.aimarketplace.co",
            "weaviate_api_key": "********************************",
            "index_name": "Test_hello_01asdasd",
        },
    )

    ec = EmbeddingConfig(
        type="AzureOpenAIEmbeddings",
        kwargs={
            "openai_api_key": "8VatSchgvZZMw8u62uhKnJgN8M56SciALrbYvLRqDqEMvd3Scj0hJQQJ99BBACYeBjFXJ3w3AAABACOGlV7v",
            "azure_deployment": "text-embedding-3-small", 
            "azure_endpoint": "https://tunkeyproduct.openai.azure.com/", 
            "openai_api_version": "2023-05-15"
        },
    )

    webhook_config = {
        "callback_url": "http://127.0.0.1:8000/api/v1/folder/file/update-status",
        "api_key": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.oRUNgBhgNIvp4PRglgbKz48D_P6FcUCujGkfJnWyuz0",
    }

    sources = [
        {
            "type": "url",
            "source_name": "https://quotes.toscrape.com/",
            "kwargs": {
                "crawler_type": "cheerio",
                "crawl_links": True,
                "enqueue_strategy": "same-domain",
            },
        },
    ]

    execute_data_pipeline(
        chunking_config=cc,
        retriever_config=rc,
        embedding_config=ec,
        webhook_config=webhook_config,
        sources=sources,
    )
    # execute_data_pipeline(
    #     object_name="uploads/230a02ed-c30c-4902-88fb-5b73be932401/3657bb27-e1f1-47a9-af7e-79992937948a.pdf.pdf", chunking_config=cc, retriever_config=rc, embedding_config=ec , webhook_config = webhook_config
    # )
