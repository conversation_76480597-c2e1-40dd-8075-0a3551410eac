from typing import List
import requests
from langchain.schema import Document
from prefect.logging import get_run_logger


def core_process_url(
    url: str,
    crawler_type: str = "cheerio",
    max_requests: int = 10,
    crawl_links: bool = True,
    enqueue_strategy: str = "same-domain",
) -> List[Document]:
    """
    Core function to process a URL by sending it to the web crawler API and converting the response into Document objects.

    Args:
        url (str): The URL to crawl
        crawler_type (str): Type of crawler ("cheerio" or "playwright")
        max_requests (int): Maximum number of pages to crawl
        crawl_links (bool): Whether to follow and crawl links
        enqueue_strategy (str): Strategy for enqueueing links ("same-domain" or "same-hostname" or "all")

    Returns:
        List[Document]: List of Document objects containing the scraped content
    """
    logger = get_run_logger()

    # API configuration
    API_ENDPOINT = (
        "https://fc62noxtye.execute-api.eu-west-1.amazonaws.com/default/web-crawler"
    )
    API_KEY = "0R2fRwPKE6LEqqwEWmaI7nLFrg5BRr961xnNdpXh"

    # Prepare the request payload
    payload = {
        "crawlerType": crawler_type,
        "startUrls": [url],
        "maxRequests": max_requests,
        "crawlLinks": crawl_links,
        "enqueueStrategy": enqueue_strategy,
    }

    # Prepare headers
    headers = {"Content-Type": "application/json", "x-api-key": API_KEY}

    # Make the request to the crawler API
    logger.info(f"Sending request to crawler API for URL: {url}")
    response = requests.post(API_ENDPOINT, headers=headers, json=payload)
    response.raise_for_status()

    # Parse the response
    data = response.json()
    documents = []

    # Process each scraped page
    for item in data.get("items", []):
        # Create a Document object for each page
        doc = Document(
            page_content=item.get("body", ""),  # Markdown content
            metadata={
                "source": item.get("url"),
                "filename": item.get("title"),
                "filetype": "markdown",
                "languages": ["Unknown"],
                "last_modified": "Unknown",
                "page_number": 1,
                "file_directory": "Unknown",
                "unique_id": "Unknown",
            },
        )
        documents.append(doc)

    logger.info(f"Successfully processed {len(documents)} pages from {url}")
    return documents
