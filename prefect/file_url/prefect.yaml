deployments:
  - name: chat_with_kb
    version: 0.2
    tags:
      - file_url-0.2
    description: file_url loader
    entrypoint: prefect/file_url/src/flow.py:execute_data_pipeline
    work_pool:
      name: chat-with-kb
      job_variables:
        image: afwtech/genflow:kb-file-url-0.2
        volumes:
         - /tmp/kb_files:/tmp/kb_files
      work_queue_name:
    parameters: {}
    schedules: []
    pull:
      - prefect.deployments.steps.git_clone:
          repository: https://github.com/aiplanethub/CHAT-WITH-KNOWLEDGEBASE.git
          branch: OAGIPLAT-479
          access_token: ****************************************
