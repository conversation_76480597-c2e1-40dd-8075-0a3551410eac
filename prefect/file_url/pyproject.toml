[tool.poetry]
name = "file-url"
version = "0.1.0"
description = ""
authors = ["aryan-aiplanet <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9,<3.11"
prefect = "^2.17.1"
unstructured = {version = "^0.12.5", extras = ["all-docs"]}
weaviate-client = "3.*"
langchain = "~0.2.5"
langchain-community = "~0.2.5"
langchain-core = "~0.2.9"
prefect-aws="~0.4.17"
requests = "2.31"
openai = "^1.8.0"



[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
