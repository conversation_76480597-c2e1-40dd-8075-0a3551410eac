from typing import List, Optional, Dict, Any
from pathlib import Path
import os
import uuid
import requests
from langchain.schema import Document
from langchain.text_splitter import TextSplitter
from processing.file import process_file as core_process_file
from processing.url import process_url as core_process_url
from models import ChunkingConfig, RetrieverConfig, EmbeddingConfig
from utils import import_class, clean_metadata
import boto3  # Required for AWS S3 operations
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Hardcoded AWS credentials
# AWS_ACCESS_KEY_ID = "your-access-key-id"
# AWS_SECRET_ACCESS_KEY = "your-secret-access-key"
# AWS_REGION = "your-region"

AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "iWa6KxYvKac2uMoipJoSMN/YVXcWC/HNf+JrMg7f"
AWS_REGION="eu-west-1"
AWS_BUCKET_NAME="chatwithknowledgebaseprod"
# Initialize S3 client
s3_client = boto3.client(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name=AWS_REGION,
)


def send_webhook_notification(url: str, status: str, message: str, api_key: Optional[str] = None, **kwargs: Any):
    headers = {}
    if api_key:
        headers["Authorization"] = api_key

    payload = {
        "status": status,
        "message": message,
        **kwargs,
    }

    try:
        response = requests.post(url, headers=headers, json=payload, allow_redirects=False)
        response.raise_for_status()
        logger.info(f"Webhook notification sent successfully: {response.json()}")
    except requests.RequestException as e:
        logger.info(f"Failed to send webhook notification: {e}")


def read_file_from_s3(object_name: str, bucket_name: Optional[str] = None) -> str:
    filename = Path(object_name).stem
    file_ext = Path(object_name).suffix
    parent_folder = "/tmp/kb_files"
    fp = f"{parent_folder}/{filename}{file_ext}"

    if not os.path.exists(parent_folder):
        os.makedirs(parent_folder)
    
    bucket_name = AWS_BUCKET_NAME

    try:
        s3_client.download_file(bucket_name, object_name, fp)
    except Exception as e:
        logger.info(f"Error downloading file from S3: {e}")
        raise

    return fp


def process_file(fp: str) -> List[Document]:
    return core_process_file(fp)


def process_url(url: str) -> List[Document]:
    return core_process_url(url)


def chunk_output(output: List[Document], chunking_config: ChunkingConfig) -> List[Document]:
    splitter_class: TextSplitter = import_class(f"langchain.text_splitter.{chunking_config.type}")
    text_splitter = splitter_class(**chunking_config.kwargs)
    chunked_docs = []
    for doc_index, doc in enumerate(output):
        content = doc.page_content
        if not content.strip():
            continue

        page_number = doc.metadata.get("page_number", doc_index)
        chunks = text_splitter.split_text(content)

        for chunk_index, chunk in enumerate(chunks):
            unique_id = str(uuid.uuid4())
            chunked_docs.append(Document(
                page_content=chunk,
                metadata={
                    "source": doc.metadata.get("source", "Unknown"),
                    "last_modified": doc.metadata.get("last_modified", "Unknown"),
                    "filetype": doc.metadata.get("filetype", "Unknown"),
                    "languages": doc.metadata.get("languages", ["Unknown"]),
                    "page_number": page_number,
                    "file_directory": doc.metadata.get("file_directory", "Unknown"),
                    "filename": doc.metadata.get("filename", "Unknown"),
                    "unique_id": unique_id,
                }
            ))

    return chunked_docs


def load_into_vectordb(
    chunked_output: List[Document],
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
) -> None:
    embedding_class = import_class(f"langchain_community.embeddings.{embedding_config.type}")
    embedding = embedding_class(**embedding_config.kwargs)

    vectorstore_class = import_class(f"langchain_community.vectorstores.{retriever_config.type}")
    vectorstore_class.from_documents(documents=chunked_output, embedding=embedding, **retriever_config.kwargs)


def execute_data_pipeline(
    chunking_config: ChunkingConfig,
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    webhook_config: Dict[str, Any],
    sources: List[Dict[str, Any]],
):
    webhook_url = webhook_config.get("callback_url")
    webhook_api_key = webhook_config.get("api_key")

    for source in sources:
        source_kwargs = source.get("kwargs", {})
        try:
            source_type = source.get("type")
            source_name = source.get("source_name")
            send_webhook_notification(
                webhook_url, "Processing", f"Pipeline execution started for {source_type}: {source_name}.", webhook_api_key, **source_kwargs
            )

            extracted_data = None

            if source_type == "url":
                extracted_data = process_url(url=source_name)
            elif source_type == "aws_object":
                fp = read_file_from_s3(object_name=source_name, bucket_name=source_kwargs.get("bucket_name", None))
                extracted_data = process_file(fp=fp)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            chunked_output = chunk_output(output=extracted_data, chunking_config=chunking_config)

            for doc in chunked_output:
                clean_metadata(doc.metadata)

            load_into_vectordb(
                chunked_output=chunked_output,
                retriever_config=retriever_config,
                embedding_config=embedding_config,
            )

            send_webhook_notification(
                webhook_url, "Success", f"Processed {source_type}: {source_name}", webhook_api_key, **source_kwargs
            )

        except Exception as e:
            send_webhook_notification(
                webhook_url, "Failed", f"Pipeline execution failed: {str(e)}", webhook_api_key, **source_kwargs
            )
            raise


if __name__ == "__main__":
    cc = ChunkingConfig(type="RecursiveCharacterTextSplitter", kwargs={"chunk_size": 4000, "chunk_overlap": 200})

    rc = RetrieverConfig(
        type="Weaviate",
        kwargs={
            "weaviate_url": "https://weaviate.aimarketplace.co",
            "weaviate_api_key": "5d92168b96ea4b9bb553b02cd14157b7",
            "index_name": "Test_hello_01",
        },
    )

    ec = EmbeddingConfig(
        type="HuggingFaceInferenceAPIEmbeddings",
        kwargs={
            "model_name": "sentence-transformers/all-MiniLM-L6-v2",
            "api_key": "*************************************",
        },
    )

    webhook_config = {
        "callback_url": "http://127.0.0.1:8000/api/v1/folder/file/update-status",
        "api_key": "Bearer your-webhook-api-key",
    }

    sources = [
        {"type": "aws_object", "source_name": "rfp_agent/ba92e1b0-a0fc-4eb2-91d3-66ee2ab66457/Learning Hub RFP (2).pdf", "kwargs": {"bucket_name": "your-bucket-name"}},
    ]

    execute_data_pipeline(
        chunking_config=cc,
        retriever_config=rc,
        embedding_config=ec,
        webhook_config=webhook_config,
        sources=sources,
    )
