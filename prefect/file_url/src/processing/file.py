from langchain_community.document_loaders import (
    UnstructuredCSVLoader,
    UnstructuredEmailLoader,
    UnstructuredPDFLoader,
    UnstructuredXMLLoader,
    UnstructuredExcelLoader,
    UnstructuredXMLLoader,
    UnstructuredExcelLoader,
    UnstructuredEPubLoader,
    UnstructuredHTMLLoader,
    UnstructuredImageLoader,
    UnstructuredMarkdownLoader,
    UnstructuredPowerPointLoader,
    UnstructuredRTFLoader,
    UnstructuredOrgModeLoader,
    UnstructuredRSTLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredTSVLoader,
    UnstructuredODTLoader,
    UnstructuredFileLoader,
)


EXTENSION_TO_LOADER_MAP = {
    "csv": {"loader_class": UnstructuredCSVLoader, "kwargs": {"mode": "elements"}},
    "email": {"loader_class": UnstructuredEmailLoader, "kwargs": {"mode": "elements"}},
    "eml": {"loader_class": UnstructuredEmailLoader, "kwargs": {"mode": "elements"}},
    "pdf": {"loader_class": UnstructuredPDFLoader, "kwargs": {"mode": "elements", "strategy": "hi_res"}},
    "xml": {"loader_class": UnstructuredXMLLoader, "kwargs": {"mode": "elements"}},
    "xls": {"loader_class": UnstructuredExcelLoader, "kwargs": {"mode": "elements"}},
    "xlsx": {"loader_class": UnstructuredExcelLoader, "kwargs": {"mode": "elements"}},
    "epub": {"loader_class": UnstructuredEPubLoader, "kwargs": {"mode": "elements"}},
    "html": {"loader_class": UnstructuredHTMLLoader, "kwargs": {"mode": "elements"}},
    "jpg": {"loader_class": UnstructuredImageLoader, "kwargs": {"mode": "elements"}},
    "jpeg": {"loader_class": UnstructuredImageLoader, "kwargs": {"mode": "elements"}},
    "png": {"loader_class": UnstructuredImageLoader, "kwargs": {"mode": "elements"}},
    "md": {"loader_class": UnstructuredMarkdownLoader, "kwargs": {"mode": "elements"}},
    "doc": {"loader_class": UnstructuredWordDocumentLoader, "kwargs": {"mode": "elements"}},
    "docx": {"loader_class": UnstructuredWordDocumentLoader, "kwargs": {"mode": "elements"}},
    "ppt": {"loader_class": UnstructuredPowerPointLoader, "kwargs": {"mode": "elements"}},
    "pptx": {"loader_class": UnstructuredPowerPointLoader, "kwargs": {"mode": "elements"}},
    "rtf": {"loader_class": UnstructuredRTFLoader, "kwargs": {"mode": "elements"}},
    "org": {"loader_class": UnstructuredOrgModeLoader, "kwargs": {"mode": "elements"}},
    "rst": {"loader_class": UnstructuredRSTLoader, "kwargs": {"mode": "elements"}},
    "tsv": {"loader_class": UnstructuredTSVLoader, "kwargs": {"mode": "elements"}},
    "odt": {"loader_class": UnstructuredODTLoader, "kwargs": {"mode": "elements"}},
    "*": {"loader_class": UnstructuredFileLoader, "kwargs": {"mode": "elements"}},
}


def process_file(file_path: str):
    """
    Process a file.
    """
    extension = file_path.split(".")[-1]
    loader_map = EXTENSION_TO_LOADER_MAP.get(extension, EXTENSION_TO_LOADER_MAP["*"])
    loader = loader_map["loader_class"](file_path, **loader_map["kwargs"])
    return loader.load()
