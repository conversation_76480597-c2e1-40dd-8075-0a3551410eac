import pymupdf4llm
from langchain.schema import Document
from pathlib import Path

def extract_documents_from_pdf(file_path: str):
    # Validate file existence
    input_path = Path(file_path)
    if not input_path.exists():
        raise FileNotFoundError(f"The file {file_path} does not exist.")
    
    # Extract content and metadata
    try:
        page_chunks = pymupdf4llm.to_markdown(file_path, page_chunks=True)
    except Exception as e:
        raise RuntimeError(f"Failed to process PDF: {e}")
    
    document_list = []

    # Process each page's content and metadata
    for page_data in page_chunks:
        page_number = page_data['metadata'].get('page', 'Unknown')
        markdown_content = page_data.get('text', '')
        
        # Create a Document object
        document = Document(
            page_content=markdown_content,
            metadata={
                "source": str(input_path),
                "page_number": page_number,
                "filetype": "PDF",
                "file_directory": str(input_path.parent),
                "filename": input_path.name,
                # Include additional metadata as needed
            },
        )
        document_list.append(document)
    
    return document_list