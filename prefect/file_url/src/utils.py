from importlib import import_module
from typing import Any, Dict, List


def import_class(class_path: str) -> Any:
    """Import class from class path"""
    module_path, class_name = class_path.rsplit(".", 1)
    module = import_module(module_path)
    return getattr(module, class_name)


DROP_KEYS = ["coordinates"]


def clean_metadata(metadata: Dict):
    for k in DROP_KEYS:
        if k in metadata:
            del metadata[k]
    return metadata
