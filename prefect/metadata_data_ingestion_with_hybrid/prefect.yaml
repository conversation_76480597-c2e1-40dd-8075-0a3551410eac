deployments:
  - name: metadata_data_ingestion_with_hybrid
    version: 0.2
    tags:
      - kb-file-url-2.6.1
    description: Data ingestion using Hybrid approach by adding metadata
    entrypoint: prefect/metadata_data_ingestion_with_hybrid/src/filter_flow.py:execute_data_pipeline
    work_pool:
      name: knowledge-base
      job_variables:
        image: afwtech/genflow:kb-file-url-2.6.1
        volumes:
          - /tmp/kb_files:/tmp/kb_files
      work_queue_name:
    parameters: {}
    schedules: []
    pull:
      - prefect.deployments.steps.git_clone:
          repository: https://github.com/aiplanethub/CHAT-WITH-KNOWLEDGEBASE.git
          branch: stage
          access_token: ****************************************
