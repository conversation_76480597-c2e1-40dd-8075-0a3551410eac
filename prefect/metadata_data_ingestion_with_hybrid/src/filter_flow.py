from typing import List, Optional, Dict, Any
from pathlib import Path
from prefect import flow, task
from prefect.logging import get_run_logger
from prefect_aws.s3 import S3Bucket
import os
import uuid
import requests
from datetime import datetime
import json
import asyncio
from langchain.schema import Document
from langchain.text_splitter import TextSplitter
import weaviate
from processing.docling_call import process_pdf as docling_process_file
from processing.llama_call import process_pdf as llama_process_file
from models import ChunkingConfig, RetrieverConfig, EmbeddingConfig, ExtractionLibrary, LLMConfig
from utils import import_class, clean_metadata
from langchain_openai import AzureChatOpenAI
from prefect.blocks.system import String

s3_bucket_block: S3Bucket = S3Bucket.load("s3-chat-with-pdf-v3")
api_key = String.load("azure-openai-api-key")
endpoint_url = String.load("azure-openai-endpoint-url")

@task
def send_webhook_notification(
    url: str,
    status: str,
    message: str,
    api_key: Optional[str] = None,
    **kwargs: Any
):
    """
    Send a notification to a webhook URL with dynamic data.
    Args:
        url (str): Webhook endpoint URL.
        status (str): Status of the flow (e.g., "Running", "Completed", "Failed").
        message (str): Custom message to send.
        api_key (str, optional): API key for authentication. Defaults to None.
        kwargs (dict): Additional data to include in the payload.
    """
    logger = get_run_logger()
    headers = {}
    if api_key:
        headers["Authorization"] = api_key
    payload = {
        "status": status,
        "message": message,
        **kwargs,  # Include additional data from kwargs
    }
    try:
        logger.info(f"Kwargs: {kwargs}")
        response = requests.post(url, headers=headers, json=payload, allow_redirects=False)
        response.raise_for_status()
        logger.info(f"Webhook notification sent successfully: {response.json()}")
    except requests.RequestException as e:
        logger.info(f"Kwargs: {kwargs}")
        logger.error(f"Failed to send webhook notification: {e}")

@task
def read_file_from_s3(object_name: str , bucket_name: Optional[str] = None):
    filename = Path(object_name).stem
    file_ext = Path(object_name).suffix
    parent_folder = "/tmp/kb_files"
    fp = f"{parent_folder}/{filename}{file_ext}"
    if not os.path.exists(parent_folder):
        os.makedirs(parent_folder)
    if bucket_name:
        s3_bucket_block.bucket_name = bucket_name

    filename = s3_bucket_block.download_object_to_path(from_path=object_name, to_path=fp)
    return fp

@task
def process_file(fp: str, extraction_library: ExtractionLibrary, fast_mode: bool) -> List[Document]:
    if extraction_library == ExtractionLibrary.LLAMA_PARSER:
        return llama_process_file(fp, fast_mode)
    elif extraction_library == ExtractionLibrary.DOCLING:
        return docling_process_file(fp)

async def generate_document_summary_and_categories_async(documents: List[Document]) -> Dict[str, Any]:
    """Generate both summary and industry categories in a single LLM call - ASYNC VERSION."""
    full_text = "\n\n".join([doc.page_content for doc in documents])
    llm = get_llm()

    prompt_template = """
    Analyze the following document and provide:
    1. A comprehensive summary (300-500 words) that captures key requirements, scope, and objectives
    2. Relevant industry categories and regions (comma-separated list)

    Document:
    {text}

    Format your response as JSON:
    {{
        "summary": "your comprehensive summary here",
        "categories": "Healthcare, Technology, Government, North America"
    }}
    """

    truncated_text = full_text[:6000] if len(full_text) > 6000 else full_text
    response = await llm.ainvoke(prompt_template.format(text=truncated_text))
    
    try:
        result = json.loads(response.content)
        return {
            "summary": result["summary"],
            "categories": [cat.strip() for cat in result["categories"].split(',')]
        }
    except json.JSONDecodeError:
        # Fallback if JSON parsing fails
        return {
            "summary": response.content[:500],
            "categories": ["Unknown"]
        }

@task
async def generate_document_summary_and_categories(documents: List[Document]) -> Dict[str, Any]:
    """Async task wrapper for document summary and categories generation."""
    return await generate_document_summary_and_categories_async(documents)

async def generate_chunk_analysis_async(chunk_text: str) -> Dict[str, Any]:
    """Generate both chunk summary and probable questions in a single LLM call - ASYNC VERSION."""
    llm = get_llm()

    prompt_template = """
    Analyze the following document section and provide:
    1. A concise summary (2-3 sentences) that captures the key points
    2. 3-5 high-quality questions that would help understand the content better

    Section:
    {text}

    Format your response as JSON:
    {{
        "summary": "your 2-3 sentence summary here",
        "questions": ["Question 1?", "Question 2?", "Question 3?"]
    }}
    """

    response = await llm.ainvoke(prompt_template.format(text=chunk_text))
    
    try:
        result = json.loads(response.content)
        return {
            "summary": result["summary"],
            "questions": result["questions"]
        }
    except json.JSONDecodeError:
        return {
            "summary": chunk_text[:200] + "...",
            "questions": ["What are the key points in this section?"]
        }

@task
async def generate_chunk_analysis(chunk_text: str) -> Dict[str, Any]:
    """Async task wrapper for chunk analysis generation."""
    return await generate_chunk_analysis_async(chunk_text)

async def process_single_chunk_async(
    chunk: str, 
    chunk_index: int, 
    doc_metadata: Dict[str, Any], 
    file_id: str, 
    overall_summary: str, 
    industry_categories: List[str]
) -> Document:
    """Process a single chunk with summary and questions generation - ASYNC VERSION."""
    logger = get_run_logger()
    logger.info(f"Processing chunk {chunk_index}...")
    
    unique_id = str(uuid.uuid4())
    chunk_analysis = await generate_chunk_analysis_async(chunk)
    
    return Document(
        page_content=chunk,
        metadata={
            "source": doc_metadata.get("source", "Unknown"),
            "last_modified": doc_metadata.get("last_modified", datetime.now().isoformat()),
            "filetype": doc_metadata.get("filetype", "Unknown"),
            "languages": doc_metadata.get("languages", ["Unknown"]),
            "page_number": doc_metadata.get("page_number", 1),
            "file_directory": doc_metadata.get("file_directory", "Unknown"),
            "filename": doc_metadata.get("filename", "Unknown"),
            "unique_id": unique_id,
            "file_id": file_id,
            "chunk_index": chunk_index,
            "overall_summary": overall_summary,
            "chunk_summary": chunk_analysis["summary"],
            "probable_questions": chunk_analysis["questions"],
            "industry_categories": industry_categories,
        }
    )

@task
async def process_single_chunk(
    chunk: str, 
    chunk_index: int, 
    doc_metadata: Dict[str, Any], 
    file_id: str, 
    overall_summary: str, 
    industry_categories: List[str]
) -> Document:
    """Async task wrapper for single chunk processing."""
    return await process_single_chunk_async(chunk, chunk_index, doc_metadata, file_id, overall_summary, industry_categories)

async def create_probable_questions_async(section_summary: str) -> List[str]:
    """Create probable questions that a user might ask about this section - ASYNC VERSION."""
    llm = get_llm()

    prompt_template = """
    Based on the following document section summary, generate 3-5 high-quality questions that would help understand the content better:

    {summary}

    Generate questions that probe for clarification, identify potential issues, and reveal unstated information.
    Format your response as a list of questions only, one per line, without numbering or bullet points.
    """

    response = await llm.ainvoke(prompt_template.format(summary=section_summary))
    questions = [q.strip() for q in response.content.strip().split('\n') if q.strip()]

    return questions

@task
async def create_probable_questions(section_summary: str) -> List[str]:
    """Async task wrapper for probable questions generation."""
    return await create_probable_questions_async(section_summary)

async def categorize_industry_async(document_summary: str) -> List[str]:
    """Categorize the document by industry and region - ASYNC VERSION."""
    llm = get_llm()

    prompt_template = """
    Based on the following document summary, identify the relevant industries and regions:

    {summary}

    Return a comma-separated list of relevant industries (e.g., Healthcare, Finance, Technology, Government) and regions.
    """

    result = await llm.ainvoke(prompt_template.format(summary=document_summary))
    categories = [category.strip() for category in result.content.split(',')]

    return categories

@task
async def categorize_industry(document_summary: str) -> List[str]:
    """Async task wrapper for industry categorization."""
    return await categorize_industry_async(document_summary)

def get_llm():
    """Helper function to get an LLM instance using the loaded API key and endpoint URL."""
    return AzureChatOpenAI(
        azure_deployment="intern-gpt4",
        api_version="2023-05-15",
        temperature=0.0,
        max_tokens=None,
        api_key = api_key.value,
        azure_endpoint = endpoint_url.value
    )

@task
async def chunk_output(output: List[Document], chunking_config: ChunkingConfig, file_id: str) -> List[Document]:
    """Chunk documents and add enhanced metadata with parallel processing - ASYNC VERSION."""
    logger = get_run_logger()

    logger.info("Generating document summary and categories...")
    doc_analysis = await generate_document_summary_and_categories_async(output)
    overall_summary = doc_analysis["summary"]
    industry_categories = doc_analysis["categories"]
    logger.info(f"Document analysis completed. Categories: {industry_categories}")

    splitter_class: TextSplitter = import_class(f"langchain.text_splitter.{chunking_config.type}")
    text_splitter = splitter_class(**chunking_config.kwargs)

    chunk_tasks = []
    
    for doc_index, doc in enumerate(output):
        content = doc.page_content
        if not content.strip():
            continue

        logger.info(f"Preparing chunks for document {doc_index + 1}/{len(output)}...")
        page_number = doc.metadata.get("page_number", doc_index + 1)
        chunks = text_splitter.split_text(content)
        
        # Create metadata dict for this document
        doc_metadata = {
            "source": doc.metadata.get("source", "Unknown"),
            "last_modified": doc.metadata.get("last_modified", datetime.now().isoformat()),
            "filetype": doc.metadata.get("filetype", "Unknown"),
            "languages": doc.metadata.get("languages", ["Unknown"]),
            "page_number": page_number,
            "file_directory": doc.metadata.get("file_directory", "Unknown"),
            "filename": doc.metadata.get("filename", "Unknown"),
        }

        for chunk_index, chunk in enumerate(chunks):
            chunk_tasks.append(
                process_single_chunk_async(
                    chunk=chunk,
                    chunk_index=chunk_index,
                    doc_metadata=doc_metadata,
                    file_id=file_id,
                    overall_summary=overall_summary,
                    industry_categories=industry_categories
                )
            )

    logger.info(f"Processing {len(chunk_tasks)} chunks in parallel (async)...")
    chunked_docs = await asyncio.gather(*chunk_tasks)
    logger.info(f"Async parallel processing completed. Generated {len(chunked_docs)} chunks.")

    return chunked_docs

@task
def generate_embedding(text:Document , embedding):
    return embedding.embed_query(text.page_content)

@task
def load_into_vectordb(
    chunked_output: List[Document],
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    file_id: str,
) -> None:
    try:

        if embedding_config.type == "AzureOpenAIEmbeddings":
            from langchain_openai import AzureOpenAIEmbeddings
            embedding = AzureOpenAIEmbeddings(
                openai_api_key=embedding_config.kwargs.get("openai_api_key"),
                azure_deployment=embedding_config.kwargs.get("azure_deployment"),
                azure_endpoint=embedding_config.kwargs.get("azure_endpoint"),
                openai_api_version=embedding_config.kwargs.get("openai_api_version"),
                chunk_size=2048,
            )

        else:
            embedding_class = import_class(f"langchain_community.embeddings.{embedding_config.type}")
            embedding = embedding_class(**embedding_config.kwargs)

        index_name = retriever_config.kwargs.get("index_name")
        client = weaviate.Client(
            url=retriever_config.kwargs.get("weaviate_url"),
            auth_client_secret=weaviate.AuthApiKey(api_key=retriever_config.kwargs.get("weaviate_api_key"))
        )

        for chunk in chunked_output:
            embedding_vector = generate_embedding(chunk, embedding)
            data_object = {
                "text": chunk.page_content,
                "metadata": chunk.metadata,
                "file_id": file_id,
                "summary": chunk.metadata.get("overall_summary", ""),
                "category": chunk.metadata.get("industry_categories", [])
            }
            client.data_object.create(
                data_object=data_object,
                class_name=index_name,
                vector=embedding_vector
            )
    except Exception as e:
        raise ValueError(f"Failed to load data into VectorDB: {str(e)}")

@flow
async def execute_data_pipeline(
    chunking_config: ChunkingConfig,
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    webhook_config: Dict[str, Any],
    sources: List[Dict[str, Any]],
):
    webhook_url = webhook_config.get("callback_url")
    webhook_api_key = webhook_config.get("api_key")

    for source in sources:
        source_kwargs = source.get("kwargs", {})
        try:
            source_type = source.get("type")
            source_name = source.get("source_name")
            fast_mode = source_kwargs.get("fast_mode", False)
            file_id = source_kwargs.get("file_id", "")
            send_webhook_notification(
                webhook_url, "Processing", f"Pipeline execution started for {source_type}: {source_name}.", webhook_api_key, **source_kwargs
            )
            extracted_data = None

            if source_type == "aws_object":
                fp = read_file_from_s3(object_name=source_name, bucket_name=source_kwargs.get("bucket_name", None))
                extraction_library_str = source_kwargs.get("extraction_library", "llamaparser")
                extraction_library = ExtractionLibrary(extraction_library_str)
                extracted_data = process_file(fp=fp, extraction_library=extraction_library, fast_mode=fast_mode)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            # ASYNC call to chunk_output
            chunked_output = await chunk_output(
                output=extracted_data,
                chunking_config=chunking_config,
                file_id=file_id
            )

            for doc in chunked_output:
                clean_metadata(doc.metadata)

            load_into_vectordb(
                chunked_output=chunked_output,
                retriever_config=retriever_config,
                embedding_config=embedding_config,
                file_id=file_id
            )

            if webhook_url:
                send_webhook_notification(
                    webhook_url, "Success", f"Processed {source_type}: {source_name}", webhook_api_key, **source_kwargs
                )
        except Exception as e:
            if webhook_url:
                send_webhook_notification(
                    webhook_url, "Failed", f"Pipeline execution failed: {str(e)}", webhook_api_key, **source_kwargs
                )
            raise

if __name__ == "__main__":
    cc = ChunkingConfig(type="RecursiveCharacterTextSplitter", kwargs={"chunk_size": 4000, "chunk_overlap": 200})

    rc = RetrieverConfig(
        type="Weaviate",
        kwargs={
            "weaviate_url": "https://weaviate.aimarketplace.co",
            "weaviate_api_key": "********************************",
            "index_name": "Test_new_rfp_metadata_filter_parallel_2",
        },
    )
    ec = EmbeddingConfig(
        type="AzureOpenAIEmbeddings",
        kwargs={
            "openai_api_key": "8VatSchgvZZMw8u62uhKnJgN8M56SciALrbYvLRqDqEMvd3Scj0hJQQJ99BBACYeBjFXJ3w3AAABACOGlV7v",
            "azure_deployment": "text-embedding-3-small",
            "azure_endpoint": "https://tunkeyproduct.openai.azure.com/",
            "openai_api_version": "2023-05-15"
        }
    )
    webhook_config = {
        "callback_url": "http://127.0.0.1:8000/api/v1/folder/file/update-status",
        "api_key": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.oRUNgBhgNIvp4PRglgbKz48D_P6FcUCujGkfJnWyuz0",
    }
    sources = [
        {"type": "aws_object", "source_name": "rfp_agent/c84ef7ac-4e8a-4a0d-a7fc-0c2cd2dd5def/Microsoft Azure Government Datasheet.PDF", "kwargs": {"file_id": "s3-chat-with-pdf" , "fast_mode": True}},
    ]
    
    asyncio.run(execute_data_pipeline(
        chunking_config=cc,
        retriever_config=rc,
        embedding_config=ec,
        webhook_config=webhook_config,
        sources=sources,
    ))
