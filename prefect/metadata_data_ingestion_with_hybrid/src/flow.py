from typing import List, Optional, Dict, Any
from pathlib import Path
from prefect import flow, task
from prefect.logging import get_run_logger
from prefect_aws.s3 import S3Bucket
import os
import uuid
import requests
from datetime import datetime
import json
from langchain.schema import Document
from langchain.text_splitter import TextSplitter
import weaviate
from processing.docling_call import process_pdf as docling_process_file
from processing.llama_call import process_pdf as llama_process_file
from models import ChunkingConfig, RetrieverConfig, EmbeddingConfig, ExtractionLibrary, LLMConfig
from utils import import_class, clean_metadata
from langchain_openai import AzureChatOpenAI
from prefect.blocks.system import String

s3_bucket_block: S3Bucket = S3Bucket.load("s3-chat-with-pdf-v3")
api_key = String.load("azure-openai-api-key")
endpoint_url = String.load("azure-openai-endpoint-url")

@task
def send_webhook_notification(
    url: str,
    status: str,
    message: str,
    api_key: Optional[str] = None,
    **kwargs: Any
):
    """
    Send a notification to a webhook URL with dynamic data.
    Args:
        url (str): Webhook endpoint URL.
        status (str): Status of the flow (e.g., "Running", "Completed", "Failed").
        message (str): Custom message to send.
        api_key (str, optional): API key for authentication. Defaults to None.
        kwargs (dict): Additional data to include in the payload.
    """
    logger = get_run_logger()
    headers = {}
    if api_key:
        headers["Authorization"] = api_key
    payload = {
        "status": status,
        "message": message,
        **kwargs,  # Include additional data from kwargs
    }
    try:
        logger.info(f"Kwargs: {kwargs}")
        response = requests.post(url, headers=headers, json=payload, allow_redirects=False)
        response.raise_for_status()
        logger.info(f"Webhook notification sent successfully: {response.json()}")
    except requests.RequestException as e:
        logger.info(f"Kwargs: {kwargs}")
        logger.error(f"Failed to send webhook notification: {e}")

@task
def read_file_from_s3(object_name: str , bucket_name: Optional[str] = None):
    filename = Path(object_name).stem
    file_ext = Path(object_name).suffix
    parent_folder = "/tmp/kb_files"
    fp = f"{parent_folder}/{filename}{file_ext}"
    if not os.path.exists(parent_folder):
        os.makedirs(parent_folder)
    if bucket_name:
        s3_bucket_block.bucket_name = bucket_name

    filename = s3_bucket_block.download_object_to_path(from_path=object_name, to_path=fp)
    return fp

@task
def process_file(fp: str, extraction_library: ExtractionLibrary, fast_mode: bool) -> List[Document]:
    if extraction_library == ExtractionLibrary.LLAMA_PARSER:
        return llama_process_file(fp, fast_mode)
    elif extraction_library == ExtractionLibrary.DOCLING:
        return docling_process_file(fp)

@task
def generate_document_summary(documents: List[Document]) -> str:
    """Generate a summary for the entire document using an LLM."""
    full_text = "\n\n".join([doc.page_content for doc in documents])

    llm = get_llm()

    prompt_template = """
    Summarize the following document:

    {text}

    Provide a comprehensive summary that captures the key requirements, scope, and objectives.
    Summary should be between 300-500 words.
    """

    truncated_text = full_text[:6000] if len(full_text) > 6000 else full_text
    summary = llm.invoke(prompt_template.format(text=truncated_text))

    return summary.content

@task
def generate_chunk_summary(chunk_text: str) -> str:
    """Generate a summary for an individual chunk using an LLM."""
    llm = get_llm()

    prompt_template = """
    Summarize the following section of a document:

    {text}

    Provide a concise summary that captures the key points of this section.
    The summary should be 2-3 sentences.
    """

    summary = llm.invoke(prompt_template.format(text=chunk_text))
    return summary.content

@task
def create_probable_questions(section_summary: str) -> List[str]:
    """Create probable questions that a user might ask about this section."""
    llm = get_llm()

    prompt_template = """
    Based on the following document section summary, generate 3-5 high-quality questions that would help understand the content better:

    {summary}

    Generate questions that probe for clarification, identify potential issues, and reveal unstated information.
    Format your response as a list of questions only, one per line, without numbering or bullet points.
    """

    response = llm.invoke(prompt_template.format(summary=section_summary)).content
    questions = [q.strip() for q in response.strip().split('\n') if q.strip()]

    return questions

@task
def categorize_industry(document_summary: str) -> List[str]:
    """Categorize the document by industry and region."""
    llm = get_llm()

    prompt_template = """
    Based on the following document summary, identify the relevant industries and regions:

    {summary}

    Return a comma-separated list of relevant industries (e.g., Healthcare, Finance, Technology, Government) and regions.
    """

    result = llm.invoke(prompt_template.format(summary=document_summary)).content
    categories = [category.strip() for category in result.split(',')]

    return categories

def get_llm():
    """Helper function to get an LLM instance using the loaded API key and endpoint URL."""
    return AzureChatOpenAI(
        azure_deployment="intern-gpt4",
        api_version="2023-05-15",
        temperature=0.0,
        max_tokens=None,
        api_key = api_key.value,
        azure_endpoint = endpoint_url.value
    )

@task
def chunk_output(output: List[Document], chunking_config: ChunkingConfig) -> List[Document]:
    """Chunk documents and add enhanced metadata."""
    logger = get_run_logger()

    logger.info("Generating overall document summary...")
    overall_summary = generate_document_summary(output)
    logger.info("Document summary generated successfully")

    logger.info("Categorizing document by industry...")
    industry_categories = categorize_industry(overall_summary)
    logger.info(f"Industry categories identified: {industry_categories}")

    splitter_class: TextSplitter = import_class(f"langchain.text_splitter.{chunking_config.type}")
    text_splitter = splitter_class(**chunking_config.kwargs)

    chunked_docs = []

    for doc_index, doc in enumerate(output):
        content = doc.page_content
        if not content.strip():
            continue

        logger.info(f"Processing document {doc_index + 1}/{len(output)}...")

        page_number = doc.metadata.get("page_number", doc_index + 1)
        chunks = text_splitter.split_text(content)

        logger.info(f"Generated {len(chunks)} chunks for document {doc_index + 1}")

        for chunk_index, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {chunk_index + 1}/{len(chunks)}...")
            unique_id = str(uuid.uuid4())

            chunk_summary = generate_chunk_summary(chunk)
            questions = create_probable_questions(chunk_summary)

            chunked_docs.append(Document(
                page_content=chunk,
                metadata={
                    "source": doc.metadata.get("source", "Unknown"),
                    "last_modified": doc.metadata.get("last_modified", datetime.now().isoformat()),
                    "filetype": doc.metadata.get("filetype", "Unknown"),
                    "languages": doc.metadata.get("languages", ["Unknown"]),
                    "page_number": page_number,
                    "file_directory": doc.metadata.get("file_directory", "Unknown"),
                    "filename": doc.metadata.get("filename", "Unknown"),
                    "unique_id": unique_id,
                    "chunk_index": chunk_index,
                    "overall_summary": overall_summary,
                    "chunk_summary": chunk_summary,
                    "probable_questions": questions,
                    "industry_categories": industry_categories,
                }
            ))
            logger.info(f"Processed chunk {chunk_index + 1}")

    return chunked_docs

@task
def generate_embedding(text:Document , embedding):
    return embedding.embed_query(text.page_content)

@task
def load_into_vectordb(
    chunked_output: List[Document],
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
) -> None:
    try:

        if embedding_config.type == "AzureOpenAIEmbeddings":
            from langchain_openai import AzureOpenAIEmbeddings
            embedding = AzureOpenAIEmbeddings(
                openai_api_key=embedding_config.kwargs.get("openai_api_key"),
                azure_deployment=embedding_config.kwargs.get("azure_deployment"),
                azure_endpoint=embedding_config.kwargs.get("azure_endpoint"),
                openai_api_version=embedding_config.kwargs.get("openai_api_version"),
                chunk_size=2048,
            )

        else:
            embedding_class = import_class(f"langchain_community.embeddings.{embedding_config.type}")
            embedding = embedding_class(**embedding_config.kwargs)
        index_name = retriever_config.kwargs.get("index_name")
        client = weaviate.Client(
            url=retriever_config.kwargs.get("weaviate_url"),
            auth_client_secret=weaviate.AuthApiKey(api_key=retriever_config.kwargs.get("weaviate_api_key"))
        )
        for chunk in chunked_output:
            embedding_vector = generate_embedding(chunk, embedding)
            data_object = {
                "text": chunk.page_content,
                "metadata": chunk.metadata
            }
            client.data_object.create(
                data_object=data_object,
                class_name=index_name,
                vector=embedding_vector
            )
    except Exception as e:
        raise ValueError(f"Failed to load data into VectorDB: {str(e)}")

@flow
def execute_data_pipeline(
    chunking_config: ChunkingConfig,
    retriever_config: RetrieverConfig,
    embedding_config: EmbeddingConfig,
    webhook_config: Dict[str, Any],
    sources: List[Dict[str, Any]],
):
    webhook_url = webhook_config.get("callback_url")
    webhook_api_key = webhook_config.get("api_key")

    for source in sources:
        source_kwargs = source.get("kwargs", {})
        try:
            source_type = source.get("type")
            source_name = source.get("source_name")
            fast_mode = source_kwargs.get("fast_mode", False)
            send_webhook_notification(
                webhook_url, "Processing", f"Pipeline execution started for {source_type}: {source_name}.", webhook_api_key, **source_kwargs
            )
            extracted_data = None

            if source_type == "aws_object":
                fp = read_file_from_s3(object_name=source_name, bucket_name=source_kwargs.get("bucket_name", None))
                extraction_library_str = source_kwargs.get("extraction_library", "llamaparser")
                extraction_library = ExtractionLibrary(extraction_library_str)
                extracted_data = process_file(fp=fp, extraction_library=extraction_library, fast_mode=fast_mode)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            chunked_output = chunk_output(
                output=extracted_data,
                chunking_config=chunking_config,
            )

            for doc in chunked_output:
                clean_metadata(doc.metadata)

            load_into_vectordb(
                chunked_output=chunked_output,
                retriever_config=retriever_config,
                embedding_config=embedding_config
            )

            if webhook_url:
                send_webhook_notification(
                    webhook_url, "Success", f"Processed {source_type}: {source_name}", webhook_api_key, **source_kwargs
                )
        except Exception as e:
            if webhook_url:
                send_webhook_notification(
                    webhook_url, "Failed", f"Pipeline execution failed: {str(e)}", webhook_api_key, **source_kwargs
                )
            raise

if __name__ == "__main__":
    cc = ChunkingConfig(type="RecursiveCharacterTextSplitter", kwargs={"chunk_size": 4000, "chunk_overlap": 200})

    rc = RetrieverConfig(
        type="Weaviate",
        kwargs={
            "weaviate_url": "https://weaviate.aimarketplace.co",
            "weaviate_api_key": "********************************",
            "index_name": "Test_new_rfp_metadata_2",
        },
    )
    ec = EmbeddingConfig(
        type="AzureOpenAIEmbeddings",
        kwargs={
            "openai_api_key": "8VatSchgvZZMw8u62uhKnJgN8M56SciALrbYvLRqDqEMvd3Scj0hJQQJ99BBACYeBjFXJ3w3AAABACOGlV7v",
            "azure_deployment": "text-embedding-3-small",
            "azure_endpoint": "https://tunkeyproduct.openai.azure.com/",
            "openai_api_version": "2023-05-15"
        }
    )
    webhook_config = {
        "callback_url": "http://127.0.0.1:8000/api/v1/folder/file/update-status",
        "api_key": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.oRUNgBhgNIvp4PRglgbKz48D_P6FcUCujGkfJnWyuz0",
    }
    sources = [
        {"type": "aws_object", "source_name": "rfp_agent/c84ef7ac-4e8a-4a0d-a7fc-0c2cd2dd5def/Microsoft Azure Government Datasheet.PDF", "kwargs": {"file_id": "s3-chat-with-pdf" , "fast_mode": True}},
        # {"type": "aws_object", "source_name": "uploads/230a02ed-c30c-4902-88fb-5b73be932401/3657bb27-e1f1-47a9-af7e-79992937948a.pdf.pdf", "kwargs": {"file_id": "s3-chat-with-pdf"}},
    ]
    execute_data_pipeline(
        chunking_config=cc,
        retriever_config=rc,
        embedding_config=ec,
        webhook_config=webhook_config,
        sources=sources,
    )
