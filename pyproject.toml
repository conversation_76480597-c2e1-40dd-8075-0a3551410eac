[tool.poetry]
name = "pdfchat-backend"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
alembic = "^1.14.0"
psycopg2-binary = "^2.9.10"
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
pyjwt = "^2.10.1"
fastapi = "^0.115.5"
python-dotenv = "^1.0.1"
pydantic = {extras = ["email"], version = "^2.10.3"}
uvicorn = "^0.32.1"
pytest = "^8.3.3"
httpx = "^0.28.0"
boto3 = "^1.35.73"
python-multipart = "^0.0.19"
pydantic-settings = "^2.6.1"
langchain-community = "^0.3.17"
weaviate-client = "==3.*"
tenacity = "^9.0.0"
langchain-openai = "^0.3.6"
google-generativeai = "^0.8.3"
google-auth = "^2.37.0"
itsdangerous = "^2.2.0"
python-slugify = "^8.0.4"
grpcio = "^1.69.0"
gunicorn = "20.1.0"
aioboto3 = "^13.4.0"
pandas = "^2.2.3"
tiktoken = "^0.8.0"
langchain-experimental = "^0.3.4"
matplotlib = "^3.10.1"
tabulate = "^0.9.0"
pypdf2 = "^3.0.1"
python-docx = "^1.2.0"
scikit-learn = "1.7.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"