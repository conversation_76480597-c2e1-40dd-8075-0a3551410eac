from fastapi import APIRouter, HTTPException, Depends, status, Request, BackgroundTasks
from sqlalchemy.orm import Session
from app.database.utils import get_db
from services.auth.auth import (
    verify_password,
    hash_password,
    create_access_token,
    generate_password_reset_token,
    get_email_from_password_reset_token,
    send_reset_email,
)
from app.database.models.user import User
from app.database.models.application_limits import ApplicationLimit
from app.database.models.user_model import UserModel
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from schemas.user import (
    UserCreate,
    UserResponse,
    LoginRequest,
    LoginResponse,
    Token,
    ForgotPassword,
    ResetPassword,
    UserUpdate,
    GoogleLoginRequest,
)
from fastapi.responses import J<PERSON><PERSON>esponse
from datetime import timedelta
from fastapi.responses import JSONResponse
from datetime import timedelta
from uuid import UUID
from app.middlewares.utils import get_user_from_request
from logger.logger import RequestLogger
from app.database.models.folder import Folder

log_route = RequestLogger.log_route
from app.core.config import settings
import httpx
from google.oauth2 import id_token
from google.auth.transport import requests
from uuid import uuid4
from services.email import email_service

router = APIRouter()
GOOGLE_CLIENT_ID = settings.GOOGLE_CLIENT_ID
GOOGLE_SECRET_KEY = settings.GOOGLE_SECRET_KEY


# Register route
@router.post("/register", response_model=Token, status_code=status.HTTP_201_CREATED)
@log_route
async def register_user(
    request: Request,
    user_data: UserCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    """
    Register a new user and return an access token.
    This checks for unique email (excluding deleted users - soft delete).
    """
    # Check if email already exists (considering only active users)
    existing_user_email = (
        db.query(User)
        .filter(User.email == user_data.email, User.is_deleted == False)
        .first()
    )

    if existing_user_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered."
        )

    try:
        # Create a new user
        user = User(
            username=user_data.username,
            email=user_data.email,
            password=hash_password(user_data.password),
            is_verified=True,
        )

        db.add(user)
        db.flush()
        app_limit = ApplicationLimit(
            userid=user.id,
            is_paid=False,
            max_folder=3,
            max_file=3,
            max_tokens=settings.ALLOWED_MAX_TOKENS,
            tokens_left=settings.ALLOWED_MAX_TOKENS,
            last_token_reset=datetime.utcnow(),
        )

        # Create default user model
        user_model = UserModel(
            user_id=user.id,
            model_id="mistral.mistral-large-2402-v1:0"
        )

        db.add(app_limit)
        db.add(user_model)
        db.commit()
        db.refresh(app_limit)
        db.refresh(user_model)

        # Send Email Verification Mail
        origin = request.headers.get("Origin", "")
        email_service.initiate_verification(
            origin=origin,
            email=user.email,
            user_id=str(user.id),
            background_tasks=background_tasks,
        )

        # Generate access token for the newly registered user
        token = create_access_token({"sub": user.email})
        expiry_time = timedelta(minutes=4025)

        # Create the response with the access token and set the cookie
        response = JSONResponse({"access_token": token, "token_type": "bearer"})

        # Configure cookie settings based on environment (HTTP vs HTTPS)
        is_production = settings.PROTOCOL == "https"
        response.set_cookie(
            key="access_token",
            value=token,
            httponly=True,
            samesite="Lax" if not is_production else "None",
            secure=is_production,  # Only secure in HTTPS
            expires=int(expiry_time.total_seconds()),  # Set expiry in seconds
        )

        return response

    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error occurred while creating the user. Please try again.",
        )


# Login route
@router.post("/login", response_model=Token, status_code=status.HTTP_200_OK)
@log_route
async def login_user(user_data: LoginRequest, db: Session = Depends(get_db)):
    """
    Authenticate user and return access token.
    """
    user = (
        db.query(User)
        .filter(User.email == user_data.email, User.is_deleted == False)
        .first()
    )

    if not user or not verify_password(user_data.password, user.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password.",
        )

    token = create_access_token({"sub": user.email})
    response = JSONResponse({"access_token": token, "token_type": "bearer"})
    expiry_time = timedelta(minutes=4025)

    # Configure cookie settings based on environment (HTTP vs HTTPS)
    is_production = settings.PROTOCOL == "https"
    response.set_cookie(
        key="access_token",
        value=token,
        httponly=True,
        samesite="Lax" if not is_production else "None",
        secure=is_production,  # Only secure in HTTPS
        expires=int(expiry_time.total_seconds()),  # Set expiry in seconds
    )
    return response


@router.post("/google/login", status_code=status.HTTP_200_OK)
async def google_login_user(
    data: GoogleLoginRequest,
    db: Session = Depends(get_db),
):
    """
    Receives an ID token ('credential') from Google's client-side script.
    Verifies it, and logs in or registers the user as needed.
    Returns an access token upon success.
    """
    credential = data.credential
    if not credential:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing Google ID token (credential).",
        )

    try:
        id_info = id_token.verify_oauth2_token(
            credential, requests.Request(), GOOGLE_CLIENT_ID
        )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid Google ID token."
        )

    if not id_info.get("email"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No email in token."
        )

    user_email = id_info["email"]
    user_name = id_info.get("name") or user_email
    user_picture = id_info.get("picture")

    user = (
        db.query(User)
        .filter(User.email == user_email, User.is_deleted == False)
        .first()
    )

    if not user:
        try:
            user = User(
                username=user_name,
                email=user_email,
                password=hash_password(str(uuid4())),
                profile_image=user_picture,
                is_verified=True,
            )
            db.add(user)
            db.flush()
            app_limit = ApplicationLimit(
                userid=user.id,
                is_paid=False,
                max_folder=3,
                max_file=3,
                max_tokens=settings.ALLOWED_MAX_TOKENS,
                tokens_left=settings.ALLOWED_MAX_TOKENS,
                last_token_reset=datetime.utcnow(),
            )
            # Create default user model
            user_model = UserModel(
                user_id=user.id,
                model_id="mistral.mistral-large-2402-v1:0"
            )

            db.add(app_limit)
            db.add(user_model)
            db.commit()
            db.refresh(user)
            db.refresh(app_limit)
            db.refresh(user_model)
        except IntegrityError:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating the user.",
            )
    user.is_verified = True
    db.commit()
    db.refresh(user)

    token = create_access_token({"sub": user.email})
    return {
        "access_token": token,
        "token_type": "bearer",
    }


@router.get("/user/me", status_code=status.HTTP_200_OK)
@log_route
async def get_user(
    user: dict = Depends(get_user_from_request), db: Session = Depends(get_db)
):
    return {
        "id": user["id"],
        "username": user["username"],
        "email": user["email"],
        "profile_image": user["profile_image"],
        "created_at": user["created_at"],
        "updated_at": user["updated_at"],
        "last_login_at": user["last_login_at"],
        "is_paid": user["is_paid"],
        "max_folder": user["max_folder"],
        "tokens_left": user["tokens_left"],
        "max_tokens": user["max_tokens"],
        "is_verified": user["is_verified"],
        "max_agents": 100,  #! Hardcoded for now, please change
    }


@router.post("/forgot-password", status_code=status.HTTP_200_OK)
@log_route
async def forgot_password(
    request: Request,
    forgot_password: ForgotPassword,
    db: Session = Depends(get_db),
):
    """Generate a password reset token and send it via email."""
    email = forgot_password.email
    user = db.query(User).filter(User.email == email).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    token = generate_password_reset_token(email)

    # Extract frontend origin from the Origin header
    origin = request.headers.get("Origin")
    if not origin:
        raise HTTPException(status_code=400, detail="Origin header is missing")

    # Construct the reset URL
    reset_url = f"{origin}/reset-password?token={token}"

    # Send email directly
    send_reset_email(user.id, reset_url)

    return {"detail": "We have sent a link to your email to reset your password."}


@router.post("/reset-password", status_code=status.HTTP_200_OK)
@log_route
async def reset_password(reset_password: ResetPassword, db: Session = Depends(get_db)):
    """Reset user password using the provided token."""
    try:
        email = get_email_from_password_reset_token(token=reset_password.token)
        user = db.query(User).filter(User.email == email).first()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        user.password = hash_password(reset_password.new_password)

        try:
            db.commit()
            db.refresh(user)
            return {"detail": "Password has been updated successfully."}
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=500, detail="Error updating password. Please try again."
            )

    except Exception as e:
        raise HTTPException(status_code=400, detail="Invalid token or token expired")


@router.patch("/user", status_code=status.HTTP_200_OK)
@log_route
async def update_user(
    update_data: UserUpdate,
    user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db),
):
    """
    Update user details (username and/or password).
    Requires authentication.
    """
    try:
        username_update = False
        userid = UUID(user["id"])
        db_user = (
            db.query(User).filter(User.id == userid, User.is_deleted == False).first()
        )

        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        if update_data.username == None and update_data.password == None:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="no data to update",
            )

        # Update username if provided
        if update_data.username is not None:
            # Check if new username is already taken
            existing_user = (
                db.query(User)
                .filter(
                    User.username == update_data.username,
                    User.id != userid,
                    User.is_deleted == False,
                )
                .first()
            )

            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken",
                )

            db_user.username = update_data.username
            new_token = create_access_token({"sub": update_data.email})
            username_update = True

        # Update password if provided
        if update_data.password is not None:
            db_user.password = hash_password(update_data.password)

        db.commit()
        db.refresh(db_user)

        if username_update:
            return {
                "detail": "Username updated successfully",
                "access_token": new_token,
            }
        else:
            return {"detail": "User details updated successfully"}

    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error occurred while updating user details",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.delete("/user", status_code=status.HTTP_200_OK)
@log_route
async def delete_user(
    user: dict = Depends(get_user_from_request), db: Session = Depends(get_db)
):
    try:
        userid = UUID(user["id"])
        db_user = db.query(User).filter(User.id == userid).first()

        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Soft delete by setting is_deleted to True
        db_user.is_deleted = True
        db.commit()

        return {"detail": "User deleted successfully (soft delete)"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/send-verification")
@log_route
async def send_verification(
    request: Request,
    background_tasks: BackgroundTasks,
    user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db),
):
    userid = UUID(user["id"])
    try:
        user = (
            db.query(User).filter(User.id == userid, User.is_deleted == False).first()
        )
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        origin = request.headers.get("Origin", "")
        return email_service.initiate_verification(
            origin=origin,
            email=user.email,
            user_id=str(user.id),
            background_tasks=background_tasks,
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.get("/verify-email")
@log_route
async def verify_email(token: str, db: Session = Depends(get_db)):
    """Verify email when user clicks the link"""
    return await email_service.handle_verification(token=token, db=db)
