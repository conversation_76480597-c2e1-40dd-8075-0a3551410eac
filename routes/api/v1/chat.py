import logging
import os
import tempfile
import time
import uuid
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any, Optional
from uuid import UUID

import pandas as pd
import requests
import matplotlib
import matplotlib.pyplot as plt
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.database.models.agentbot import AgentBot
from app.database.models.chats import Chat<PERSON>essage, ChatSession
from app.database.models.folder import Folder
from app.database.utils import get_db
from app.middlewares.utils import get_user_from_request
from app.dependencies.user_context import set_query_context
from logger.logger import RequestLogger
from schemas.chat import QueryRequest
from services.agentbot.s3_upload import S3Service
from services.chat.query_service import AnswerService
from services.data_analysis.data_analysis_agent import DataAnalysisAgent
from services.data_analysis.feedback import FeedbackAgent
from services.pdf_parser.chat import ChatService
from services.pdf_parser.s3_service import GetPresignedUrl
from services.translation.translator_service import create_translator
from sqlalchemy.orm import joinedload
from logger.logger import RequestLogger
from schemas.chat import QueryRequest
from services.agentbot.s3_upload import S3Service
from services.chat.query_service import AnswerService
from services.data_analysis.data_analysis_agent import DataAnalysisAgent
from services.data_analysis.feedback import FeedbackAgent
from services.pdf_parser.chat import ChatService
from services.pdf_parser.s3_service import GetPresignedUrl
from sqlalchemy.orm import joinedload
from app.core.config import settings


# Force headless plotting
import matplotlib

matplotlib.use("Agg")
import matplotlib.pyplot as plt

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

log_route = RequestLogger.log_route

# Chat_Service = ChatService()
router = APIRouter(prefix="/api/v1/agent", tags=["chat"])

# Define persistent folder for exported CSV files and charts
EXPORT_DIR = Path("exports")
EXPORT_DIR.mkdir(exist_ok=True)
CHARTS_DIR = EXPORT_DIR / "charts"
CHARTS_DIR.mkdir(exist_ok=True)


# Class created so that plot upload works with existing s3 upload functions. Do not change!
class FakeUploadFile:
    def __init__(self, filename: str, content_type: str, file_obj: BytesIO):
        self.filename = filename
        self.content_type = content_type
        self.file = file_obj


# Not using right now
# @router.post("/{folder_id}/query")
# @log_route
# async def ongoing_chat(
#     request: QueryRequest,
#     folder_id: str,
#     session_id: str = None,
#     db: Session = Depends(get_db),
#     current_user: dict = Depends(get_user_from_request),
# ):
#     try:
#         user_id = UUID(current_user["id"])
#         user_paid = current_user["is_paid"]
#         print(user_paid)
#     except Exception as e:
#         raise HTTPException(status_code=400, detail=f"Invalid user ID: {e}")

#     chat_service = ChatService(db, user_id, str(request.folder_id))
#     session = chat_service.get_or_create_session(
#         request.session_id, first_query=str(request.query)
#     )
#     past_messages = chat_service.get_past_messages(limit=5)
#     conversation_history = past_messages

#     retriever_results = chat_service.get_relevant_documents(
#         request.query, request.folder_id
#     )
#     relevant_context = "\n\n".join(
#         [
#             f"(PDF: {item['filename']}, Page number: {item['page_number']}, unique_id: {item['unique_id']}): {item['text']}"
#             for item in retriever_results
#         ]
#     )

#     full_context = f"Conversation History:\n{conversation_history}\n\nRelevant Context:\n{relevant_context}"
#     total_token_used = 0

#     answer_service = AnswerService()
#     ai_msg_text = answer_service.generate_answer(query=request.query, user_paid=user_paid, full_context=full_context)
#     print(ai_msg_text)

#     # Ensure ai_msg_text is a string
#     if not isinstance(ai_msg_text, str):
#         ai_msg_text = str(ai_msg_text)

#     json_match = re.search(r"(\{.*\})", ai_msg_text, re.DOTALL)
#     if not json_match:
#         raise ValueError("No valid JSON found in AI response.")

#     json_string = json_match.group(1)
#     response_json = json.loads(json_string)
#     token_left = chat_service.check_and_deduct_tokens(response_json, total_token_used)
#     response_json = restructure_data(response_json, retriever_results)

#     chat_service.log_message("USER", request.query, metadata_texts="user")
#     chat_service.log_message("SYSTEM", response_json, metadata_texts=retriever_results)

#     return {
#         "session_id": str(session.id),
#         "folder_id": str(session.folder_id),
#         "content": response_json,
#         "token_left": token_left,
#         "chat_name": (
#             chat_service.get_session_name(request.query)
#             if session.name == "New Chat"
#             else session.name
#         ),
#     }


# needs agent_id (Fix this)
@router.get("/{agent_id}/session")
@log_route
async def get_all_sessions_for_user(
    agent_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
    search: str = None,
    search_enabled: bool = False,
    page: int = 1,
    page_size: int = 10,
):

    try:
        user_id = UUID(current_user["id"])
        agent_id = UUID(agent_id)

        # Base query for fetching sessions
        query = db.query(ChatSession).filter(
            ChatSession.agentid == agent_id, ChatSession.userid == user_id
        )

        # Apply search filter if enabled and search term is provided
        if search_enabled and search:
            query = query.filter(
                ChatSession.name.ilike(f"%{search}%")  # Search within session name
                | ChatMessage.content.ilike(
                    f"%{search}%"
                )  # Search within the last message content
            )
        total_sessions = query.count()
        # Paginate the query results
        sessions = query.offset((page - 1) * page_size).limit(page_size).all()

        chat_details = []
        for session in sessions:
            # Get the last message in the session
            last_message = (
                db.query(ChatMessage)
                .filter(ChatMessage.session_id == session.id)
                .order_by(ChatMessage.created_at.desc())
                .first()
            )

            last_message_content = last_message.content if last_message else None

            # Get the folder (knowledge base) associated with the session
            folder = (
                db.query(Folder)
                .join(AgentBot, AgentBot.folder_id == Folder.id)
                .filter(AgentBot.id == agent_id)
                .first()
            )
            knowledge_base_name = folder.name if folder else "Unknown"

            chat_details.append(
                {
                    "agent_id": str(agent_id),
                    "session_id": str(session.id),
                    "folder_id": str(folder.id),
                    "knowledge_base_name": knowledge_base_name,
                    "last_message": last_message_content,
                    "created_at": session.created_at,
                    "chat_name": session.name,
                }
            )

        # Prepare the response with pagination metadata
        response = {
            "count": total_sessions,
            "prev": (
                f"/user-chats?page={page-1}&page_size={page_size}&search={search}&search_enabled={search_enabled}"
                if page > 1
                else None
            ),
            "next": (
                f"/user-chats?page={page+1}&page_size={page_size}&search={search}&search_enabled={search_enabled}"
                if (page * page_size) < total_sessions
                else None
            ),
            "results": chat_details,
        }

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to fetch chat sessions: {e}"
        )


# Not use it for now
@router.delete("/{session_id}")
@log_route
async def delete_chat(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """
    Delete a chat session and all its messages.

    Args:
        session_id (str): The session ID to delete.
        db (Session): Database session dependency.

    Returns:
        dict: Confirmation message.
    """
    try:
        user_id = UUID(current_user["id"])
        sessionid = UUID(session_id)

        # Validate the session
        session = (
            db.query(ChatSession)
            .filter(ChatSession.id == sessionid, ChatSession.userid == user_id)
            .first()
        )
        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # Delete all messages associated with the session
        db.query(ChatMessage).filter(ChatMessage.session_id == session.id).delete()

        # Delete the session
        db.delete(session)
        db.commit()

        return {"message": f"Chat session {session_id} deleted successfully"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to delete chat session: {e}"
        )


# agent id
@router.get("/{agent_id}/session/{session_id}")
@log_route
async def get_previous_messages(
    agent_id: str,
    session_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    try:
        try:
            user_id = UUID(current_user["id"])
            sessionid = UUID(session_id)
            agent_id = UUID(agent_id)
        except Exception as e:
            raise HTTPException(status_code=406, detail=str(e))

        # Validate the session for the user and folder
        session = (
            db.query(ChatSession)
            .filter(
                ChatSession.id == sessionid,
                ChatSession.userid == user_id,
                ChatSession.agentid == agent_id,
            )
            .first()
        )
        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")
        messages = (
            db.query(ChatMessage)
            .options(joinedload(ChatMessage.plots))
            .filter(ChatMessage.session_id == session.id)
            .order_by(ChatMessage.created_at)
            .all()
        )

      

        return messages

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch messages: {e}")


@router.post("/{agent_id}/session")
async def create_session(
    agent_id: str,
    request: Optional[QueryRequest],
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    try:
        user_id = UUID(current_user["id"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid user ID: {e}")

    chat_service = ChatService(db, user_id, agent_id)
    session = chat_service.get_or_create_session(
        request.session_id or None, first_query=str(request.query)
    )

    return {
        "session_id": str(session.id),
        "chat_name": "New Chat",
    }


@router.post("/{agent_id}/session/{session_id}/query")
async def handle_query(
    request: QueryRequest,
    agent_id: str,
    session_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
    user_model: str = Depends(set_query_context),  # This sets the context and returns model
):
    start_time = time.time()
    try:
        user_id = UUID(current_user["id"])
        user_paid = current_user["is_paid"]
        # print(user_paid)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid user ID: {e}")

    agent = db.query(AgentBot).filter(AgentBot.id == agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    chat_service = ChatService(db, user_id, agent_id)
    conversation_history = chat_service.get_past_messages(session_id, limit=5)
    
    is_bedrock = settings.IS_BEDROCK
        
    if isinstance(is_bedrock, str):
        is_bedrock = is_bedrock.lower() == 'true'
   
    if is_bedrock:
        # Use Bedrock Translation Service
        try:
            translator_service = create_translator()
            translation_result, token_used = await translator_service.translate(
                request.query, 
                agent.instructions,
                conversation_history
            )
            
            response_json = {
                "summary": translation_result,
                "details": []
            }
            retriever_results = []
                    
        except Exception as e:
            logger.error(f"Bedrock translation service failed: {e}")
            raise e
    else:
        # Use regular processing through query service
        answer_service = AnswerService()
        response_json, token_used, retriever_results = await answer_service.process_query(
            request.query, user_paid, agent.folder_id, conversation_history, agent.instructions
        )
       
   
    # token_left = chat_service.check_and_deduct_tokens(response_json, token_used)
    chat_service.log_message("USER", session_id, request.query, metadata_texts="user")
    chat_service.log_message(
        "SYSTEM", session_id, translation_result, metadata_texts=retriever_results
    )

    session = chat_service.get_or_create_session(
        session_id, first_query=str(request.query)
    )

    token_left = 1000

    if session.name == "New Chat":
        try:
            translator_service = create_translator()
            session_name = await translator_service.get_session_name(request.query)
        except ValueError:
            # Fallback session name when translation service not available
            session_name = f"Chat {str(session.id)[:8]}"
        chat_service.update_session_name(session_id, session_name)
    else:
        session_name = session.name
    end_time = time.time()
    logging.info(
        f"Total time taken by query endpoint: {end_time - start_time:.2f} seconds"
    )

    return {
        "session_id": session_id,
        "folder_id": agent.folder_id,
        "content": response_json,
        "token_left": token_left,
        "chat_name": session_name,
        "retriever_results": retriever_results,
    }


@router.post("/{agent_id}/session/{session_id}/da/query")
async def handle_da_query(
    request: QueryRequest,
    agent_id: str,
    session_id: str,
    db: Any = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    # Validate agent and user
    agent_record = db.query(AgentBot).filter(AgentBot.id == agent_id).first()
    if not agent_record:
        raise HTTPException(status_code=404, detail="Agent not found")

    try:
        user_id = UUID(current_user["id"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid user ID: {e}")

    # Setup chat service and session
    chat_service = ChatService(db, user_id, agent_id)
    session = chat_service.get_or_create_session(
        session_id, first_query=str(request.query)
    )

    # Set session name if new chat
    if session.name == "New Chat":
        session_name = chat_service.get_session_name(request.query)
        chat_service.update_session_name(session_id, session_name)
    else:
        session_name = session.name

    # Load data
    try:
        combined_df, csv_files = chat_service.get_csv_data_for_folder(
            agent_record.folder_id
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching CSV data: {e}")

    try:
        da_agent = DataAnalysisAgent(user_id=current_user["id"], db=db)
        da_agent.load_data(combined_df, csv_files)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error loading data: {e}")

    # Get past messages
    past_messages = chat_service.get_past_messages(session_id, limit=10)

    # Count consecutive clarifications
    consecutive_clarifications = 0
    for msg in reversed(past_messages):
        if (
            msg["type"] == "SYSTEM"
            and isinstance(msg.get("metadata_texts", ""), str)
            and "clarification" in msg.get("metadata_texts", "")
        ):
            consecutive_clarifications += 1
        else:
            # Non-clarification message found, stop counting
            break

    # Log the user's question
    chat_service.log_message("USER", session_id, request.query, metadata_texts="user")

    # Check if we need clarification and haven't already asked twice
    if consecutive_clarifications < 2:
        try:
            # Convert messages to simple strings for the clarification function
            message_contents = [
                msg["content"] for msg in past_messages if "content" in msg
            ]

            # Get clarification question
            clarification_result = await da_agent.get_clarification_question(
                request.query, message_contents
            )

            # If clarification is needed, ask for it
            if clarification_result["needs_clarification"]:
                clarification_question = clarification_result["clarification_question"]

                # Log the clarification question
                chat_service.log_message(
                    "SYSTEM",
                    session_id,
                    clarification_question,
                    metadata_texts="clarification",
                    plots=[],
                )

                return {
                    "session_id": session_id,
                    "folder_id": agent_record.folder_id,
                    "clarification_needed": True,
                    "clarification_question": clarification_question,
                    "token_left": current_user["tokens_left"],
                    "chat_name": session_name,
                    "retriever_results": "",
                    "plot_urls": [],
                }
        except Exception as e:
            logger.warning(
                f"Error in clarification process: {e} | method: handle_da_query : warning"
            )
            # Continue to generate a response if clarification fails

    # Generate response if no clarification needed or already asked twice
    try:
        response_text, total_tokens_used, relevant_csv_files = (
            await da_agent.process_question(request.query, past_messages)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {e}")

    # Handle plot generation with names
    plot_urls = []
    charts_folder = Path("exports/charts")
    charts_folder.mkdir(exist_ok=True)
    message_intro = ""

    s3 = S3Service()
    for fig_num in plt.get_fignums():
        file_name = f"{uuid.uuid4()}.png"
        local_plot_path = charts_folder / file_name
        fig = plt.figure(fig_num)
        fig.savefig(local_plot_path, format="png", dpi=300, bbox_inches="tight")
        plt.close(fig)

        # Generate a plot name based on the current query
        plot_index = len(plot_urls) + 1
        plot_name = "Unnamed Plot"

        # Use the agent's plot name if available, otherwise generate a new one
        if hasattr(da_agent, "plots") and plot_index in da_agent.plots:
            plot_name = da_agent.plots[plot_index]
        else:
            # Try to generate a name if the agent doesn't have one
            try:
                plot_name = da_agent.get_plot_name(request.query)
                message_intro = da_agent.get_visualization_intro(request.query)
            except:
                plot_name = f"Plot_{plot_index}"
                message_intro = "Here is a helpful visualization as per your query."

        with open(local_plot_path, "rb") as f:
            buf = BytesIO(f.read())
        fake_file = FakeUploadFile(
            filename=file_name, content_type="image/png", file_obj=buf
        )
        try:
            url = s3.upload_file(fake_file, file_name)
            # Add dict with both URL and title to the plot_urls list
            plot_urls.append({"url": url, "title": plot_name})
        except Exception as e:
            logger.error(f"Error uploading plot: {e} | method: handle_da_query : error")
        try:
            os.remove(local_plot_path)
        except Exception as e:
            logger.error(
                f"Error deleting local plot file: {e} | method: handle_da_query : error"
            )

    # Log the system response
    chat_service.log_message(
        "SYSTEM",
        session_id,
        {
            "response_text": response_text,
            "message_intro": message_intro,
        },
        metadata_texts=[
            f"File paths: {str(combined_df)[:50]}",
        ],
        plots=plot_urls,
    )

    # Deduct tokens
    tokens_used = total_tokens_used if total_tokens_used else 0
    tokens_left = da_agent.check_and_deduct_tokens(tokens_used)

    # Return the response with plot URLs and titles
    return {
        "session_id": session_id,
        "folder_id": agent_record.folder_id,
        "content": {
            "response_text": (
                response_text["output"]
                if isinstance(response_text, dict)
                else response_text
            ),
            "message_intro": message_intro,
        },
        "token_left": tokens_left,
        "chat_name": session_name,
        "retriever_results": relevant_csv_files,
        "plot_urls": plot_urls,
    }
