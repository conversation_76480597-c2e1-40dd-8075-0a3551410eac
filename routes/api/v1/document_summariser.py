import logging
from typing import Optional, List
from uuid import UUID
import os

from fastapi import (
    APIRouter,
    Depends,
    Path,
    HTTPException,
    UploadFile,
    File,
    Form,
)
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.database.utils import get_db
from app.database.models.document_summary import DocumentSummary, SummaryStatus
from app.database.models.document_summary_file import (
    DocumentSummaryFile,
    DocumentSummaryFileStatus,
)
from app.middlewares.utils import get_user_from_request
from app.core.file_utils import file_upload_manager
from app.dependencies.user_context import set_process_context
from services.document_summarizer_service import document_summarizer_service
from schemas.document_summarizer import (
    DocumentSummaryCreateRequest,
    DocumentSummaryUpdateRequest,
    DocumentSummaryResponse,
    DocumentSummaryListResponse,
    DocumentSummaryFileUploadResponse,
    DocumentSummaryFileResponse,
)
from logger.logger import RequestLogger

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
log_route = RequestLogger.log_route

router = APIRouter(prefix="/api/v1/document_summariser", tags=["document", "summary"])


@router.get("/", response_model=DocumentSummaryListResponse)
@log_route
async def get_all_summaries(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
):
    """Get all document summaries for the current user with pagination and search"""
    try:
        user_id = UUID(current_user["id"])

        query = db.query(DocumentSummary).where(
            DocumentSummary.user_id == user_id,
            DocumentSummary.is_deleted == False,
        )

        if search and len(search) > 0:
            query = query.where(DocumentSummary.name.like(f"%{search}%"))

        # Get total count before pagination
        total_count = query.count()

        # Apply pagination
        summaries = query.offset((page - 1) * page_size).limit(page_size).all()

        return DocumentSummaryListResponse(
            results=[
                DocumentSummaryResponse(
                    id=summary.id,
                    name=summary.name,
                    status=summary.status,
                    prompt=summary.prompt,
                    summary=summary.summary,
                    updated_at=summary.updated_at,
                    is_deleted=summary.is_deleted,
                    meta_data=summary.meta_data,
                )
                for summary in summaries
            ],
            count=total_count,
            prev=(
                f"/api/v1/document_summariser?page={page-1}&page_size={page_size}&search={search}"
                if page > 1
                else None
            ),
            next=(
                f"/api/v1/document_summariser?page={page+1}&page_size={page_size}&search={search}"
                if (page * page_size) < total_count
                else None
            ),
        )
    except Exception as e:
        logger.error(f"Error getting summaries: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve summaries")


@router.get("/{summary_id}", response_model=DocumentSummaryResponse)
@log_route
async def get_summary_by_id(
    summary_id: UUID = Path(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Get a specific document summary by ID"""
    try:
        user_id = UUID(current_user["id"])

        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        return DocumentSummaryResponse(
            id=summary.id,
            name=summary.name,
            status=summary.status,
            prompt=summary.prompt,
            summary=summary.summary,
            updated_at=summary.updated_at,
            is_deleted=summary.is_deleted,
            meta_data=summary.meta_data,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting summary {summary_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve summary")


@router.post("/", response_model=DocumentSummaryResponse)
@log_route
async def create_summary(
    summary_data: DocumentSummaryCreateRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Create a new document summary"""
    try:
        user_id = UUID(current_user["id"])

        summary = DocumentSummary(
            name=summary_data.name,
            prompt=summary_data.prompt,
            meta_data=summary_data.meta_data,
            user_id=user_id,
            status=SummaryStatus.PENDING.value,
        )

        db.add(summary)
        db.commit()
        db.refresh(summary)

        return DocumentSummaryResponse(
            id=summary.id,
            name=summary.name,
            status=summary.status,
            prompt=summary.prompt,
            summary=summary.summary,
            updated_at=summary.updated_at,
            is_deleted=summary.is_deleted,
            meta_data=summary.meta_data,
        )
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to create summary")


@router.patch("/{summary_id}", response_model=DocumentSummaryResponse)
@log_route
async def update_summary(
    summary_data: DocumentSummaryUpdateRequest,
    summary_id: UUID = Path(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Update an existing document summary"""
    try:
        user_id = UUID(current_user["id"])

        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        # Update fields if provided
        if summary_data.name is not None:
            summary.name = summary_data.name
        if summary_data.prompt is not None:
            summary.prompt = summary_data.prompt
        if summary_data.meta_data is not None:
            summary.meta_data = summary_data.meta_data

        db.commit()
        db.refresh(summary)

        return DocumentSummaryResponse(
            id=summary.id,
            name=summary.name,
            status=summary.status,
            prompt=summary.prompt,
            summary=summary.summary,
            updated_at=summary.updated_at,
            is_deleted=summary.is_deleted,
            meta_data=summary.meta_data,
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating summary {summary_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update summary")


@router.delete("/{summary_id}")
@log_route
async def delete_summary(
    summary_id: UUID = Path(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Soft delete a document summary"""
    try:
        user_id = UUID(current_user["id"])

        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        # Soft delete
        summary.is_deleted = True
        db.commit()

        return {"message": "Document summary deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting summary {summary_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete summary")


# File upload and management routes


@router.post(
    "/{summary_id}/files/upload", response_model=DocumentSummaryFileUploadResponse
)
@log_route
async def upload_file(
    summary_id: UUID = Path(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Upload a file for document summarization"""
    try:
        user_id = UUID(current_user["id"])

        # Verify summary exists and user has access
        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        # Validate file type
        allowed_extensions = {".pdf", ".docx", ".txt", ".doc"}
        file_extension = (
            os.path.splitext(file.filename)[1].lower() if file.filename else ""
        )

        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File type not supported. Allowed types: {', '.join(allowed_extensions)}",
            )

        # Save file to temporary storage
        file_info = await file_upload_manager.save_uploaded_file(
            file, subdirectory=str(summary_id)
        )

        # Create database record
        summary_file = DocumentSummaryFile(
            name=file_info["original_filename"] or "Unknown",
            type=file_extension[1:] if file_extension else "unknown",
            size=file_info["file_size"],
            path=file_info["file_path"],
            document_summary_id=summary_id,
            status=DocumentSummaryFileStatus.QUEUE.value,
            meta_data={
                "content_type": file_info["content_type"],
                "uploaded_at": file_info["uploaded_at"].isoformat(),
                "saved_filename": file_info["saved_filename"],
            },
        )

        db.add(summary_file)
        db.commit()
        db.refresh(summary_file)

        return DocumentSummaryFileUploadResponse(
            id=summary_file.id,
            name=summary_file.name,
            type=summary_file.type,
            size=summary_file.size,
            status=summary_file.status,
            message="File uploaded successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload file")


@router.get("/{summary_id}/files", response_model=List[DocumentSummaryFileResponse])
@log_route
async def get_summary_files(
    summary_id: UUID = Path(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Get all files for a document summary"""
    try:
        user_id = UUID(current_user["id"])

        # Verify summary exists and user has access
        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        files = (
            db.query(DocumentSummaryFile)
            .where(
                and_(
                    DocumentSummaryFile.document_summary_id == summary_id,
                    DocumentSummaryFile.is_deleted == False,
                )
            )
            .all()
        )

        return [
            DocumentSummaryFileResponse(
                id=file.id,
                name=file.name,
                type=file.type,
                size=file.size,
                path=file.path,
                status=file.status,
                url=file.url,
                meta_data=file.meta_data,
                updated_at=file.updated_at,
                is_deleted=file.is_deleted,
            )
            for file in files
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting files for summary {summary_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve files")


@router.get("/{summary_id}/files/{file_id}", response_model=DocumentSummaryFileResponse)
@log_route
async def get_summary_file(
    summary_id: UUID = Path(...),
    file_id: UUID = Path(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Get a specific file for a document summary"""
    try:
        user_id = UUID(current_user["id"])

        # Verify summary exists and user has access
        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        file = (
            db.query(DocumentSummaryFile)
            .where(
                and_(
                    DocumentSummaryFile.id == file_id,
                    DocumentSummaryFile.document_summary_id == summary_id,
                    DocumentSummaryFile.is_deleted == False,
                )
            )
            .first()
        )

        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        return DocumentSummaryFileResponse(
            id=file.id,
            name=file.name,
            type=file.type,
            size=file.size,
            path=file.path,
            status=file.status,
            url=file.url,
            meta_data=file.meta_data,
            updated_at=file.updated_at,
            is_deleted=file.is_deleted,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file {file_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve file")


@router.delete("/{summary_id}/files/{file_id}")
@log_route
async def delete_summary_file(
    summary_id: UUID = Path(...),
    file_id: UUID = Path(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    """Delete a file from a document summary"""
    try:
        user_id = UUID(current_user["id"])

        # Verify summary exists and user has access
        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        file = (
            db.query(DocumentSummaryFile)
            .where(
                and_(
                    DocumentSummaryFile.id == file_id,
                    DocumentSummaryFile.document_summary_id == summary_id,
                    DocumentSummaryFile.is_deleted == False,
                )
            )
            .first()
        )

        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        # Delete physical file
        if file.path:
            file_upload_manager.delete_file(file.path)

        # Soft delete database record
        file.is_deleted = True
        db.commit()

        return {"message": "File deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting file {file_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete file")


@router.post("/{summary_id}/process")
@log_route
async def process_summary(
    summary_id: UUID = Path(...),
    custom_prompt: str = Form(None),  # Make it optional
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
    important_keywords: str = Form(None),  # Make it optional
    user_model: str = Depends(set_process_context),  # This sets the context and returns model
):
    """Process a document summary with its uploaded files synchronously"""
    try:
        user_id = UUID(current_user["id"])

        # Verify summary exists and user has access
        summary = (
            db.query(DocumentSummary)
            .where(
                and_(
                    DocumentSummary.id == summary_id,
                    DocumentSummary.user_id == user_id,
                    DocumentSummary.is_deleted == False,
                )
            )
            .first()
        )

        if not summary:
            raise HTTPException(status_code=404, detail="Document summary not found")

        # Check if there are files to process
        files = (
            db.query(DocumentSummaryFile)
            .where(
                and_(
                    DocumentSummaryFile.document_summary_id == summary_id,
                    DocumentSummaryFile.is_deleted == False,
                )
            )
            .all()
        )

        if not files:
            raise HTTPException(status_code=400, detail="No files found to process")

        logger.info(f"Default prompt is {summary.prompt}")

        # Use custom prompt if provided, otherwise use stored prompt
        prompt_to_use = custom_prompt if custom_prompt else summary.prompt
        logger.info(f"📝 Using prompt: {prompt_to_use[:100]}...")
        logger.info(f"📁 Processing {len(files)} files")

        # Process the summary synchronously
        logger.info("🔄 Starting document_summarizer_service.process_document_summary...")
        result = await document_summarizer_service.process_document_summary(
            summary=summary, files=files, db=db, custom_prompt=prompt_to_use, important_keywords=important_keywords
        )
        logger.info(f"✅ Document processing completed with status: {result.get('status', 'unknown')}")

        if result["status"] == "completed":
            return {
                "message": "Document summary processed successfully",
                "summary_id": str(summary_id),
                "status": "completed",
                "summary": result.get("summary", ""),
                "files_count": len(files),
                "hyperlinks": result.get("hyperlinks", {}),
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Processing failed: {result.get('error', 'Unknown error')}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing summary {summary_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to process summary: {str(e)}"
        )
