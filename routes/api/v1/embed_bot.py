from fastapi import APIRouter, HTTPException, Depends, UploadFile, Form, Request
from sqlalchemy.orm import Session
from app.database.utils import get_db
from fastapi import File
from app.database.models.folder import Folder
from app.database.models.agentbot import AgentBot
from logger.logger import RequestLogger
from app.middlewares.utils import get_user_from_request
from uuid import UUID
from services.agentbot.s3_upload import S3Service
from typing import Optional, Union
import logging


# service initialization
s3_service = S3Service()
log_route = RequestLogger.log_route
router = APIRouter(prefix="/api/v1/agent", tags=["Embed_Agent"])


@router.post("/", status_code=200)
@log_route
async def creat_agent(
    folder_id: str = Form(...),
    agent_name: str = Form(...),
    authorized_domain: Optional[str] = Form(None),
    color_theme: Optional[str] = Form(None),
    agent_avatar: str = File(...),
    avatar_color: str = File(...),
    is_exposed: Optional[bool] = Form(False),
    current_user: dict = Depends(get_user_from_request),
    instructions: Optional[str] = Form(None),
    db: Session = Depends(get_db),
):
    try:
        user_id = UUID(current_user["id"])
        folderid = UUID(folder_id)
        logging.info(f"Folder ID ahsdhash: {folderid}")
        
        import secrets
        random_folder_name = f"Agent_{agent_name}_{secrets.token_hex(4)}"
        
        new_folder = Folder(
            name=random_folder_name,
            userid=user_id,
            description=f"Auto-created folder for agent {agent_name}"
        )
        db.add(new_folder)
        db.flush()  
        folderid = new_folder.id

        if is_exposed and not authorized_domain:
            raise HTTPException(
                status_code=422,
                detail="Authorized domain is required for exposing agent",
            )

        # Create a new agent
        new_agent = AgentBot(
            agent_name=agent_name,
            user_id=user_id,
            folder_id=folderid,
            agent_avatar=agent_avatar,
            avatar_color=avatar_color,
            authorized_domain=authorized_domain or None,
            color_theme=color_theme or None,
            is_exposed=is_exposed,
            instructions=instructions,
        )
        db.add(new_agent)
        db.commit()
        db.refresh(new_agent)

        # Return the response with agent info
        return {
            "id": str(new_agent.id),
            "agent_name": new_agent.agent_name,
            "agent_avatar": new_agent.agent_avatar,
            "color_theme": new_agent.color_theme,
            "authorized_domain": new_agent.authorized_domain,
            "folder_id": str(new_agent.folder_id),
            "folder_name": "Default",  # Added folder name
            "is_exposed": new_agent.is_exposed,
            "last_updated": new_agent.updated_at,
            "instructions": new_agent.instructions,
        }

    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc
    except Exception as e:
        # Handle other unexpected errors with a 500 status code
        db.rollback()  # Rollback in case of an error

        raise HTTPException(status_code=500, detail=f"Error creating agent: {str(e)}")


@router.get("/all", status_code=200)
@log_route
async def get_all_agents(
    page: int = 1,  # Default to the first page
    page_size: int = 10,  # Default to 10 agents per page
    all: bool = False,  # If True, return all agents without pagination
    search: Optional[str] = None,  # Optional search query
    search_enabled: bool = False,  # If search is enabled
    current_user: dict = Depends(
        get_user_from_request
    ),  # Get the current user from the request
    db: Session = Depends(get_db),
):
    try:
        # Get the user ID from the current user
        user_id = UUID(current_user["id"])

        query = (
            db.query(AgentBot, Folder.name.label("folder_name"))
            .join(Folder, AgentBot.folder_id == Folder.id)
            .filter(AgentBot.user_id == user_id, AgentBot.is_deleted == False)
        )

        # Apply search filter if 'search' parameter is provided and search_enabled is True
        if search_enabled and search:
            query = query.filter(AgentBot.agent_name.ilike(f"%{search}%"))

        if all:
            results = query.all()
            return {
                "count": query.count(),
                "prev": None,
                "next": None,
                "results": [
                    {
                        "id": str(result.AgentBot.id),
                        "agent_name": result.AgentBot.agent_name,
                        "agent_avatar": result.AgentBot.agent_avatar,
                        "avatar_color": result.AgentBot.avatar_color,
                        "color_theme": result.AgentBot.color_theme,
                        "authorized_domain": result.AgentBot.authorized_domain,
                        "folder_id": str(result.AgentBot.folder_id),
                        "folder_name": result.folder_name,  # Added folder name
                        "is_exposed": result.AgentBot.is_exposed,
                        "last_updated": result.AgentBot.updated_at,
                    }
                    for result in results
                ],
            }
        # Get total agents count after applying filters
        total_agents = query.count()

        # Paginate the query results
        results = query.offset((page - 1) * page_size).limit(page_size).all()

        response = {
            "count": total_agents,
            "prev": (
                f"/agents/all?page={page-1}&page_size={page_size}&search={search}"
                if page > 1
                else None
            ),
            "next": (
                f"/agents/all?page={page+1}&page_size={page_size}&search={search}"
                if (page * page_size) < total_agents
                else None
            ),
            "results": [
                {
                    "id": str(result.AgentBot.id),
                    "agent_name": result.AgentBot.agent_name,
                    "agent_avatar": result.AgentBot.agent_avatar,
                    "avatar_color": result.AgentBot.avatar_color,
                    "color_theme": result.AgentBot.color_theme,
                    "authorized_domain": result.AgentBot.authorized_domain,
                    "folder_id": str(result.AgentBot.folder_id),
                    "folder_name": result.folder_name,  # Added folder name
                    "is_exposed": result.AgentBot.is_exposed,
                    "last_updated": result.AgentBot.updated_at,
                }
                for result in results
            ],
        }

        return response

    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc
    except Exception as e:

        raise HTTPException(status_code=500, detail=f"Error fetching agents: {str(e)}")


@router.get("/{agent_id}", status_code=200)
@log_route
async def get_agent(
    agent_id: str,
    request: Request,
    current_user: dict = Depends(
        get_user_from_request
    ),  # Get the current user from the request
    db: Session = Depends(get_db),
):
    try:

        user_id = UUID(current_user["id"])
        agent_id = UUID(agent_id)

        # Check if folder exists for the current user

        # Query the agent based on folder_id
        agent = db.query(AgentBot).filter(AgentBot.id == agent_id).first()

        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")

        folder = db.query(Folder).filter(Folder.id == agent.folder_id).first()

        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")

        if folder.userid != user_id:
            raise HTTPException(status_code=403, detail="Not Authorised")

        return {
            "id": str(agent.id),
            "agent_name": agent.agent_name,
            "agent_avatar": agent.agent_avatar,
            "avatar_color": agent.avatar_color,
            "color_theme": agent.color_theme,
            "authorized_domain": agent.authorized_domain,
            "folder_id": str(agent.folder_id),
            "folder_name": folder.name,  # Added folder name
            "is_exposed": agent.is_exposed,
            "last_updated": agent.updated_at,
            "instructions": agent.instructions,
        }
    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc

    except Exception as e:

        raise HTTPException(status_code=500, detail=f"Error fetching agent: {str(e)}")


@router.patch("/{agent_id}", status_code=200)
@log_route
async def update_agent(
    agent_id: str,
    agent_name: Optional[str] = Form(None),
    avatar_color: Optional[str] = Form(None),
    authorized_domain: Optional[str] = Form(None),
    instructions: Optional[str] = Form(None),
    color_theme: Optional[str] = Form(None),
    agent_avatar: Optional[str] = Form(None),  # Accept both UploadFile and str
    folder_id: Optional[str] = Form(None),
    current_user: dict = Depends(get_user_from_request),
    is_exposed: Optional[bool] = Form(None),
    db: Session = Depends(get_db),
):
    # try:
    # Parse UUIDs
    user_id = UUID(current_user["id"])
    agent_id = UUID(agent_id)

    # Check if the agent exists in the folder
    agent_to_update = (
        db.query(AgentBot)
        .filter(AgentBot.id == agent_id, AgentBot.user_id == user_id)
        .first()
    )

    if not agent_to_update:
        raise HTTPException(status_code=404, detail="Agent not found")
    if (
        is_exposed
        and agent_to_update.authorized_domain == None
        and authorized_domain == None
    ):
        raise HTTPException(
            status_code=403,
            detail="Authorized domain is required for exposing agent",
        )

    # Update other properties
    if agent_avatar is not None:
        agent_to_update.agent_avatar = agent_avatar
    if avatar_color is not None:
        agent_to_update.avatar_color = avatar_color
    if agent_name is not None:
        agent_to_update.agent_name = agent_name
    if instructions is not None:
        agent_to_update.instructions = instructions
    if color_theme is not None:
        agent_to_update.color_theme = color_theme
    if authorized_domain is not None:
        agent_to_update.authorized_domain = authorized_domain
    if is_exposed is not None:
        agent_to_update.is_exposed = is_exposed
    if folder_id is not None:
        folder_id = UUID(folder_id)
        agent_to_update.folder_id = folder_id

    folder = db.query(Folder).filter(Folder.id == agent_to_update.folder_id).first()

    try:
        # Save changes to database
        db.commit()
        db.refresh(agent_to_update)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

    return {
        "id": str(agent_to_update.id),
        "agent_name": agent_to_update.agent_name,
        "agent_avatar": agent_to_update.agent_avatar,
        "avatar_color": agent_to_update.avatar_color,
        "color_theme": agent_to_update.color_theme,
        "authorized_domain": agent_to_update.authorized_domain,
        "folder_id": str(agent_to_update.folder_id),
        "folder_name": folder.name,  # Added folder name
        "is_exposed": agent_to_update.is_exposed,
        "last_updated": agent_to_update.updated_at,
    }

    # except HTTPException as http_exc:
    #     raise http_exc
    # except Exception as e:
    #     db.rollback()
    #     print(f"Error updating agent: {e}")
    #     raise HTTPException(status_code=500, detail=f"Error updating agent: {str(e)}")


# in case we need it in future private route
"""
@router.post('/query/{folder_id}', status_code= 200)
@log_route
async def get_response(
    req: Request,
    folder_id: str,
    db: Session = Depends(get_db),
    data: QueryRequest = Depends(rate_limit_dependency),
):
    try:
        folderid = UUID(folder_id)
        agent = db.query(AGENTBOT).filter(AGENTBOT.folder_id == folderid).first()
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        origin = req.headers.get("origin", None)  # Get the Origin header, if present
        if not agent.is_exposed:
            raise HTTPException(
                status_code=400, detail="Agent is not available for public usage."
            )
        if origin:
            print(f"Request coming from domain: {origin}")
        else:
            print("No origin header present")
        # Verify the authorized domain (assuming the authorized domain is a single string for simplicity)
        if agent.authorized_domain != origin:
            raise HTTPException(
                status_code=403, detail="Request's origin domain is not authorized"
            )

        retriever_service = AGENTBOTSERVICE(folder_id=folder_id)
        relevant_content = retriever_service.get_relevant_documents(
            query=data.query, index_name=folder_id
        )
        relevant_context = "\n\n".join(
            [
                f"(PDF: {item['original_file_name']}, Chunk: {item['chunk_index']}): {item['text']}"
                for item in relevant_content
            ]
        )

        answer_service = AnswerService(retriever_service)
        answer = answer_service.generate_answer(False, relevant_context, request)

        json_match = re.search(r"(\{.*\})", answer.ai_msg_text, re.DOTALL)
        if not json_match:
            raise ValueError("No valid JSON found in AI response.")

        json_string = json_match.group(1)
        response_json = json.loads(json_string)
        response_json = restructure_data(response_json, [])
        ai_msg = response_json.get("summary")

        return {"answer": ai_msg}
    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Query failed: {e}")
"""
