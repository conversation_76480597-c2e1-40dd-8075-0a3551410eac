from fastapi import APIRouter, HTTPException, Depends, status, UploadFile, Form, Request
from fastapi import File as Files
import requests
from schemas.pdf_parser import PdfResponse
from schemas.pdf_parser import (
    FolderResponse,
    CreateFolder,
    FileCreate,
    FileResponse,
    PresignedURLResponse,
    FileUploadResponse,
)
from services.pdf_parser.s3_service import S3Handler
from services.pdf_parser.file_upload import FileUploadService
import os
import uuid
from app.database.utils import get_db
from app.database.models.folder import Folder
from app.database.models.files import File, FileStatus
from sqlalchemy.orm import Session
from sqlalchemy import func
from sqlalchemy import and_
from typing import Optional, Dict, Any
from uuid import UUID, uuid4
import os
from uuid import uuid4
from app.core.config import settings
from app.middlewares.utils import get_user_from_request
from pathlib import Path

import json
import time
from logger.logger import RequestLogger
from services.pdf_parser.utils import generate_unique_filename
from app.core.prefect.knowledgebase import trigger_prefect_flow_run
import logging

from services.pdf_parser.s3_service import GetPresignedUrl

log_route = RequestLogger.log_route
aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY
aws_access_key = settings.AWS_ACCESS_KEY_ID
aws_region_name = settings.AWS_DEFAULT_REGION
aws_bucket_name = settings.AWS_BUCKET_NAME
router = APIRouter(prefix="/api/v1/folder", tags=["folder"])

s3_service = S3Handler()
s3_presigned_url = GetPresignedUrl()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@router.post("", status_code=200, response_model=FolderResponse)
@log_route
async def create_folder(
    folder: CreateFolder,
    current_user: dict = Depends(
        get_user_from_request
    ),  # Use authenticate_request to get user details
    db: Session = Depends(get_db),
):
    """
    Creates a new folder for the current authenticated user.

    """
    try:
        # Extract the UUID of the user
        user_id = UUID(current_user["id"])
        folder_query = db.query(Folder).filter(
            Folder.userid == user_id, Folder.is_deleted == False
        )
        folder_count = folder_query.count()
        if folder_count >= current_user["max_folder"]:
            raise HTTPException(
                status_code=400, detail="You have reached the maximum number of folders"
            )

        # Create a new folder object
        new_folder = Folder(
            name=folder.name,
            userid=user_id,  # Use UUID as userid
            description=folder.description,
        )

        # Save the new folder to the database
        db.add(new_folder)
        db.commit()
        db.refresh(new_folder)

        return {
            "id": str(new_folder.id),
            "name": new_folder.name,
            "userid": str(new_folder.userid),  # Convert UUID to string
            "description": new_folder.description,
            "created_at": new_folder.created_at,
            "updated_at": new_folder.updated_at,
        }
    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc

    except Exception as e:
        db.rollback()  # Rollback in case of an error
        raise HTTPException(status_code=500, detail=f"Error creating folder: {str(e)}")


@router.get("", status_code=200)
@log_route
async def show_folders(
    current_user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db),
    page: int = 1,
    page_size: int = 10,
    search: str = None,
    search_enabled: bool = False,
):
    try:
        userid = UUID(current_user["id"])
        if not userid:
            raise HTTPException(status_code=403, detail="Unauthorized")

        # Base query with file count
        query = (
            db.query(Folder, func.count(File.id).label("file_count"))
            .outerjoin(File, and_(Folder.id == File.folderid, File.is_deleted == False))
            .filter(Folder.userid == userid, Folder.is_deleted == False)
            .group_by(Folder.id)
        )

        # Apply search filter if 'search' parameter is provided and search_enabled is True
        if search_enabled and search:
            query = query.filter(Folder.name.ilike(f"%{search}%"))

        # Get total folders count after applying filters
        total_folders = query.count()

        # Paginate the query results
        folders = query.offset((page - 1) * page_size).limit(page_size).all()

        # Build response
        response = {
            "count": total_folders,
            "prev": (
                f"/folders/{userid}?page={page-1}&page_size={page_size}"
                if page > 1
                else None
            ),
            "next": (
                f"/folders/{userid}?page={page+1}&page_size={page_size}"
                if (page * page_size) < total_folders
                else None
            ),
            "results": [
                {
                    "id": str(folder.id),
                    "name": folder.name,
                    "userid": folder.userid,
                    "description": folder.description,
                    "created_at": folder.created_at,
                    "updated_at": folder.updated_at,
                    "is_deleted": folder.is_deleted,
                    "file_count": file_count,
                }
                for folder, file_count in folders
            ],
        }
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/{folder_id}", status_code=200)
@log_route
async def update_folder_details(
    folder_id: str,
    folder_data: dict,
    current_user: dict = Depends(get_user_from_request),
    db: Session = Depends(get_db),
):
    try:

        userid = UUID(current_user["id"])

        folder = (
            db.query(Folder)
            .filter(Folder.id == folder_id, Folder.userid == userid)
            .first()
        )

        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != userid:
            raise HTTPException(
                status_code=403,
                detail="You do not have permission to update this folder",
            )

        for key, value in folder_data.items():
            if hasattr(folder, key):
                setattr(folder, key, value)

        db.commit()
        db.refresh(folder)

        return {
            "id": str(folder.id),
            "name": folder.name,
            "userid": folder.userid,
            "description": folder.description,
            "created_at": folder.created_at,
            "updated_at": folder.updated_at,
            "is_deleted": folder.is_deleted,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{folder_id}", status_code=200)
@log_route
async def delete_folder(
    folder_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    try:

        # Fetch the folder
        folder = db.query(Folder).filter(Folder.id == folder_id).first()

        # Check if folder exists
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")

        # Check if the folder belongs to the current user
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(
                status_code=403,
                detail="You do not have permission to delete this folder",
            )

        # Check if the folder is already marked as deleted
        if folder.is_deleted:
            return {"message": "Folder is already marked as deleted."}

        # Mark the folder as deleted (soft delete)
        folder.is_deleted = True
        db.commit()

        return {
            "message": "Folder deleted successfully",
            "id": str(folder.id),
            "name": folder.name,
            "userid": folder.userid,
            "description": folder.description,
            "created_at": folder.created_at,
            "updated_at": folder.updated_at,
            "is_deleted": folder.is_deleted,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{folder_id}/file", status_code=200)
@log_route
async def create_file(
    folder_id: str,
    request: Request,
    name: str = Form(...),
    uploaded_file: UploadFile = Files(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
) -> Dict[str, Any]:
    try:

        userpaid = current_user["is_paid"]
        # Check if folder exists
        folder = db.query(Folder).filter(Folder.id == UUID(folder_id)).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        files_query = db.query(File).filter(
            File.folderid == UUID(folder_id), File.is_deleted == False
        )
        file_count = files_query.count()
        if file_count >= current_user["max_file"] and userpaid == False:
            raise HTTPException(
                status_code=402, detail="Not allowed to upload more than two"
            )

        # generate unique name
        name = generate_unique_filename(name, folder_id, db)
        # Ensure the storage folder exists
        storage_path = Path(f"./{settings.UPLOAD_FOLDER}")
        storage_path.mkdir(parents=True, exist_ok=True)

        # Generate a unique filename for the uploaded file
        file_extension = uploaded_file.filename.split(".")[-1].lower()
        new_filename = f"{uuid.uuid4()}.{file_extension}"
        file_path = storage_path / new_filename

        # Write the uploaded file to the storage path
        with file_path.open("wb") as buffer:
            file_content = await uploaded_file.read()
            buffer.write(file_content)

        # Extract metadata from the file
        file_size = file_path.stat().st_size
        file_type = uploaded_file.content_type or "application/octet-stream"
        creation_date = time.strftime(
            "%Y-%m-%d %H:%M:%S", time.gmtime(file_path.stat().st_ctime)
        )
        last_modified_date = time.strftime(
            "%Y-%m-%d %H:%M:%S", time.gmtime(file_path.stat().st_mtime)
        )
        metadata = {
            "file_size": file_size,
            "file_type": file_type,
            "creation_date": creation_date,
            "last_modified_date": last_modified_date,
        }
        # upload to s3
        s3_url = s3_service.upload_file(str(file_path), folder_id, name)
        logger.info("s3 Processing started")

        new_file = File(
            name=name,
            type=file_type,
            size=file_size,
            folderid=folder_id,
            meta_data=json.dumps(metadata),
            is_deleted=False,
            url=s3_url,
            status=(
                FileStatus.QUEUE.value
                if file_type in ["application/pdf"]
                else FileStatus.SUCCESS.value
            ),
        )
        db.add(new_file)
        db.commit()
        db.refresh(new_file)

        if file_type == "application/pdf":
            trigger_prefect_flow_run(new_file, request, db)

        s3_service.delete_local_file(str(file_path))

        # Return response with file details
        return {
            "id": str(new_file.id),
            "name": new_file.name,
            "type": new_file.type,
            "size": new_file.size,
            "folderid": folder_id,
            "meta_data": metadata,
            "is_deleted": new_file.is_deleted,
            "file_path": str(new_filename),  # Return the filename
            "s3_key": str(s3_url),
            "status": (
                new_file.status if file_type != "text/csv" else FileStatus.SUCCESS
            ),
        }

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        # If any unexpected error occurs, clean up by removing the file
        if file_path.exists():
            file_path.unlink()

        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


@router.get("/file/{folder_id}", status_code=200)
async def show_not_deleted_file(
    folder_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
    search: str = None,
    search_enabled: bool = False,
    page: int = 1,
    page_size: int = 10,
):
    try:
        # Retrieve the folder to ensure the user has access
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(status_code=403, detail="You do not have permissions")

        # Base query for files
        files_query = db.query(File).filter(
            File.folderid == folder_id, File.is_deleted == False
        )

        # Apply search filter if enabled and search term is provided
        if search_enabled and search:
            files_query = files_query.filter(File.name.ilike(f"%{search}%"))

        # Get total file count after filters
        total_files = files_query.count()

        # Paginate the query results
        files = files_query.offset((page - 1) * page_size).limit(page_size).all()

        # Prepare the response
        response = {
            "count": total_files,
            "prev": (
                f"/files/{folder_id}?page={page-1}&page_size={page_size}&search={search}&search_enabled={search_enabled}"
                if page > 1
                else None
            ),
            "next": (
                f"/files/{folder_id}?page={page+1}&page_size={page_size}&search={search}&search_enabled={search_enabled}"
                if (page * page_size) < total_files
                else None
            ),
            "results": [
                {
                    "id": str(file.id),
                    "name": file.name,
                    "type": file.type,
                    "size": file.size,
                    "folderid": file.folderid,
                    "meta_data": file.meta_data,
                    "is_deleted": file.is_deleted,
                    "status": file.status,
                    "url": file.url,
                }
                for file in files
            ],
        }
        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{folder_id}/file/{file_id}", status_code=200)
@log_route
async def read_file(
    folder_id: str,
    file_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    try:
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(status_code=403, detail="You do not have permissions")
        file = db.query(File).filter(File.id == file_id).first()

        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        return file

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{folder_id}/file/{file_id}", status_code=200)
@log_route
async def read_file(
    folder_id: str,
    file_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    try:
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(status_code=403, detail="You do not have permissions")
        file = db.query(File).filter(File.id == file_id).first()

        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        return file

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{folder_id}/file/{file_id}", status_code=200)
@log_route
async def delete_file(
    folder_id: str,
    file_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    try:
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(status_code=403, detail="You do not have permissions")
        file = db.query(File).filter(File.id == file_id).first()

        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        file.is_deleted = True
        db.commit()

        return {"message": "File deleted successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/update-status", status_code=200)
@log_route
async def update_status(
    request: Request,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_user_from_request),
):
    body = await request.json()

    # Extract the required fields from the body
    file_id = body.get("file_id")
    status = body.get("status")
    message = body.get("message")

    # Validate inputs
    if not file_id or not status:
        raise HTTPException(status_code=400, detail="file_id and status are required.")

    # Retrieve the file from the database
    file = db.query(File).filter(File.id == file_id).first()

    if not file:
        raise HTTPException(status_code=404, detail="File not found.")

    # Update the status
    file.status = status
    file.message = message

    # Commit the change
    db.commit()

    return {
        "message": "Status updated successfully.",
        "file_id": file_id,
        "status": status,
    }


@router.get(
    "/get-url/{folderid}/{file_id}",
    status_code=200,
)
@log_route
async def get_url(
    folderid: str,
    file_id: str,
    db: Session = Depends(get_db),
):
    try:
        fileid = UUID(file_id)
        file = db.query(File).filter(File.id == fileid).first()
        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        url = s3_presigned_url.get_url(folderid, file.name)
        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=" Error Generating URL for file")


@router.get(
    "/get-url-by-name/{folderid}/{file_name}",
    status_code=200,
)
@log_route
async def get_url_by_name(
    folderid: str,
    file_name: str,
    db: Session = Depends(get_db),
):
    try:
        file = (
            db.query(File)
            .filter(File.folderid == folderid, File.name == file_name)
            .first()
        )
        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        url = s3_presigned_url.get_url(folderid, file.name)
        return {"url": url}
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error retrieving presigned URL")
