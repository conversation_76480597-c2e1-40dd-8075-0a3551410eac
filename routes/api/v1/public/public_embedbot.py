from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from app.database.utils import get_db
from app.database.models.user import User
from app.database.models.folder import Folder
from app.database.models.agentbot import AgentBot
from logger.logger import RequestLogger
from schemas.agentbot import QueryRequest
from uuid import UUID
from services.agentbot.s3_upload import S3Service

from services.chat.query_service import AnswerService
from app.database.models.application_limits import ApplicationLimit
from app.middlewares.utils import rate_limit_dependency
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



            
# service initialization
s3_service = S3Service()
log_route = RequestLogger.log_route
router = APIRouter(prefix="/api/v1/public/agent", tags=["Embed_Agent"])


@router.get("/{agent_id}", status_code=200)
@log_route
async def get_agent(
    agent_id: str,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        try:
            agent_id = UUID(agent_id)
        except Exception as e:
            raise HTTPException(status_code=422, detail="Unprocessable Entity")

        agent = db.query(AgentBot).filter(AgentBot.id == agent_id).first()
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        if not agent.is_exposed:
            raise HTTPException(status_code=403, detail="The Agent isn't Exposed")

        return {
            "agent_name": agent.agent_name,
            "agent_avatar": agent.agent_avatar,  # Emoji string (eg- :smiley:)
            "color_theme": agent.color_theme,
            "avatar_color": agent.avatar_color,
        }
    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching agent: {str(e)}")


@router.post("/{agent_id}/query", status_code=200)
@log_route
async def get_response(
    agent_id: str,
    req: Request,
    Request: QueryRequest = Depends(rate_limit_dependency),
    db: Session = Depends(get_db),  # Database session
):
    try:
        agent_id = UUID(agent_id)
        agent = db.query(AgentBot).filter(AgentBot.id == agent_id).first()
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")

        result = (
            db.query(Folder, User, ApplicationLimit)
            .join(User, Folder.userid == User.id)
            .join(ApplicationLimit, User.id == ApplicationLimit.userid)
            .filter(Folder.id == agent.folder_id)
            .first()
        )

        if result:
            folder, user, user_application_limit = result

        origin = req.headers.get("origin", None)  # Get the Origin header, if present

        if not agent.is_exposed:
            raise HTTPException(
                status_code=400, detail="Agent is not available for public usage."
            )
        if origin:
            logger.info(f"Request coming from domain: {origin}")
        else:
            logger.info("No origin header present")
        # Verify the authorized domain (assuming the authorized domain is a single string for simplicity)
        if agent.authorized_domain != origin:
            raise HTTPException(
                status_code=403, detail="Request's origin domain is not authorized"
            )

        answer_service = AnswerService()
        response_json, token_used, retriever_context = (
            await answer_service.process_query(
                Request.query,
                user_application_limit.is_paid,
                agent.folder_id,
                Request.chat_history,
            )
        )

        return {"content": response_json}
    except HTTPException as http_exc:
        # Specific exception for HTTPException (404, 500, etc.)
        raise http_exc
    except Exception as e:
        
        raise HTTPException(status_code=500, detail=f"Query failed: {e}")
