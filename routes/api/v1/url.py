from fastapi import APIRouter, Depends, Request, HTTPException
from sqlalchemy.orm import Session
from app.database.utils import get_db
from app.core.prefect.knowledgebase import trigger_prefect_flow_run_url
from schemas.url import URLProcessingRequest, URLProcessingResponse
from app.middlewares.middleware import authenticate_request
from app.database.models.files import File, FileStatus
from app.database.models.folder import Folder
from logger.logger import RequestLogger
from uuid import UUID

router = APIRouter(prefix="/api/v1/folder", tags=["folder"])
log_route = RequestLogger.log_route


@router.post("/{folder_id}/url", response_model=URLProcessingResponse)
@log_route
async def process_url(
    request: Request,
    folder_id: UUID,
    url_data: URLProcessingRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(authenticate_request),
):
    """
    Process a URL through the Prefect pipeline for a specific folder
    """
    try:
        # Check if folder exists and user has access
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(status_code=403, detail="You do not have permissions")

        # Check file count limit for non-paid users
        if not current_user["is_paid"]:
            files_query = db.query(File).filter(
                File.folderid == folder_id, File.is_deleted == False
            )
            file_count = files_query.count()
            if file_count >= current_user["max_file"]:
                raise HTTPException(
                    status_code=402,
                    detail="Not allowed to upload more than maximum allowed files",
                )

        # Create File record with URL data
        url_record = File(
            url=url_data.get_url_string(),  # Convert HttpUrl to string
            status=FileStatus.QUEUE.value,  # Use .value to get the string
            name=url_data.get_url_string(),  # Using URL as name
            type="url",  # Indicating this is a URL type record
            folderid=folder_id,
            meta_data={
                "crawler_type": url_data.crawler_type or "cheerio",
                "max_requests": url_data.max_requests or 10,
                "crawl_links": url_data.crawl_links or True,
                "enqueue_strategy": url_data.enqueue_strategy or "same-domain",
            },
        )
        db.add(url_record)
        db.commit()
        db.refresh(url_record)

        # Trigger Prefect flow with URL record
        trigger_prefect_flow_run_url(file=url_record, request=request, session=db)

        return URLProcessingResponse(
            url_id=str(url_record.id),
            status=FileStatus.QUEUE.value,
            message="URL processing initiated successfully",
            metadata=url_record.meta_data,
        )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        # Update URL status if flow trigger fails
        if "url_record" in locals():
            url_record.status = FileStatus.FAILED.value
            url_record.message = str(e)
            db.commit()
        raise HTTPException(status_code=500, detail=f"Failed to process URL: {str(e)}")


@router.post("/url/update-status", status_code=200)
@log_route
async def update_url_status(
    request: Request,
    db: Session = Depends(get_db),
):
    """
    Update URL processing status via webhook.
    Called by Prefect flow to update processing status.
    """
    try:
        body = await request.json()

        # Extract the required fields from the body
        status = body.get("status")
        message = body.get("message")
        kwargs = body.get("kwargs", {})
        file_id = kwargs.get("file_id")

        if not file_id or not status:
            raise HTTPException(
                status_code=400, detail="file_id and status are required."
            )

        # Retrieve the URL from the database
        url_record = (
            db.query(File).filter(File.id == file_id, File.type == "url").first()
        )

        if not url_record:
            raise HTTPException(status_code=404, detail="URL record not found.")

        # Update the status
        url_record.status = status
        url_record.message = message
        db.commit()

        return {
            "message": "Status updated successfully.",
            "url_id": str(url_record.id),
            "status": status,
        }
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to update status: {str(e)}"
        )


@router.get("/{folder_id}/url/{url_id}", status_code=200)
@log_route
async def get_url_status(
    folder_id: UUID,
    url_id: UUID,
    db: Session = Depends(get_db),
    current_user: dict = Depends(authenticate_request),
):
    """
    Get URL processing status
    """
    try:
        # Check if folder exists and user has access
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")
        if folder.userid != UUID(current_user["id"]):
            raise HTTPException(status_code=403, detail="You do not have permissions")

        url_record = (
            db.query(File)
            .filter(
                File.id == url_id,
                File.type == "url",
                File.folderid == folder_id,
                File.is_deleted == False,
            )
            .first()
        )

        if not url_record:
            raise HTTPException(status_code=404, detail="URL record not found")

        return {
            "id": str(url_record.id),
            "url": url_record.url,
            "status": url_record.status,
            "message": url_record.message,
            "folderid": str(url_record.folderid),
            "meta_data": url_record.meta_data,
        }

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
