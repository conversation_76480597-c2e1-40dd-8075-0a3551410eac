from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import select
from pydantic import BaseModel
from typing import Optional
import uuid

from app.database.utils import get_db
from app.database.models.user_model import UserModel

router = APIRouter()


class UserModelUpdate(BaseModel):
    model_id: str


class UserModelResponse(BaseModel):
    id: str
    user_id: str
    model_id: str

    class Config:
        from_attributes = True


@router.put("/user_model/{user_id}")
async def update_user_model(
    user_id: str,
    user_model_data: UserModelUpdate,
    db: Session = Depends(get_db)
):
    """
    Update or create model_id for a user_id
    """
    try:
        # Convert user_id string to UUID
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user_id format"
        )
    
    # Check if user_model record exists
    stmt = select(UserModel).where(UserModel.user_id == user_uuid)
    existing_record = db.execute(stmt).scalar_one_or_none()
    
    if existing_record:
        # Update existing record
        existing_record.model_id = user_model_data.model_id
        db.commit()
        db.refresh(existing_record)
        return UserModelResponse(
            id=str(existing_record.id),
            user_id=str(existing_record.user_id),
            model_id=existing_record.model_id
        )
    else:
        # Create new record
        new_user_model = UserModel(
            user_id=user_uuid,
            model_id=user_model_data.model_id
        )
        db.add(new_user_model)
        db.commit()
        db.refresh(new_user_model)
        return UserModelResponse(
            id=str(new_user_model.id),
            user_id=str(new_user_model.user_id),
            model_id=new_user_model.model_id
        )


@router.get("/user_model/{user_id}")
async def get_user_model(
    user_id: str,
    db: Session = Depends(get_db)
):
    """
    Get model_id for a user_id
    """
    try:
        # Convert user_id string to UUID
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user_id format"
        )
    
    # Get user_model record
    stmt = select(UserModel).where(UserModel.user_id == user_uuid)
    user_model = db.execute(stmt).scalar_one_or_none()
    
    if not user_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User model not found"
        )
    
    return UserModelResponse(
        id=str(user_model.id),
        user_id=str(user_model.user_id),
        model_id=user_model.model_id
    )


@router.delete("/user_model/{user_id}")
async def delete_user_model(
    user_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete model_id record for a user_id
    """
    try:
        # Convert user_id string to UUID
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user_id format"
        )
    
    # Get user_model record
    stmt = select(UserModel).where(UserModel.user_id == user_uuid)
    user_model = db.execute(stmt).scalar_one_or_none()
    
    if not user_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User model not found"
        )
    
    db.delete(user_model)
    db.commit()
    
    return {"message": "User model deleted successfully"}
