from pydantic import BaseModel, Field
from typing import List, Optional
from uuid import UUID
from datetime import datetime


class QueryRequest(BaseModel):
    query: Optional[str] = None
    session_id: Optional[UUID] = None


class ChatMessageResponse(BaseModel):
    type: str
    content: str
    metadata: Optional[dict]


class Clarify(BaseModel):
    """Human clarification"""

    needs_clarification: bool = Field(
        description="Need clarification to the question, True or False"
    )
    clarification_question: str = Field(description="Ask the clarification required")
