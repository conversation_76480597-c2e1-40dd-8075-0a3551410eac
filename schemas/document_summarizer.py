from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from uuid import UUID


class DocumentSummaryCreateRequest(BaseModel):
    name: str = Field(..., description="Name of the document summary")
    prompt: Optional[str] = Field(None, description="Custom prompt for summarization")
    meta_data: Optional[dict] = Field(None, description="Additional metadata")


class DocumentSummaryUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, description="Name of the document summary")
    prompt: Optional[str] = Field(None, description="Custom prompt for summarization")
    meta_data: Optional[dict] = Field(None, description="Additional metadata")


class DocumentSummaryResponse(BaseModel):
    id: UUID
    name: str
    status: str
    prompt: Optional[str]
    summary: Optional[str]
    updated_at: Optional[datetime]
    is_deleted: bool
    meta_data: Optional[dict]


class DocumentSummaryListResponse(BaseModel):
    results: List[DocumentSummaryResponse]
    count: int
    prev: Optional[str]
    next: Optional[str]


class DocumentSummaryFileUploadResponse(BaseModel):
    id: UUID
    name: str
    type: str
    size: Optional[int]
    status: str
    message: Optional[str]


class DocumentSummaryFileResponse(BaseModel):
    id: UUID
    name: str
    type: str
    size: Optional[int]
    path: str
    status: str
    url: Optional[str]
    meta_data: Optional[dict]
    updated_at: Optional[datetime]
    is_deleted: bool 