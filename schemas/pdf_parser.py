from pydantic import BaseModel
from uuid import UUID
from typing import Optional, Dict
from datetime import datetime
class PdfResponse(BaseModel):
    
    s3url : str
    file_name : str


class FolderResponse(BaseModel):
    id: str
    name: str
    userid: str
    description: str
    created_at: datetime
    updated_at: datetime


class CreateFolder(BaseModel):
    name : str
    description: str 


class FileCreate(BaseModel):
    name: str
    type: Optional[str] = None
    size: Optional[int] = None
    meta_data: Optional[Dict]
    is_deleted: Optional[bool] = False
class FileResponse(BaseModel):
    id:UUID
class PresignedURLResponse(BaseModel):
    message: str
    presigned_url: str
    file_name: str
class FileUploadResponse(BaseModel):
    message: str  
    file_name: str  