from pydantic import BaseModel, HttpUrl
from typing import Dict, Any, Optional


class URLProcessingResponse(BaseModel):
    url_id: str  # Not URL itself because it can be scraped in multiple ways and multiple times
    status: str
    message: str
    metadata: Dict[str, Any] = {}


class URLProcessingRequest(BaseModel):
    url: HttpUrl
    crawler_type: Optional[str] = "cheerio"
    max_requests: Optional[int] = 10
    crawl_links: Optional[bool] = True
    enqueue_strategy: Optional[str] = "same-domain"

    def get_url_string(self) -> str:
        return str(self.url)  # Convert HttpUrl to string
