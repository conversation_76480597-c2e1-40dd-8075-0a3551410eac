from pydantic import BaseModel, EmailStr, ConfigDict, <PERSON>
from typing import Optional
from datetime import datetime
from uuid import UUID


# Schema for user creation request


class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str


# Schema for user response
class UserResponse(BaseModel):
    id: UUID
    username: str
    email: EmailStr
    profile_image: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class UserUpdate(BaseModel):
    username: Optional[str] = None
    password: Optional[str] = None


# Schema for login request
class LoginRequest(BaseModel):
    email: str
    password: str = Field(min_length=6)


# Schema for login response
class LoginResponse(BaseModel):
    access_token: str
    token_type: str


class Token(BaseModel):
    access_token: str
    token_type: str


class GoogleLoginRequest(BaseModel):
    credential: str


class ForgotPassword(BaseModel):
    email: str


class ResetPassword(BaseModel):
    token: str
    new_password: str = Field(min_length=6)
