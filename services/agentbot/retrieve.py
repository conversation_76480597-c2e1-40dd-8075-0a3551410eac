from uuid import UUID
from fastapi import HTTPException
from sqlalchemy.orm import Session
import weaviate
from langchain_community.embeddings import HuggingFaceInferenceAPIEmbeddings
from app.core.config import settings
from services.pdf_parser.utils import sanitize_class_name
import google.generativeai as genai


class AGENTBOTSERVICE:
    def __init__(self):
        self.gemini = genai.configure(api_key=settings.GOOGLE_API_KEY)
        self.gemini_model = genai.GenerativeModel("gemini-1.5-flash")
        self.client = weaviate.Client(
            url=settings.WEAVIATE_URL,
            auth_client_secret=weaviate.AuthApiKey(settings.WEAVIATE_API_KEY),
        )
        self.embeddings = HuggingFaceInferenceAPIEmbeddings(
            api_key=settings.HUGGINGFACE_API_KEY,
            model_name=settings.MODEL_NAME,
        )

    def generate_embedding(self, text):
        embedding = self.embeddings.embed_documents(text)
        return embedding

    def get_relevant_documents(self, query: str, index_name: str, k: int = 5):
        try:
            weaviate_response = []

            index_name = sanitize_class_name(str(index_name))
          
            question_embedding = self.generate_embedding(query)
           
            retriever = (
                self.client.query.get(
                    index_name,
                    [
                        "text",
                        "file_directory",
                        "filename",
                        "filetype",
                        "languages",
                        "last_modified",
                        "page_number",
                        "unique_id",
                    ],
                )
                .with_near_vector({"vector": question_embedding})
                .with_limit(5)
                .do()
            )
            
        

            if (
                "data" in retriever
                and "Get" in retriever["data"]
                and index_name in retriever["data"]["Get"]
            ):
                
                for item in retriever["data"]["Get"][index_name]:
                    weaviate_response.append(item)

            # Return document contents as plain text
            return weaviate_response

        except Exception as e:
            
            raise HTTPException(
                status_code=500, detail=f"Failed to retrieve relevant documents: {e}"
            )