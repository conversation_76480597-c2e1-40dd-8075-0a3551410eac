import boto3
from typing import Dict
from botocore.exceptions import NoCredentialsError
from fastapi import FastAPI, File, UploadFile, HTTPException
from app.core.config import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class S3Service:
    def __init__(self):
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_DEFAULT_REGION,
            config=boto3.session.Config(
                signature_version="s3v4", connect_timeout=10, read_timeout=10
            ),
        )
        self.region_name = (settings.AWS_DEFAULT_REGION,)
        self.bucket_name = settings.AWS_BUCKET_NAME

    def upload_file(self, file: UploadFile, file_name: str) -> str:
        try:
            # Read file content
            file_content = file.file.read()

            # Create the object key by using the folder and file name
            file_name = f"{file_name}/{file.filename}"

            # Debug information
            logger.info(f"Uploading to bucket: {self.bucket_name}")
            logger.info(f"File key: {file_name}")
            logger.info(f"Content type: {file.content_type}")

            # Ensure ContentType is set
            content_type = file.content_type or "application/octet-stream"

            # Upload the file to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=file_name,
                Body=file_content,
                ContentType=content_type,
                ACL="public-read",  # Make the file publicly accessible
            )

            # Generate the public URL
            file_url = f"https://{self.bucket_name}.s3.{self.region_name[0]}.amazonaws.com/{file_name}"
            return file_url

        except NoCredentialsError:
            logger.info("AWS credentials are missing.")
            raise HTTPException(
                status_code=403, detail="AWS credentials are not available."
            )
        except Exception as e:
            logger.info(f"Error during upload: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Error uploading file to S3: {str(e)}"
            )
