from datetime import datetime, timedelta
from typing import Optional
from passlib.context import Crypt<PERSON>ontext
import jwt
import os
# Load environment variables
from app.core.config import settings
from fastapi import HTTPException
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart

from app.database.models.user import User
from app.database.utils import get_db
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



# Define secret key and algorithm for JWT

SECRET_KEY = settings.SECRET_KEY

ALGORITHM = settings.ALGORITHM

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Hash password
def hash_password(password: str) -> str:
    return pwd_context.hash(password)

# Verify password
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

# Generate JWT token
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=4250))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

# Decode JWT token
def decode_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None


def generate_password_reset_token(email: str) -> str:
    """Generate JWT token for password reset."""
    expire = datetime.utcnow() + timedelta(minutes=30)
    return jwt.encode(
        {
            "exp": expire,
            "nbf": datetime.utcnow(),
            "sub": email
        },
        settings.SECRET_KEY,
        algorithm="HS256"
    )

def get_email_from_password_reset_token(token: str) -> str:
    """Verify and extract email from password reset token."""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return payload["sub"]
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Reset token has expired")
    except jwt.JWTError:
        raise HTTPException(status_code=400, detail="Invalid reset token")

def send_reset_email(user_id: int, reset_url: str):
    """Send password reset email using SMTP."""
    try:
        session = next(get_db())
        user = session.query(User).filter(User.id == user_id).first()
        
        if not user:
            logger.info(f"User {user_id} not found")
            return
        
        # SMTP Configuration
        smtp_server = settings.SMTP_SERVER
        smtp_port = 587
        smtp_username = settings.SMTP_USERNAME
        smtp_password = settings.SMTP_PASSWORD
        from_email = "AI Planet Support <<EMAIL>>"
        to_email = user.email
        
        # Create message
        message = MIMEMultipart("alternative")
        message["Subject"] = "Reset Your Password"
        message["From"] = from_email
        message["To"] = to_email
        
        # HTML email content
        html_content = f"""
        <html>
        <body>
            <p>Hello!</p>
            <p>You're receiving this email because a password reset was requested for your account.</p>
            <p>To reset your password, please click the button below:</p>
            <p><a href="{reset_url}" style="padding: 10px 20px; color: white; background-color: #44924C; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
            <p>This link will expire in 30 minutes. If you don't reset your password within that time, you can request a new link by visiting <a href="{reset_url.split('/reset-password')[0]}/forgot-password">here</a>.</p>
            <p>If you did not request to reset your password, you can safely ignore this email. Your password will remain unchanged.</p>
            <p>
            Thank you,<br>
            The Support Team
            </p>
        </body>
        </html>
        """
        
        part = MIMEText(html_content, "html")
        message.attach(part)
        
        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.sendmail(from_email, [to_email], message.as_string())
        server.quit()
        
    except Exception as e:
        logger.info(f"Error sending email: {e}")
        raise
    finally:
        session.close()
