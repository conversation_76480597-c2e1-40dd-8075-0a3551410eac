from services.pdf_parser.utils import sanitize_class_name
import google.generativeai as genai
from langchain_openai import AzureChatOpenAI
from openai import AsyncAzureOpenAI
from app.core.config import settings
import re
import json
from services.prompts.prompts import system_prompt
import weaviate
import aioboto3
import time
import logging
from services.translation.translator_service import create_translator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AnswerService:
    """
    A service class responsible for generating AI-driven answers.
    It uses different approaches, depending on whether the user has paid or not
    and whether we want multiple detailed answers or a single short answer.
    """

    def __init__(self):
        """
        :param retriever_service: An object that provides access to the language models (llm and gemini_model).
        """

        self.ai_msg_text = ""
        self.total_token_used = None
        self.gemini = genai.configure(api_key=settings.GOOGLE_API_KEY)
        self.gemini_model = genai.GenerativeModel(settings.GEMINI_MODEL)
        # self.embeddings = AzureOpenAIEmbeddings(
        #     azure_deployment="text-embedding-3-small",
        #     azure_endpoint=settings.AZURE_EMBEDDING_API_URL,
        #     openai_api_key=settings.AZURE_EMBEDDING_API_KEY,
        #     openai_api_version="2023-05-15",
        #     chunk_size=200,
        # )

        self.embeddings = AsyncAzureOpenAI(
            azure_deployment="text-embedding-3-small",
            azure_endpoint=settings.AZURE_EMBEDDING_API_URL,
            api_key=settings.AZURE_EMBEDDING_API_KEY,
            api_version="2023-05-15",
        )
        # https://tunkeyproduct.openai.azure.com/"
        # "8VatSchgvZZMw8u62uhKnJgN8M56SciALrbYvLRqDqEMvd3Scj0hJQQJ99BBACYeBjFXJ3w3AAABACOGlV7v"
        self.llm = AzureChatOpenAI(
            model="gpt-4o-mini",
            azure_deployment="intern-gpt4",
            api_version="2023-07-01-preview",
            temperature=0.3,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            openai_api_key=settings.AZUREOPENAI_API_KEY,
        )
        self.use_azure_only = settings.USE_AZURE_ONLY

        self.model_id = "cohere.rerank-v3-5:0"
        self.model_package_arn = f"arn:aws:bedrock:{settings.BEDROCK_REGION}::foundation-model/{self.model_id}"

        self.client = weaviate.Client(
            url=settings.WEAVIATE_URL,
            auth_client_secret=weaviate.AuthApiKey(settings.WEAVIATE_API_KEY),
        )
        self.gemini_msg = ""

    async def query_transformation(
        self, query: str, user_paid: bool, conversation_history: list[dict]
    ):
        """
        Transforms the query and chat history into a single string for AI processing.

        Args:
            query (str): User query.
            chat_history (list): List of chat messages.

        Returns:
            str: Concatenated string of query and chat history.
        """
        start_time = time.time()
        if self.use_azure_only or user_paid:
            messages = [
                (
                    "system",
                    """You are an AI assistant optimizing queries for better retrieval from a vector database.
                    Your task is to expand and rephrase the query **only if necessary** to improve search accuracy.
                    If the provided conversation history is irrelevant to the query, return the query exactly as it is,
                    without any modification or additional explanation.""",
                ),
                (
                    "human",
                    f"""
                    Conversation History:{conversation_history}

                    User Query: {query}

                    Output only the transformed query (or the exact same query if no changes are needed).
                    """,
                ),
            ]
            ai_msg = await self.llm.ainvoke(messages)
            expanded_query = ai_msg.content

        else:
            prompt = f"""
                You are an AI assistant optimizing queries for better retrieval from a vector database.
                Your task is to expand and rephrase the query **only if necessary** to improve search accuracy.
                If the provided conversation history is irrelevant to the query, return the query exactly as it is,
                without any modification or additional explanation.

                Conversation History:
                {conversation_history}

                User Query: {query}

                Output only the transformed query (or the exact same query if no changes are needed).

            """

            ai_msg = await self.gemini_model.generate_content_async(prompt)
            expanded_query = ai_msg.candidates[0].content.parts[0].text
        end_time = time.time()
        logging.info(f"Query Transformation Time: {end_time - start_time:.2f} seconds")
        return expanded_query

    async def rerank_text(self, text_query, text_sources, num_results):
        """Calls AWS Bedrock to rerank text asynchronously."""
        session = aioboto3.Session()
        async with session.client(
            "bedrock-agent-runtime",
            aws_access_key_id=settings.BEDROCK_ACCESS_KEY,
            aws_secret_access_key=settings.BEDROCK_SECRET_KEY,
            region_name=settings.BEDROCK_REGION,
        ) as client:
            response = await client.rerank(
                queries=[{"type": "TEXT", "textQuery": {"text": text_query}}],
                sources=text_sources,
                rerankingConfiguration={
                    "type": "BEDROCK_RERANKING_MODEL",
                    "bedrockRerankingConfiguration": {
                        "numberOfResults": num_results,
                        "modelConfiguration": {"modelArn": self.model_package_arn},
                    },
                },
            )
        return response["results"]

    async def rerank(self, query: str, docs, top_n: int = 20):
        """Formats documents for reranking and returns top-ranked results."""
        start_time = time.time()
        text_sources = []
        for doc in docs:
            text_sources.append(
                {
                    "type": "INLINE",
                    "inlineDocumentSource": {
                        "type": "TEXT",
                        "textDocument": {
                            "text": doc["text"],  # Extracting text from document
                        },
                    },
                }
            )

        if len(text_sources) <= 0:
            return text_sources

        if len(text_sources) < top_n:
            top_n = len(text_sources)

        results = await self.rerank_text(query, text_sources, top_n)

        # Extracting ranked documents based on model response
        ranked_docs = []
        for result in results:
            index = result["index"]
            score = result["relevanceScore"]
            ranked_docs.append(
                {
                    "text": docs[index]["text"],
                    "file_directory": docs[index]["metadata"]["file_directory"],
                    "page_number": docs[index]["metadata"]["page_number"],
                    "filename": docs[index]["metadata"]["filename"],
                    "filetype": docs[index]["metadata"]["filetype"],
                    "unique_id": docs[index]["metadata"]["unique_id"],
                    "last_modified": docs[index]["metadata"]["last_modified"],
                    "score": score,
                    "source": docs[index]["metadata"]["source"],
                }
            )

        # Sort results by score in descending order
        ranked_docs = sorted(ranked_docs, key=lambda x: x["score"], reverse=True)
        # print(ranked_docs)
        end_time = time.time()
        logging.info(f"Reranking Time: {end_time - start_time:.2f} seconds")
        return ranked_docs

    def add_file_index(self, data):
        """
        Adds a file_index to metadata items based on unique filenames.

        Args:
            data (list): List of dictionaries containing answers and metadata.

        Returns:
            list: Updated list with file_index added to metadata items.
        """
        # Create a mapping for filenames to unique indices
        file_index_map = {}
        current_index = 1

        # Iterate through the data to process metadata
        for entry in data:
            for metadata_item in entry.get("metadata", []):
                filename = metadata_item.get("filename")
                if filename:
                    # Assign a unique index if the filename is not already in the map
                    if filename not in file_index_map:
                        file_index_map[filename] = current_index
                        current_index += 1
                    # Add the file_index to the metadata
                    metadata_item["file_index"] = file_index_map[filename]

        return data

    def restructure_data(self, summary_data, metadata):
        """
        Restructures summary data and metadata into the desired format.

        Args:
            summary_data (dict): Dictionary containing summary and details with unique IDs.
            metadata (list): List of metadata dictionaries containing unique IDs and associated information.

        Returns:
            dict: Restructured data combining answers and their associated metadata.
        """
        # Create a mapping of metadata using unique_id for quick lookup
        metadata_map = {
            item.get("unique_id"): item for item in metadata if item.get("unique_id")
        }

        # Initialize the output structure
        structured_data = {"summary": summary_data.get("summary", ""), "details": []}

        # Process each detail in the summary data
        for detail in summary_data.get("details", []):
            source = detail.get("source", {})
            unique_ids = source.get("unique_id", "")

            if unique_ids:
                # Split unique_ids and fetch metadata for each valid unique ID
                associated_metadata = [
                    metadata_map[uid]
                    for uid in unique_ids.split(", ")
                    if uid in metadata_map
                ]
            else:
                associated_metadata = []

            # Append to the structured details
            structured_data["details"].append(
                {"answer": detail.get("answer", ""), "metadata": associated_metadata}
            )
        structured_data["details"] = self.add_file_index(structured_data["details"])
        return structured_data

    async def generate_embedding(self, text):
        start_time = time.time()

        embedding = await self.embeddings.embeddings.create(
            model="text-embedding-3-small", input=[text]  # or your deployed model name
        )
        # logging.info(embedding.data[0].embedding)
        end_time = time.time()
        logging.info(f"Embedding Generation Time: {end_time - start_time:.2f} seconds")

        return embedding.data[0].embedding

    async def get_relevant_documents(self, query: str, folder_id: str, top_k: int = 5):
        # try:
        # to fix circular import issue in production, please do not change without consulting

        weaviate_response = []

        index_name = sanitize_class_name(str(folder_id))
        question_embedding = await self.generate_embedding(query)
        start_time = time.time()

        async def retrieval_query():
            retriever = (
                self.client.query.get(
                    index_name,
                    [
                        "text",
                        "metadata { filename, page_number, unique_id, last_modified, filetype, file_directory, source }",
                    ],
                )
                .with_hybrid(
                    query=query, alpha=settings.ALPHA, vector=question_embedding
                )
                .with_limit(30)
                .do()
            )
            return retriever

        retriever = await retrieval_query()

        if (
            "data" in retriever
            and "Get" in retriever["data"]
            and index_name in retriever["data"]["Get"]
        ):
            logging.info("Retrieved data from Weaviate")
            for item in retriever["data"]["Get"][index_name]:
                weaviate_response.append(item)

        logging.info(f"Retrieved {len(weaviate_response)} documents")
        end_time = time.time()
        logging.info(f"Retrieval Time: {end_time - start_time:.2f} seconds")

        return weaviate_response

        # except Exception as e:
        #     print(
        #         f"Error retrieving documents: {e}"
        #     )  # Replace with proper logging in production
        #     raise HTTPException(
        #         status_code=500, detail=f"Failed to retrieve relevant documents: {e}"
        #     )

    async def process_query(
        self, query: str, user_paid: bool, folder_id: str, conversation_history: list, agent_instructions: str = None
    ):
        """
        Process query using either Bedrock translation service (IS_BEDROCK=true) or regular processing (IS_BEDROCK=false)
        """
       
        # Regular processing using existing Azure/Gemini logic in generate_answer
        retriever_results = []
        
        # Uncomment and modify these lines as needed for full document processing
        # expanded_query = await self.query_transformation(
        #     query, user_paid, conversation_history
        # )
        # logging.info(expanded_query)
        # retriever_results = await self.get_relevant_documents(expanded_query, folder_id)
        logging.info(query)
        # retriever_results = await self.rerank(
        #     query=query, docs=retriever_results, top_n=settings.TOP_K
        # )
        # relevant_context = "\n\n".join(
        #     [
        #         f"(PDF: {item['filename']}, Page number: {item['page_number']}, unique_id: {item['unique_id']}): {item['text']}"
        #         for item in retriever_results
        #     ]
        # )

        # full_context = f"Conversation History:\n{conversation_history}\n\nRelevant Context:\n{relevant_context}"
        full_context = f"Conversation History:\n{conversation_history}\n"

        # Use the existing generate_answer logic with Azure/Gemini
        answer, token_used = await self.generate_answer(
            query=query, user_paid=user_paid, full_context=full_context, agent_instructions=agent_instructions
        )

        print(f"Answer: {answer}")

        # Check if this is a translation response (plain text) or regular JSON response
        json_match = re.search(r"(\{.*\})", answer, re.DOTALL)
        if json_match:
            # Regular JSON response
            response_json = json.loads(json_match.group(1))
            response_json = self.restructure_data(response_json, retriever_results)
        else:
            # Plain text response - wrap in JSON format
            response_json = {
                "summary": answer,
                "details": []
            }
        
        return response_json, token_used, retriever_results

    async def generate_answer(self, query: str, user_paid: bool, full_context: str, agent_instructions: str = None):
        start_time = time.time()
        
        base_prompt = """
       You are a smart, helpful, and instruction-aware assistant that communicates with users in a natural and conversational way. Your main goal is to answer questions clearly and accurately. However, **always follow user instructions first**.

        Your special ability is **bidirectional translation between English and Tamil**, but only when explicitly asked.

        ---

        🔁 **Behavior Rules**

        1. **Always check if the user has asked for a translation or given specific instructions.**  
        - If the user requests a translation, follow it carefully.
        - If the user does **not** request a translation, do **not** translate.
        - Never assume translation is needed unless clearly asked.

        2. If the user asks for a translation:
        - **From English to Tamil** → Output Tamil.
        - **From Tamil to English** → Output English.
        - **For bi-directional translation**, detect the input language and translate accordingly.

        3. If the input is a **greeting or a general message**, respond naturally and appropriately in that language — **do not translate unless asked**.

        4. If no instruction is given and it's a general question, answer naturally in the **same language as the question**.

        ---

        🌐 **Language Logic**

        - **"Translate to Tamil"** → Output Tamil.
        - **"Translate to English"** → Output English.
        - **No translation instruction** → Just respond conversationally.

        ---

        🔧 **Additional Instructions**

        If the user provides any special instruction (such as:  
        "write it in markdown", "use formal tone", "make it concise", "explain like I'm 5", etc.):

        - You **must prioritize and follow that instruction** before anything else.
        - These instructions override default behavior when relevant.

        Notes:-
        Do not translate if they don't have asked you to translate. 

        """
        
        if self.use_azure_only or user_paid:
            messages = [
                ("system", base_prompt),
                ("human", f"Instructions: {agent_instructions}\n\nQuery: {query}" if agent_instructions else f"Query: {query}")
            ]

            ai_msg = await self.llm.ainvoke(messages)
            self.total_token_used = ai_msg.response_metadata["token_usage"]["total_tokens"]
            self.ai_msg_text = ai_msg.content
        elif not user_paid:
            prompt_gemini = f"""
            System Prompt: {base_prompt}
            User Prompt:
                Instructions: {agent_instructions}
                
                Query: {query}
            """

            self.gemini_msg = await self.gemini_model.generate_content_async(prompt_gemini)
            self.total_token_used = self.gemini_msg.usage_metadata.total_token_count
            self.ai_msg_text = self.gemini_msg.candidates[0].content.parts[0].text
        
        logging.info(self.total_token_used)
        total_token_used = self.total_token_used // 100
        end_time = time.time()
        logging.info(f"Answer Generation Time: {end_time - start_time:.2f} seconds")

        return self.ai_msg_text, total_token_used

    def add_file_index(self, data):
        """
        Adds a file_index to metadata items based on unique filenames.

        Args:
            data (list): List of dictionaries containing answers and metadata.

        Returns:
            list: Updated list with file_index added to metadata items.
        """
        # Create a mapping for filenames to unique indices
        file_index_map = {}
        current_index = 1

        # Iterate through the data to process metadata
        for entry in data:
            for metadata_item in entry.get("metadata", []):
                filename = metadata_item.get("filename")
                if filename:
                    # Assign a unique index if the filename is not already in the map
                    if filename not in file_index_map:
                        file_index_map[filename] = current_index
                        current_index += 1
                    # Add the file_index to the metadata
                    metadata_item["file_index"] = file_index_map[filename]

        return data

    def restructure_data(self, summary_data, metadata):
        """
        Restructures summary data and metadata into the desired format.

        Args:
            summary_data (dict): Dictionary containing summary and details with unique IDs.
            metadata (list): List of metadata dictionaries containing unique IDs and associated information.

        Returns:
            dict: Restructured data combining answers and their associated metadata.
        """
        # Create a mapping of metadata using unique_id for quick lookup
        metadata_map = {
            item.get("unique_id"): item for item in metadata if item.get("unique_id")
        }

        # Initialize the output structure
        structured_data = {"summary": summary_data.get("summary", ""), "details": []}

        # Process each detail in the summary data
        for detail in summary_data.get("details", []):
            source = detail.get("source", {})
            unique_ids = source.get("unique_id", "")

            if unique_ids:
                # Split unique_ids and fetch metadata for each valid unique ID
                associated_metadata = [
                    metadata_map[uid]
                    for uid in unique_ids.split(", ")
                    if uid in metadata_map
                ]
            else:
                associated_metadata = []

            # Append to the structured details
            structured_data["details"].append(
                {"answer": detail.get("answer", ""), "metadata": associated_metadata}
            )
        structured_data["details"] = self.add_file_index(structured_data["details"])
        return structured_data

    def get_answer(self, query: str, user_paid: bool, past_message, folder_id):

        relvantcontext = self.get_relevant_documents(
            query=query,
            folder_id=folder_id,
        )
        relevant_context = "\n\n".join(
            [
                f"(PDF: {item['filename']}, Page number: {item['page_number']}, unique_id: {item['unique_id']}): {item['text']}"
                for item in relvantcontext
            ]
        )
        fullcontext = f"Conversation History:\n{past_message}\n\nRelevant Context:\n{relevant_context}"
        answer = self.generate_answer(
            query=query, user_paid=user_paid, full_context=fullcontext
        )
        json_match = re.search(r"(\{.*\})", answer, re.DOTALL)
        if not json_match:
            raise ValueError("No valid JSON found in AI response.")
        json_string = json_match.group(1)
        response_json = json.loads(json_string)
        response_json = self.restructure_data(response_json, relvantcontext)
        return response_json
