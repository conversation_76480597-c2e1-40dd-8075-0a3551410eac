from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Any, Dict, <PERSON>, Tuple, Union

from fastapi import HTTPException
import pandas as pd
from sqlalchemy.orm import Session
from app.core.config import settings
from app.database.models.application_limits import ApplicationLimit
from app.database.models.files import File
from app.database.models.user import User
from schemas.chat import Clarify
from langchain_openai import AzureChatOpenAI
from langchain_experimental.agents import create_pandas_dataframe_agent
from langchain.agents.agent_types import AgentType
from langchain_community.callbacks import get_openai_callback
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import <PERSON>sonOutputParser
import tiktoken
from fastapi import HTTPException

from .prompts import CLARIFICATION_QUERY
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field


class RelevantFiles(BaseModel):
    files: List[str] = Field(description="A list of relevant CSV file names.")


class DataAnalysisAgent:
    def __init__(
        self,
        user_id: str,
        db: Session,
    ):

        self.llm = AzureChatOpenAI(
            openai_api_version="2023-05-15",
            azure_deployment="intern-gpt4",
            api_key=settings.AZUREOPENAI_API_KEY,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            temperature=0.3,
        )
        self.df = None
        self.agent = None
        self.chat_history: List[Dict] = []
        self.clarification_history: Dict = {}
        self.user_id = user_id
        self.db = db
        self.user = self.db.query(User).filter(User.id == self.user_id).first()
        self.app_limit = self.get_application_limit()
        self.reset_tokens_if_needed()
        self.total_tokens_used = 0
        self.plot_count = 0
        self.plots = {}
        self.csv_files = []

    def get_application_limit(self):
        """Fetch the application limit details for the given user_id."""
        app_limit = (
            self.db.query(ApplicationLimit)
            .filter(ApplicationLimit.userid == self.user_id)
            .first()
        )
        if not app_limit:
            raise HTTPException(status_code=404, detail="Application limit not found")
        return app_limit

    def load_data(self, data: Union[str, pd.DataFrame], csv_files: List[File]) -> None:
        if isinstance(data, pd.DataFrame):
            self.df = data
        else:
            self.df = pd.read_csv(data)
        self.summary = self.df.head(3).to_string()
        self.csv_files = csv_files

        self.agent = create_pandas_dataframe_agent(
            self.llm,
            self.df,
            verbose=True,
            agent_type=AgentType.OPENAI_FUNCTIONS,
            handle_parsing_errors=True,
            allow_dangerous_code=True,
        )

    def get_relevant_csv_files(self, query: str, response: str) -> List[File]:
        if not query:
            return []

        csv_files = []

        for i in self.csv_files:
            csv_files.append(i.name)

        prompt = f"""
            Based on the following query: "{query}", determine which CSV files should be used for the analysis.
            Return a JSON object with a key "files" containing a list of relevant CSV filenames. 
            Use this response "{response}" to identify the relevant files.
            If no files are relevant, return an empty list.

            Below are the csv files being used:

            {str(csv_files)}
        """

        parser = JsonOutputParser(pydantic_object=RelevantFiles)

        try:
            response = self.llm.invoke(prompt)
            parsed_response = parser.parse(response.content)
            relevant_filenames = parsed_response["files"]

            relevant_files = [
                file.name for file in self.csv_files if file.name in relevant_filenames
            ]

            return relevant_files
        except Exception as e:
            print("ERROR bro", e)
            return []

    def get_plot_name(self, query: str, plot_description: str = None):
        if not query:
            return f"Plot_{self.plot_count}"

        combined_input = query
        if plot_description:
            combined_input = f"{query} - {plot_description}"

        prompt = f"""
            Generate a short and clear plot name based on the following query and description: "{combined_input}".
            The name should be descriptive of the plot's content, simple, understandable, and no longer than 20 characters.
            Respond with only the name, nothing else.
        """

        try:
            response = self.llm.invoke(prompt)
            plot_name = response.content

            plot_name = plot_name.strip().replace('"', "").replace("'", "")
            plot_name = plot_name[:20] if plot_name else f"Plot_{self.plot_count}"
            return plot_name
        except Exception as e:
            return f"Plot_{self.plot_count}"

    def get_visualization_intro(self, query: str, plot_description: str = None):
        if not query:
            return ""

        combined_input = query
        if plot_description:
            combined_input = f"{query} - {plot_description}"

        prompt = f"""
            Generate a short and clear description for the plots based on the following query and description: "{combined_input}".
            The description should introduce the context of the plot's content in a simple, understandable way and should be no 
            longer than 1 sentence.
            Respond with only the description, nothing else.
        """

        try:
            response = self.llm.invoke(prompt)
            plot_desc = response.content

            plot_desc = plot_desc.strip().replace('"', "").replace("'", "")
            return plot_desc
        except Exception as e:
            return f""

    def process_agent_response(self, response: str, query: str) -> str:
        """Process the agent response to identify and name any plots generated."""

        # Look for indicators of plot generation in the response
        plot_indicators = [
            "plt.show()",
            "display plot",
            "generated a plot",
            "created a visualization",
            "fig, ax",
            "created chart",
            "generated graph",
            "visualization of",
        ]

        contains_plot = any(
            indicator.lower() in response.lower() for indicator in plot_indicators
        )

        if contains_plot:
            self.plot_count += 1
            plot_name = self.get_plot_name(query)
            self.plots[self.plot_count] = plot_name

            # Add the plot name information to the response
            response += f"\n\nPlot name: {plot_name}"

        return response

    async def get_clarification_question(
        self, question: str, chat_history: List[str]
    ) -> Dict:
        prompt = CLARIFICATION_QUERY

        parser = JsonOutputParser(pydantic_object=Clarify)
        prompt = PromptTemplate(
            template=prompt,
            input_variables=["question", "summary", "chat_history"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )
        chain = prompt | self.llm | parser
        response = chain.ainvoke(
            {
                "question": question,
                "summary": self.summary,
                "chat_history": chat_history,
            }
        )
        return response

    async def human_clarification(self, question: str) -> Dict:
        chat_history = []
        context = question
        while True:
            clarification_result = await self.get_clarification_question(
                context, chat_history
            )
            if not clarification_result["needs_clarification"]:
                return {
                    "final_question": context,
                    "clarification_history": chat_history,
                }
            current_question = clarification_result["clarification_question"]
            # In an API, simulate clarification by appending an empty string.
            simulated_input = ""
            context += f" {simulated_input}"
            chat_history.append(f"Q: {current_question}")
            chat_history.append(f"A: {simulated_input}")
            # Break after one iteration to avoid infinite loop in API
            return {"final_question": context, "clarification_history": chat_history}

    async def generate_response(
        self, question: str, chat_history: List[str]
    ) -> Tuple[str, int]:
        with get_openai_callback() as cb:
            result = await self.agent.ainvoke({"input": question + str(chat_history)})
            total_tokens_used = cb.total_tokens

        processed_result = self.process_agent_response(str(result["output"]), question)

        encoding = tiktoken.get_encoding("cl100k_base")
        input_tokens = len(encoding.encode(question))
        output_tokens = len(encoding.encode(processed_result))
        estimated_tokens = input_tokens + output_tokens

        final_token_count = max(total_tokens_used, estimated_tokens)

        self.total_tokens_used = final_token_count
        return processed_result, final_token_count

    async def process_question(
        self, question: str, past_messages: List[Dict[str, Any]]
    ) -> Tuple[str, int]:
        if self.df is None:
            return "Please load data first", 0

        formatted_history = []
        for msg in past_messages:
            if "type" in msg and "content" in msg:
                if msg["type"] == "USER":
                    formatted_history.append(f"Human: {msg['content']}")
                elif msg["type"] == "SYSTEM":
                    formatted_history.append(f"AI: {msg['content']}")

        response, total_tokens_used = await self.generate_response(
            question, formatted_history
        )
        relevant_files = self.get_relevant_csv_files(question, response)
        return response, total_tokens_used, relevant_files

    def check_and_deduct_tokens(self, total_tokens_used: int):
        if self.app_limit.tokens_left < total_tokens_used:
            raise HTTPException(status_code=403, detail="Insufficient Credits")

        self.app_limit.tokens_left -= total_tokens_used
        self.db.commit()
        return self.app_limit.tokens_left

    def reset_tokens_if_needed(self):
        now = datetime.utcnow()
        if self.app_limit.last_token_reset + timedelta(days=1) < now:
            self.app_limit.tokens_left = self.app_limit.max_tokens
            self.app_limit.last_token_reset = now
            self.db.commit()
