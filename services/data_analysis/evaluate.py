import ast
import pandas as pd
import matplotlib.pyplot as plt
from typing import Any, Dict
import numpy as np


class CodeEvaluator:
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.metrics = {
            "syntax_valid": False,
            "execution_success": False,
            "output_valid": False,
            "performance_score": 0.0,
        }

    def validate_syntax(self, code: str) -> bool:
        code = code[0] if type(code) != str else code
        try:
            ast.parse(code)
            self.metrics["syntax_valid"] = True
            return True
        except SyntaxError:
            return False

    def evaluate_execution(self, code: str, expected_output: Any) -> Dict:
        code = code[0] if type(code) != str else code
        if not self.validate_syntax(code):
            return self.metrics
        try:
            local_vars = {"df": self.df.copy(), "pd": pd, "np": np, "plt": plt}
            exec(code, {}, local_vars)
            if "result" not in local_vars:
                self.metrics["execution_success"] = False
                return self.metrics
            result = local_vars["result"]
            self.metrics["execution_success"] = True
            if isinstance(expected_output, pd.DataFrame):
                try:
                    self.metrics["output_valid"] = True
                except AssertionError:
                    self.metrics["output_valid"] = False
            else:
                self.metrics["output_valid"] = result == expected_output
            self.metrics["performance_score"] = self._calculate_performance()
            print("====" * 15)
            print(f"Metrics: {self.metrics}")
            print("====" * 15)
            return self.metrics
        except Exception as e:
            self.metrics["execution_success"] = False
            return self.metrics

    def _calculate_performance(self) -> float:
        score = 0.0
        if self.metrics["syntax_valid"]:
            score += 0.3
        if self.metrics["execution_success"]:
            score += 0.4
        if self.metrics["output_valid"]:
            score += 0.3
        return score
