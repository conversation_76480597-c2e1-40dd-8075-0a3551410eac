from services.data_analysis.evaluate import CodeEvaluator
from typing import Any, Dict
import pandas as pd


class FeedbackAgent:
    def __init__(self, df: pd.DataFrame, llm):
        self.df = df
        self.llm = llm
        self.evaluator = CodeEvaluator(df)

    def generate_feedback(self, question: str, code: str, metrics: Dict) -> str:
        prompt = f"""Analyze this code execution result:
        Question: {question}
        Code: {code}
        Metrics: {metrics}

        Provide specific feedback on:
        1. Code correctness
        2. Potential improvements
        3. Alternative approaches if performance is low
        Keep feedback concise and actionable."""
        print("====" * 15)
        print(f"Feedback: \n {self.llm.invoke(prompt).content}")
        print("====" * 15)
        return self.llm.invoke(prompt).content

    def process_response(self, question: str, generated_code: str) -> Dict:
        metrics = self.evaluator.evaluate_execution(generated_code, None)
        feedback = self.generate_feedback(question, generated_code, metrics)
        return {
            "metrics": metrics,
            "feedback": feedback,
            "improved_code": (
                self._improve_code(generated_code, feedback)
                if metrics["performance_score"] < 0.8
                else generated_code
            ),
        }

    def _improve_code(self, original_code: str, feedback: str) -> str:
        prompt = f"""Original code: {original_code}
        Feedback: {feedback}

        Improve the code based on the feedback while maintaining its core functionality.
        Return only the improved code without explanations."""
        return self.llm.invoke(prompt)
