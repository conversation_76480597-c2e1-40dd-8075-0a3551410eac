CLARIFICATION_QUERY = """Given this context:
        - Current question: {question}
        - Data summary: {summary}
        - CHAT_HISTORY: {chat_history}

        Determine if the question needs clarification. Consider:
        1. Be intelligent, don't often ask the clarification question, return needs_clarification as false
        2. Even small hint should be enough, in that case return needs_clarification as false
        3. Is the question clear and specific enough to analyze the data?
        4. Are required columns/metrics specified, if this context is required then ask the clarification question?
        5. Is there missing context that would affect the analysis?
        6. Even if previous question is not answered, you will never repeat the same question from CHAT_HISTORY

        Important:
        - Only ask ONE clarification question at a time, ONLY IF REQUIRED.
        - Don't repeat questions from chat history
        - If the question is out of context or any column information is needed, return needs_clarification as true
        - If the question is clear enough, return needs_clarification as false
        {format_instructions}
        """
