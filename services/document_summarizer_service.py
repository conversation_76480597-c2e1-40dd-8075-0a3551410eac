import asyncio
import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from pathlib import Path
import os

from sqlalchemy.orm import Session
from app.database.models.document_summary import DocumentSummary, SummaryStatus
from app.database.models.document_summary_file import (
    DocumentSummaryFile,
    DocumentSummaryFileStatus,
)
from document_summarizer.main_pipeline import DocumentAnalysisPipeline
from document_summarizer.hyperlink_generator import hyperlink_generator

logger = logging.getLogger(__name__)


class DocumentSummarizerService:
    """Service for handling document summarization processing"""

    def __init__(self):
        self.pipeline = DocumentAnalysisPipeline()

    async def process_document_summary(
        self,
        summary: DocumentSummary,
        files: List[DocumentSummaryFile],
        db: Session,
        custom_prompt: Optional[str] = None,
        important_keywords: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Process a document summary with its uploaded files

        Args:
            summary: DocumentSummary instance to process
            files: List of DocumentSummaryFile instances
            db: Database session
            custom_prompt: Optional custom prompt for summarization
            important_keywords: Optional important keywords for analysis

        Returns:
            Dict containing processing results
        """
        try:
            # Clear previous hyperlink registry for this processing session
            hyperlink_generator.clear_registry()

            # Update summary status to processing
            summary.status = SummaryStatus.IN_PROGRESS.value
            db.commit()

            # Validate file paths exist
            document_paths = []
            file_metadata = {}  # Map file paths to file metadata
            invalid_files = []
            processed_file_ids = []  # Initialize here

            for file in files:
                logger.info(f"Processing file status: {file.status}")
                # Process files that are either in QUEUE or PROCESSING status (for retry scenarios)
                if file.status in [DocumentSummaryFileStatus.QUEUE.value, DocumentSummaryFileStatus.PROCESSING.value]:
                    logger.info(f"Processing file {file.id} for summary {summary.id}")
                    logger.info(f"Checking file path: {file.path}")

                    if not file.path:
                        logger.error(f"No file path stored for file {file.id}")
                        file.status = DocumentSummaryFileStatus.FAILED.value
                        invalid_files.append(file)
                    elif not os.path.exists(file.path):
                        logger.error(f"File not found at path: {file.path} for file {file.id}")
                        # Check if directory exists
                        dir_path = os.path.dirname(file.path)
                        logger.error(f"Directory exists: {os.path.exists(dir_path)}")
                        if os.path.exists(dir_path):
                            logger.error(f"Directory contents: {os.listdir(dir_path)}")
                        file.status = DocumentSummaryFileStatus.FAILED.value
                        invalid_files.append(file)
                    else:
                        logger.info(f"File found successfully at: {file.path}")
                        document_paths.append(file.path)
                        file_metadata[file.path] = {
                            "file_id": str(file.id),
                            "file_name": file.name,
                            "file_type": file.type
                        }
                        file.status = DocumentSummaryFileStatus.PROCESSING.value
            db.commit()
            
            logger.info(f"Summary meta data is {summary.meta_data}")
            if summary.meta_data:
                if summary.meta_data.get("processed_files"):
                    if int(summary.meta_data.get("processed_files")) > 0:
                        processed_file_ids = [str(file.id) for file in files if file.status == DocumentSummaryFileStatus.SUCCESS.value and file.is_deleted == False]
                    
            # If no valid files, mark summary as failed
            if not document_paths and not processed_file_ids:
                summary.status = SummaryStatus.FAILED.value
                summary.meta_data = {
                    "error": "No valid file paths found",
                    "invalid_files": len(invalid_files),
                    "total_files": len(files)
                }
                db.commit()
                
                return {
                    "summary_id": str(summary.id),
                    "status": "failed",
                    "error": "No valid file paths found",
                    "processed_files": len(files) - len(invalid_files),
                    "total_files": len(files)
                }

            # Run the analysis pipeline
            logger.info(f"Processing {len(document_paths)} documents for summary {summary.id}")
            
            # Use collection_id directly instead of relying on actual_collection_id
            summary_text = await self.pipeline.run_analysis(
                document_paths=document_paths if document_paths else None,
                user_id=str(summary.user_id),
                collection_id=str(summary.id),
                output_instructions_prompt=custom_prompt,
                important_keywords=important_keywords,
                file_metadata=file_metadata,
            )

            # Process the summary text and update database
            if summary_text and summary_text.strip():
                # Calculate total processed files (new + existing)
                total_processed_files = len(document_paths) + (len(processed_file_ids) if processed_file_ids else 0)
                
                # Get hyperlink data
                hyperlink_data = hyperlink_generator.get_all_hyperlinks()

                # Update summary with successful results
                summary.summary = summary_text
                summary.status = SummaryStatus.COMPLETED.value
                summary.meta_data = {
                    "processed_files": total_processed_files,
                    "total_files": len(files),
                    "invalid_files": len(invalid_files),
                    "processing_completed": True,
                    "summary_length": len(summary_text),
                    "hyperlinks_count": len(hyperlink_data),
                    "hyperlinks": hyperlink_data
                }

                # Update successful file statuses (only for newly processed files)
                successful_files = []
                for file in files:
                    if file not in invalid_files:
                        # Only update status for files that were actually processed in this run
                        if file.path in document_paths:
                            file.status = DocumentSummaryFileStatus.SUCCESS.value
                            file.meta_data = {
                                "processed": True,
                                "file_type": file.type,
                                "file_size": file.size,
                            }
                        
                        # Include all successful files in response (new + existing)
                        if file.status == DocumentSummaryFileStatus.SUCCESS.value:
                            successful_files.append({
                                "file_id": str(file.id),
                                "file_name": file.name,
                                "status": "success"
                            })

                db.commit()

                return {
                    "summary_id": str(summary.id),
                    "status": "completed",
                    "summary": summary_text,
                    "processed_files": successful_files,
                    "total_files": len(files),
                    "invalid_files": len(invalid_files),
                    "hyperlinks": hyperlink_data
                }

            else:
                # Handle failed analysis - empty or no summary generated
                error_message = "No summary text generated by analysis pipeline"
                
                summary.status = SummaryStatus.FAILED.value
                summary.meta_data = {
                    "error": error_message,
                    "processed_files": 0,
                    "total_files": len(files),
                    "invalid_files": len(invalid_files),
                    "analysis_failed": True
                }

                # Update all valid files to failed status
                for file in files:
                    if file not in invalid_files:
                        file.status = DocumentSummaryFileStatus.FAILED.value
                        file.meta_data = {"error": error_message}

                db.commit()

                return {
                    "summary_id": str(summary.id),
                    "status": "failed",
                    "error": error_message,
                    "processed_files": 0,
                    "total_files": len(files),
                }

        except Exception as e:
            logger.error(f"Error processing document summary {summary.id}: {e}")

            # Update summary status to failed
            summary.status = SummaryStatus.FAILED.value
            summary.meta_data = {
                "error": str(e),
                "exception_occurred": True,
                "total_files": len(files)
            }

            # Update all file statuses to failed
            for file in files:
                if file.status == DocumentSummaryFileStatus.PROCESSING.value:
                    file.status = DocumentSummaryFileStatus.FAILED.value
                    file.meta_data = {"error": str(e)}

            db.commit()
            raise e


# Global service instance
document_summarizer_service = DocumentSummarizerService()
