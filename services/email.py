import boto3
import jwt
import logging
from pathlib import Path
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BackgroundTasks, status, Request
from fastapi.responses import RedirectResponse, JSONResponse
from datetime import datetime, timedelta
from uuid import UUID
from typing import Dict
from sqlalchemy.orm import Session
from app.core.config import settings
from app.database.models.user import User


class EmailService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.ses_client = boto3.client(
                "ses",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_DEFAULT_REGION,
            )
            self.jwt_secret = settings.SECRET_KEY
            self.token_expire_minutes = 15
            self.sender_email = settings.SENDER_EMAIL
            # Get the root directory (where services/email.py is)
            root_dir = Path(__file__).parent.parent

            # Construct path to template in app/constants
            self.email_verification_template_path = (
                root_dir / "app" / "constants" / "email_verification.html"
            )
            self._initialized = True

    def construct_verification_url(self, origin: Request, token: str) -> str:
        """Construct the verification URL based on settings"""
        # Construct the reset URL
        url = f"{origin}/verify-email?token={token}"
        logging.info(f"Email verification url for user. {url}")
        return url

    async def send_verification_email(
        self, origin: str, email: str, token: str
    ) -> None:
        """Send verification email using AWS SES"""
        verification_link = self.construct_verification_url(origin, token)

        with open(self.email_verification_template_path, "r", encoding="utf-8") as file:
            template_content = file.read()

        # Format the template
        formatted_email = template_content.format(
            verification_link=verification_link,
            self_token_expire_minutes=self.token_expire_minutes,
        )

        logging.info("Send verification email using AWS SES")
        try:
            response = self.ses_client.send_email(
                Source=self.sender_email,
                Destination={"ToAddresses": [email]},
                Message={
                    "Subject": {"Data": "Verify Your Email Address"},
                    "Body": {"Html": {"Data": formatted_email}},
                },
            )
            logging.info(f"Email sent! Message ID: {response['MessageId']}")
        except Exception as e:
            logging.info(f"Error sending email: {str(e)}")

    def create_email_verification_token(self, email: str, user_id: str) -> str:
        """Create JWT token with expiration"""
        expiration = datetime.utcnow() + timedelta(minutes=self.token_expire_minutes)
        return jwt.encode(
            {"email": email, "user_id": user_id, "exp": expiration},
            self.jwt_secret,
            algorithm=settings.ALGORITHM,
        )

    def verify_email_token(self, token: str) -> Dict:
        """Verify JWT token and return payload"""
        try:
            payload = jwt.decode(
                token, self.jwt_secret, algorithms=[settings.ALGORITHM]
            )
            return payload
        except jwt.ExpiredSignatureError:
            logging.error("Verification link has expired")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification link has expired",
            )
        except jwt.PyJWTError:
            logging.error("Invalid verification token")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification token",
            )

    def initiate_verification(
        self, origin: str, email: str, user_id: str, background_tasks: BackgroundTasks
    ) -> Dict:
        """Initiate the email verification process"""
        token = self.create_email_verification_token(email, user_id)
        background_tasks.add_task(self.send_verification_email, origin, email, token)
        return {"message": "Verification email sent successfully"}

    async def handle_verification(self, token: str, db: Session) -> RedirectResponse:
        """Handle email verification when user clicks the link"""
        try:
            payload = self.verify_email_token(token)
            logging.info(
                f"Extract details from token: {payload['user_id']} & {payload['email']}"
            )
            user = (
                db.query(User)
                .filter(
                    User.id == UUID(payload["user_id"]),
                    User.email == payload["email"],
                    User.is_deleted == False,
                )
                .first()
            )
            if user:
                user.is_verified = True
                db.commit()
                logging.info(f"{payload['user_id']} email verified")
                return JSONResponse(
                    content={"message": "Email verified successfully."}, status_code=200
                )
            logging.error(f"No user exists with this email {payload['email']}")
            raise HTTPException(
                status_code=404,
                detail=f"No user exists with this email {payload['email']}",
            )
        except HTTPException as http_exc:
            # Catch FastAPI's HTTPException and return a JSON response
            return JSONResponse(
                content={"error": http_exc.detail}, status_code=http_exc.status_code
            )

        except Exception as e:
            # Handle unexpected exceptions and return a 500 response
            logging.error(f"An unexpected error occurred: {str(e)}")
            return JSONResponse(
                content={"error": "An internal server error occurred."}, status_code=500
            )


# Create a singleton instance
email_service = EmailService()


# Prevent creating new instances
def get_email_service() -> EmailService:
    return email_service
