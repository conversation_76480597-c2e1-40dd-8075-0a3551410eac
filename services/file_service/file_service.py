from app.database.models.files import File
from sqlalchemy.orm import Session
import os
from io import BytesIO
from services.pdf_parser.s3_service import S3Handler


class FileService:
    def __init__(self, db: Session):
        self.db = db

    def store_csv_file(self, folder_id: str, local_csv_path: str) -> File:
        s3 = S3Handler()
        file_name = os.path.basename(local_csv_path)
        with open(local_csv_path, "rb") as f:
            file_bytes = BytesIO(f.read())
        s3_url = s3.upload_file(file_bytes, file_name)
        new_file = File(
            name=file_name, type="csv", folderid=folder_id, url=s3_url, status="SUCCESS"
        )
        self.db.add(new_file)
        self.db.commit()
        self.db.refresh(new_file)
        return new_file
