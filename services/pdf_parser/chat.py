from uuid import UUID
from aiohttp import Client<PERSON>rror
from fastapi import HTTPException
import pandas as pd
import requests
from sqlalchemy.orm import Session
from app.database.models.chats import ChatPlot, ChatSession, ChatMessage
from app.database.models.agentbot import AgentBot
from app.core.config import settings
import google.generativeai as genai
from datetime import datetime, timedelta
from app.database.models.files import File
from app.database.models.user import User
from app.database.models.application_limits import ApplicationLimit
from typing import List, Dict, Any
import json

from services.pdf_parser.s3_service import GetPresignedUrl, S3Handler
from io import StringIO


class ChatService:
    def __init__(self, db: Session, user_id: UUID, agent_id: str):
        self.db = db
        self.user_id = user_id
        self.agent_id = UUID(agent_id)
        self.session = None
        self.app_limit = self.get_application_limit()
        # google generative ai initialize
        self.gemini = genai.configure(api_key=settings.GOOGLE_API_KEY)
        self.gemini_model = genai.GenerativeModel("gemini-1.5-flash")
        self.user = self.db.query(User).filter(User.id == self.user_id).first()
        self.reset_tokens_if_needed()

        # Initialize Weaviate client and vector store
        # self.client = weaviate.Client(
        #     url=settings.WEAVIATE_URL,
        #     auth_client_secret=weaviate.AuthApiKey(settings.WEAVIATE_API_KEY),
        # )
        # self.embeddings = HuggingFaceInferenceAPIEmbeddings(
        #     api_url=settings.HUGGINGFACE_API_URL,
        #     api_key=settings.HUGGINGFACE_API_KEY,
        #     model_name=settings.MODEL_NAME,
        # )

        # self.llm = AzureChatOpenAI(
        #     azure_deployment="intern-gpt4",
        #     api_version="2023-05-15",
        #     temperature=0.3,
        #     azure_endpoint="https://internkey.openai.azure.com/",
        #     openai_api_key=settings.AZUREOPENAI_API_KEY,
        # )

    def get_application_limit(self):
        """Fetch the application limit details for the given user_id."""
        app_limit = (
            self.db.query(ApplicationLimit)
            .filter(ApplicationLimit.userid == self.user_id)
            .first()
        )
        if not app_limit:
            raise HTTPException(status_code=404, detail="Application limit not found")
        return app_limit

    def reset_tokens_if_needed(self):
        """Reset the token count if 24 hours have passed since the last reset."""
        now = datetime.utcnow()
        if self.app_limit.last_token_reset + timedelta(days=1) < now:
            self.app_limit.tokens_left = (
                self.app_limit.max_tokens
            )  # Reset to default value
            self.app_limit.last_token_reset = now
            self.db.commit()  # Persist changes to DB

    def check_and_deduct_tokens(self, response: str, total_token_used: int):
        """Check if the user has enough tokens for the response and deduct the used tokens."""
        if self.app_limit.tokens_left < total_token_used:
            raise HTTPException(status_code=403, detail="Insufficient Credits")

        # Deduct the tokens
        self.app_limit.tokens_left -= total_token_used
        self.db.commit()  # Persist changes to DB
        return self.app_limit.tokens_left

    def get_session_name(self, query: str):
        """Generates a simple, clear, and short chat session name based on the first query."""

        if not query:
            raise HTTPException(status_code=400, detail="First query not provided.")

        # Improved prompt for a clear and relevant session name
        prompt = f"""
            Generate a short and clear chat session name based on the following query: "{query}".
            The name should be simple, understandable, and no longer than 20 characters.
            Respond with only the name, nothing else.
        """

        try:
            response = self.gemini_model.generate_content(prompt)
            chat_name = response.candidates[0].content.parts[0].text
            chat_name = chat_name[:20] if chat_name else "Chat Session"
            return chat_name
        except Exception as e:

            raise HTTPException(status_code=500, detail="Failed to generate chat name")

    def get_or_create_session(self, session_id: str = None, first_query: str = None):
        agentbot = self.db.query(AgentBot).filter(AgentBot.id == self.agent_id).first()
        if not agentbot:
            raise HTTPException(status_code=404, detail="Agentbot not found")
        try:
            if session_id:
                # Attempt to retrieve the specific session by ID
                self.session = (
                    self.db.query(ChatSession)
                    .filter(
                        ChatSession.id == session_id,
                        ChatSession.userid == self.user_id,
                        ChatSession.agentid == self.agent_id,
                    )
                    .first()
                )
                if not self.session:
                    raise HTTPException(
                        status_code=404, detail="Chat session not found"
                    )
            else:

                if not self.session:

                    self.session = ChatSession(
                        name="New Chat", userid=self.user_id, agentid=self.agent_id
                    )
                    self.db.add(self.session)
                    self.db.commit()
                    self.db.refresh(self.session)

            return self.session

        except HTTPException as http_err:
            # Re-raise known HTTP exceptions for proper API responses
            raise http_err

        except Exception as e:
            # Log the unexpected exception and raise a generic HTTPException
            raise HTTPException(
                status_code=500, detail="Failed to retrieve or create chat session"
            )

    def update_session_name(self, session_id: UUID, new_name: str):
        """Update the name of the chat session."""
        try:
            self.session = (
                self.db.query(ChatSession)
                .filter(
                    ChatSession.id == session_id,
                )
                .first()
            )
            if not self.session:
                raise HTTPException(status_code=404, detail="Chat session not found")

            self.session.name = new_name
            self.db.commit()
            return self.session

        except HTTPException as http_err:
            # Re-raise known HTTP exceptions for proper API responses
            raise http_err

        except Exception as e:
            # Log the unexpected exception and raise a generic HTTPException
            raise HTTPException(
                status_code=500, detail="Failed to update chat session name"
            )

    def get_s3_last_modified(self, file_key: str):
        """Get last modified timestamp from S3."""
        try:
            s3client = S3Handler().s3_client
            response = s3client.head_object(
                Bucket=settings.AWS_BUCKET_NAME, Key=file_key
            )
            return response["LastModified"]
        except ClientError as e:
            raise HTTPException(status_code=500, detail=f"S3 Error: {e}")

    def get_csv_data_for_folder(self, folder_id: UUID):
        csv_files = (
            self.db.query(File)
            .filter(
                File.folderid == folder_id,
                File.type == "text/csv",
                File.is_deleted == False,
            )
            .all()
        )
        presigned_url_handler = GetPresignedUrl()

        if not csv_files:
            raise HTTPException(
                status_code=404, detail="No CSV files found for this folder."
            )

        all_data = []
        for file_record in csv_files:
            file_key = f"uploads/{folder_id}/{file_record.name}"
            s3_last_modified = self.get_s3_last_modified(file_key)

            # if file_record.updated_at and file_record.updated_at >= s3_last_modified:
            #     all_data.append(pd.DataFrame(file_record.csv_data))
            # else:
            presigned_url = presigned_url_handler.get_url(
                folderid=folder_id, filename=file_record.name
            )
            resp = requests.get(presigned_url)
            resp.raise_for_status()

            df = pd.read_csv(StringIO(resp.text))
            df = df.where(pd.notnull(df), None)  # Convert real NaNs to None
            df = df.replace("NaN", None)  # Convert string "NaN" to None
            df = df.replace({float("nan"): None})  # Redundant safety for float NaNs
            file_record.csv_data = df.where(pd.notnull(df), None).to_dict(
                orient="records"
            )
            file_record.updated_at = s3_last_modified
            self.db.commit()
            all_data.append(df)

        return pd.concat(all_data, ignore_index=True), csv_files

    def get_past_messages(
        self, session_id: UUID, limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve the last few messages from the session and process SYSTEM messages accordingly."""
        past_messages = (
            self.db.query(ChatMessage)
            .filter(ChatMessage.session_id == session_id)
            .order_by(ChatMessage.created_at.desc())
            .limit(limit)
            .all()
        )

        structured_messages = []
        for message in past_messages:
            if message.type == "SYSTEM":
                try:
                    system_content = (
                        json.loads(message.content)
                        if isinstance(message.content, str)
                        else message.content
                    )

                    if isinstance(system_content, dict):
                        structured_content = {
                            "summary": system_content.get("summary", ""),
                            "details": [
                                {"answer": detail["answer"]}
                                for detail in system_content.get("details", [])
                            ],
                        }
                    elif isinstance(system_content, list):
                        structured_content = {"summary": "", "details": system_content}
                    else:
                        structured_content = {
                            "summary": str(system_content),
                            "details": [],
                        }

                    structured_messages.append(
                        {"type": "SYSTEM", "content": structured_content}
                    )
                except json.JSONDecodeError:
                    structured_messages.append(
                        {"type": message.type, "content": message.content}
                    )
            else:

                structured_messages.append(
                    {"type": message.type, "content": message.content}
                )

        return structured_messages

    def log_message(
        self,
        message_type: str,
        session_id: UUID,
        content,
        metadata_texts,
        plots: List[dict] = [],
    ):
        content = json.dumps(content , ensure_ascii=False)
        metadata_texts = json.dumps(metadata_texts, ensure_ascii=False)

        # Create the message object
        message = ChatMessage(
            session_id=session_id,
            type=message_type,
            content=content,
            metadata_text=metadata_texts,
        )
        self.db.add(message)
        self.db.commit()  # Commit first to generate message.id

        # Create and link ChatPlot instances
        plot_instances = [
            ChatPlot(message_id=message.id, url=plot["url"], title=plot.get("title"))
            for plot in plots
        ]

        self.db.add_all(plot_instances)
        self.db.commit()
        self.db.refresh(message)

        return message
