import requests
from fastapi import HTTPException

class FileUploadService:
    @staticmethod
    def upload_file_to_presigned_url(presigned_url: str, fields: dict, file: bytes, file_name: str):
        """
        Uploads a file to the provided presigned URL.

        :param presigned_url: URL to upload the file.
        :param fields: Additional fields required for the presigned POST.
        :param file: File content in bytes.
        :param file_name: Name of the file being uploaded.
        :return: Response from the presigned URL upload.
        """
        try:
            # Prepare the multipart form data
            files = {
                "file": (file_name, file, "application/pdf"),
            }
            response = requests.post(presigned_url, data=fields, files=files)

            # Check for upload success
            if response.status_code != 204:  # AWS S3 returns 204 on successful upload
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"File upload failed: {response.text}"
                )

            return {"message": "File uploaded successfully"}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")
