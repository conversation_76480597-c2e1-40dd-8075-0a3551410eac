from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.docstore.document import Document
from langchain_community.embeddings import HuggingFaceInferenceAPIEmbeddings
from langchain_community.vectorstores import Weaviate
from llama_parse import LlamaParse
from llama_index.core import SimpleDirectoryReader
import weaviate
from app.core.config import settings
from fastapi import HTTPException
import os
from typing import List
import nest_asyncio
from services.pdf_parser.utils import sanitize_class_name
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

nest_asyncio.apply()

class PDFProcessor:
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Weaviate Authentication
        try:
            if not settings.WEAVIATE_API_KEY:
                raise ValueError("WEAVIATE_API_KEY is missing in settings.")
            self.auth_config = weaviate.auth.AuthApiKey(settings.WEAVIATE_API_KEY)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Weaviate authentication error: {str(e)}")

        # Initialize HuggingFace Embeddings
        try:
            if not settings.HUGGINGFACE_API_KEY or not settings.MODEL_NAME:
                raise ValueError("HUGGINGFACE_API_KEY or MODEL_NAME is missing in settings.")
            self.embedding_model = HuggingFaceInferenceAPIEmbeddings(
                api_key=settings.HUGGINGFACE_API_KEY,
                model_name=settings.MODEL_NAME
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"HuggingFace model initialization error: {str(e)}")
        try:
            if not settings.LLAMAINDEX_API:
                raise ValueError("LLAMAINDEX_API is missing in settings.")
            self.parser = LlamaParse(api_key=settings.LLAMAINDEX_API, result_type="text")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"LlamaParse initialization error: {str(e)}")


        # Initialize Weaviate Client
        try:
            if not settings.WEAVIATE_URL:
                raise ValueError("WEAVIATE_URL is missing in settings.")
            self.weaviate_client = weaviate.Client(
                url=settings.WEAVIATE_URL,
                auth_client_secret=self.auth_config
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Weaviate client initialization error: {str(e)}")


    def _chunk_documents(self, documents: List[Document], file_path: str, file_name:str) -> List[Document]:
        try:
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap
            )
            chunked_docs = []
            for doc_index, doc in enumerate(documents):
                content = doc.page_content
                if not content.strip():
                    continue

                # Add page number if available
                page_number = doc.metadata.get("page_number", doc_index)

                chunks = text_splitter.split_text(content)

                for chunk_index, chunk in enumerate(chunks):
                    chunked_docs.append(Document(
                        page_content=chunk,
                        metadata={
                            **doc.metadata,
                            "source": file_path,
                            "original_file_name": file_name,
                            "chunk_index": chunk_index,
                            "document_index": doc_index,
                            "page_number": page_number
                        }
                    ))
            return chunked_docs
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error chunking documents: {str(e)}")

    def _validate_and_sanitize_metadata(self, documents: List[Document]) -> List[Document]:
        try:
            sanitized_docs = []
            for doc in documents:
                metadata = doc.metadata or {}
                sanitized_metadata = {
                    "source": metadata.get("source", "Unknown source"),
                    "original_file_name": metadata.get("original_file_name", "Unknown file"),
                    "chunk_index": int(metadata.get("chunk_index", 0)),
                    "document_index": int(metadata.get("document_index", 0))
                }
                sanitized_docs.append(
                    Document(page_content=doc.page_content, metadata=sanitized_metadata)
                )
            return sanitized_docs
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error sanitizing metadata: {str(e)}")

    async def process_and_store(self, file_path: str, folder_id: str, current_user_paid: bool, file_org_name:str) -> None:
        try:
            if not os.path.isfile(file_path):
                raise HTTPException(status_code=404, detail=f"File not found: {file_path}")

            # Validate file size (max 4MB)
            if os.path.getsize(file_path) > 4 * 1024 * 1024:  # 4 MB
                raise HTTPException(status_code=400, detail="File size exceeds the 4MB limit.")

            user_paid = current_user_paid

            # Parse PDF using the appropriate method
            documents = await self._parse_pdf(file_path, user_paid)

            if not documents:
                raise HTTPException(status_code=400, detail="No documents found in the provided file.")

            # Chunk documents
            chunked_docs = self._chunk_documents(documents, file_path,file_name=file_org_name )
            if not chunked_docs:
                raise HTTPException(status_code=400, detail="No valid chunks generated from the document.")

            # Validate and sanitize metadata
            chunked_docs = self._validate_and_sanitize_metadata(chunked_docs)

            # Store in Weaviate
            self._store_in_weaviate(chunked_docs, folder_id)

        except HTTPException as he:
            raise he
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {str(e)}")

    async def _parse_pdf(self, file_path: str, user_paid: bool) -> List[Document]:
        try:
            if user_paid:
                # Use LlamaIndex for paid users
                logger.info("Using LlamaIndex for parsing PDF...")
                response = SimpleDirectoryReader(
                    input_files=[file_path],
                    file_extractor={".pdf": self.parser}
                ).load_data()

                documents = [
                    Document(page_content=doc.text, metadata={"page_number": idx + 1})
                    for idx, doc in enumerate(response)
                    if doc.text.strip() and doc.text != "NO_CONTENT_HERE"
                  
                  
                ]

            else:
                # Use PyPDFLoader for non-paid users
                logger.info("Using PyPDFLoader for parsing PDF...")
                loader = PyPDFLoader(file_path)
                documents = loader.load_and_split()

            return documents

        except FileNotFoundError:
            raise HTTPException(status_code=404, detail=f"File not found at path: {file_path}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to parse file: {str(e)}")

    def _store_in_weaviate(self, documents: List[Document], folder_id: str) -> None:
        class_name = sanitize_class_name(str(folder_id))
        # print(class_name)
        try:
            schema = self.weaviate_client.schema.get()
            existing_classes = [cls['class'] for cls in schema.get('classes', [])]

            if class_name not in existing_classes:
                self.weaviate_client.schema.create_class({
                    "class": class_name,
                    "properties": [
                        {"name": "text", "dataType": ["text"]},
                        {"name": "source", "dataType": ["string"]},
                        {"name": "original_file_name", "dataType": ["string"]},
                        {"name": "chunk_index", "dataType": ["int"]},
                        {"name": "document_index", "dataType": ["int"]}
                    ]
                })

            Weaviate.from_documents(
                documents=documents,
                embedding=self.embedding_model,
                client=self.weaviate_client,
                index_name=class_name
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error while storing documents in Weaviate: {str(e)}")
