import boto3
from botocore.exceptions import Client<PERSON>rror
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from app.core.config import settings
import os
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class S3Handler:
    def __init__(self):
        try:
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_DEFAULT_REGION,
                config=boto3.session.Config(
                    signature_version="s3v4", connect_timeout=10, read_timeout=10
                ),
            )
            self.bucket_name = settings.AWS_BUCKET_NAME
            logger.info("S3 client initialized")
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to initialize S3 client: {str(e)}"
            )

    def check_folder_exists(self, folder_id: str) -> bool:
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name, Prefix=f"uploads/{folder_id}/", MaxKeys=1
            )
            return "Contents" in response
        except ClientError as e:
            raise HTTPException(
                status_code=500, detail=f"Error checking S3 folder: {str(e)}"
            )

    def upload_file(
        self,
        file_path: str,
        folder_id: str,
        file_name: str,
    ) -> str:
        try:
            s3_key = f"uploads/{folder_id}/{file_name}"

            # First, check if file exists locally
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Local file not found: {file_path}")

            # Print debugging information
            logger.info(f"Attempting to upload file:")
            # print(f"File path: {file_path}")
            # print(f"Bucket: {self.bucket_name}")
            # print(f"S3 key: {s3_key}")

            # Try to upload
            self.s3_client.upload_file(file_path, self.bucket_name, s3_key)

            # Generate the public URL
            public_url = f"https://{self.bucket_name}.s3.{settings.AWS_DEFAULT_REGION}.amazonaws.com/{s3_key}"

            return public_url

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            logger.info(
                f"S3 Error Code: {error_code} , S3 Error Message: {error_message}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Error uploading to S3: {error_code} - {error_message}",
            )
        except Exception as e:

            raise HTTPException(
                status_code=500, detail=f"Failed to upload {file_path} to S3: {str(e)}"
            )

    def delete_local_file(self, file_path: str) -> None:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            logger.info(f"Warning: Failed to delete local file {file_path}: {str(e)}")


class GetPresignedUrl:
    def __init__(self):
        try:
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_DEFAULT_REGION,
                config=boto3.session.Config(
                    signature_version="s3v4", connect_timeout=10, read_timeout=10
                ),
            )
            self.bucket_name = settings.AWS_BUCKET_NAME
            logger.info("S3 client initialized")
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to initialize S3 client: {str(e)}"
            )

    def get_url(
        self,
        folderid: str,
        filename: str,
        expiration_seconds: int = 1800,
    ):
        try:
            s3_key_url = f"uploads/{folderid}/{filename}"
            s3_url = self.s3_client.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": self.bucket_name,
                    "Key": s3_key_url,
                    "ResponseContentType": "application/pdf",
                    "ResponseContentDisposition": 'inline; filename="preview.pdf"',
                },
                ExpiresIn=expiration_seconds,
            )
            return s3_url
        except Exception as e:

            raise HTTPException(
                status_code=500, detail=f"Error generating presigned URL:{e}"
            )
