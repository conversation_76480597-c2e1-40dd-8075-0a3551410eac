# # Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
# # SPDX-License-Identifier: Apache-2.0
# """
# Shows how to generate an embedding with the Amazon Titan Text Embeddings V2 Model
# """

# import json
# import logging
# import boto3
# import os
# from dotenv import load_dotenv
# load_dotenv()

# from botocore.exceptions import ClientError


# logger = logging.getLogger(__name__)
# logging.basicConfig(level=logging.INFO)


# def generate_embedding(model_id, body):
#     """
#     Generate an embedding with the vector representation of a text input using Amazon Titan Text Embeddings G1 on demand.
#     Args:
#         model_id (str): The model ID to use.
#         body (str) : The request body to use.
#     Returns:
#         response (JSON): The embedding created by the model and the number of input tokens.
#     """

#     logger.info("Generating an embedding with Amazon Titan Text Embeddings V2 model %s", model_id)

#     bedrock = boto3.client(service_name='bedrock-runtime', region_name=os.environ.get("BEDROCK_REGION"), aws_access_key_id=os.environ.get("BEDROCK_ACCESS_KEY"), aws_secret_access_key=os.environ.get("BEDROCK_SECRET_KEY"))

#     accept = "application/json"
#     content_type = "application/json"

#     response = bedrock.invoke_model(
#         body=body, modelId=model_id, accept=accept, contentType=content_type
#     )

#     response_body = json.loads(response.get('body').read())

#     return response_body


# def main():
#     """
#     Entrypoint for Amazon Titan Embeddings V2 - Text example.
#     """

#     logging.basicConfig(level=logging.INFO,
#                         format="%(levelname)s: %(message)s")

#     model_id = "amazon.titan-embed-text-v2:0"
#     input_text = "What are the different services that you offer?"


#     # Create request body.
#     body = json.dumps({
#         "inputText": input_text,
#         "embeddingTypes": ["binary"]
#     })


#     try:

#         response = generate_embedding(model_id, body)

#         print(f"Generated an embedding: {response['embeddingsByType']['binary']}") # returns binary embedding
#         print(f"Input text: {input_text}")
#         print(f"Input Token count:  {response['inputTextTokenCount']}")

#     except ClientError as err:
#         message = err.response["Error"]["Message"]
#         logger.error("A client error occurred: %s", message)
#         print("A client error occured: " +
#               format(message))

#     else:
#         print(f"Finished generating an embedding with Amazon Titan Text Embeddings V2 model {model_id}.")


# if __name__ == "__main__":
#     main()


import os
import logging
import subprocess
import sys
import argparse
import glob
import hashlib
from typing import List, Dict, Any, Optional
import re
from pathlib import Path
import json
import requests
import time
import boto3
from botocore.exceptions import ClientError
import numpy as np
import aioboto3
import asyncio

from dotenv import load_dotenv
load_dotenv()
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('political_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class BedrockClaudeLLM:
    """Custom LLM class for AWS Bedrock Claude using aioboto3"""
    
    def __init__(self, model_id: str = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"):
        self.model_id = model_id
        # Store AWS credentials from environment or default
        self.region_name = os.environ.get("BEDROCK_REGION")
        self.aws_access_key_id = os.environ.get("BEDROCK_ACCESS_KEY")
        self.aws_secret_access_key = os.environ.get("BEDROCK_SECRET_KEY")
        
        # Test connection
        try:
            self._test_connection()
            logger.info(f"✅ AWS Bedrock Claude LLM initialized for region: {self.region_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AWS Bedrock Claude LLM: {e}")
            raise e
    
    def _test_connection(self):
        """Test Bedrock connection synchronously"""
        try:
            client = boto3.client(
                service_name='bedrock-runtime',
                region_name=self.region_name,
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
            )
            logger.info("✅ Bedrock Claude LLM client test successful")
        except Exception as e:
            logger.error(f"❌ Bedrock Claude LLM connection test failed: {e}")
            raise e
    
    def _create_bedrock_request_body(self, messages: List[Dict], temperature: float = 0.3, max_tokens: int = 4000) -> Dict:
        """Create request body for Anthropic Claude models"""
        # Convert messages to Claude format
        claude_messages = []
        system_message = ""
        
        for message in messages:
            role, content = message
            if role == "system":
                system_message = content
            elif role == "human" or role == "user":
                claude_messages.append({"role": "user", "content": content})
            elif role == "assistant":
                claude_messages.append({"role": "assistant", "content": content})
        
        request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "temperature": temperature,
            "max_tokens": max_tokens,
            "messages": claude_messages
        }
        
        # Add system message if present
        if system_message:
            request_body["system"] = system_message
            
        return request_body
    
    async def ainvoke_async(self, messages: List[tuple], temperature: float = 0.3, max_tokens: int = 4000, max_retries: int = 2) -> Dict:
        """Invoke Claude model asynchronously with retry logic"""
        session = aioboto3.Session()
        
        for attempt in range(max_retries + 1):
            try:
                async with session.client(
                    service_name='bedrock-runtime',
                    region_name=self.region_name,
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                ) as client:
                    
                    # Prepare request body
                    request_body = self._create_bedrock_request_body(messages, temperature, max_tokens)
                    
                    # Call Bedrock API
                    response = await client.invoke_model(
                        modelId=self.model_id,
                        contentType="application/json",
                        accept="application/json",
                        body=json.dumps(request_body)
                    )
                    
                    # Parse response
                    response_body = json.loads(await response['body'].read())
                    
                    # Extract content
                    content = response_body['content'][0]['text']
                    
                    # Create response object similar to Azure OpenAI format
                    return {
                        "content": content,
                        "usage": {
                            "input_tokens": response_body.get('usage', {}).get('input_tokens', 0),
                            "output_tokens": response_body.get('usage', {}).get('output_tokens', 0),
                            "total_tokens": response_body.get('usage', {}).get('input_tokens', 0) + response_body.get('usage', {}).get('output_tokens', 0)
                        },
                        "stop_reason": response_body.get('stop_reason', 'end_turn')
                    }
                    
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                
                if error_code == 'ThrottlingException' and attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"⚠️ Rate limit hit (attempt {attempt + 1}/{max_retries + 1}), waiting {wait_time}s")
                    await asyncio.sleep(wait_time)
                    continue
                elif error_code == 'ValidationException':
                    logger.error(f"❌ Invalid request: {e}")
                    raise e
                elif error_code == 'AccessDeniedException':
                    logger.error(f"❌ Access denied - check permissions: {e}")
                    raise e
                else:
                    logger.error(f"❌ AWS Bedrock API error: {e}")
                    if attempt < max_retries:
                        await asyncio.sleep(1)
                        continue
                    raise e
                    
            except Exception as e:
                logger.error(f"❌ Error invoking Claude model: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(2)
                    continue
                raise e
        
        raise Exception("Maximum retries exceeded")
    
    def ainvoke(self, messages: List[tuple], **kwargs) -> Dict:
        """Synchronous wrapper for async invoke"""
        return asyncio.run(self.ainvoke_async(messages, **kwargs))
    

# test claude bedrock
claude_bedrock_client = BedrockClaudeLLM(
    model_id="us.anthropic.claude-3-7-sonnet-20250219-v1:0",
)

messages = [("user", "Hello, how are you?")]
response = claude_bedrock_client.ainvoke(messages, temperature=0.3, max_tokens=4000)
print(response)
