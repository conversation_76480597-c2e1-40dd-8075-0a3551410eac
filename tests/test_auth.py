import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from app.database.utils import Base
from main import create_app
from app.database.utils import get_db

SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False},
    poolclass=StaticPool
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="function")
def test_db():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def client(test_db):
    def override_get_db():
        try:
            yield test_db
        finally:
            test_db.close()
    
    app = create_app()
    app.dependency_overrides[get_db] = override_get_db
    return TestClient(app)

def test_register_user_success(client):
    response = client.post(
        "/api/v1/register", 
        json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "strongpassword123"
        }
    )
    assert response.status_code == 201
    assert "access_token" in response.json()
    assert response.json()["token_type"] == "bearer"

def test_register_duplicate_email(client):
    client.post(
        "/api/v1/register", 
        json={
            "username": "firstuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    
    response = client.post(
        "/api/v1/register", 
        json={
            "username": "seconduser",
            "email": "<EMAIL>",
            "password": "differentpassword"
        }
    )
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]

def test_register_duplicate_username(client):
    client.post(
        "/api/v1/register", 
        json={
            "username": "existinguser",
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    
    response = client.post(
        "/api/v1/register", 
        json={
            "username": "existinguser",
            "email": "<EMAIL>",
            "password": "differentpassword"
        }
    )
    assert response.status_code == 400
    assert "Username already taken" in response.json()["detail"]

def test_login_success(client):
    client.post(
        "/api/v1/register", 
        json={
            "username": "loginuser",
            "email": "<EMAIL>",
            "password": "correctpassword"
        }
    )
    
    response = client.post(
        "/api/v1/login",
        json={
            "username": "loginuser",
            "password": "correctpassword"
        }
    )
    assert response.status_code == 200
    assert "access_token" in response.json()
    assert response.json()["token_type"] == "bearer"

def test_login_invalid_username(client):
    response = client.post(
        "/api/v1/login",
        json={
            "username": "nonexistentuser",
            "password": "somepassword"
        }
    )
    assert response.status_code == 401
    assert "Invalid username or password" in response.json()["detail"]

def test_login_invalid_password(client):
    client.post(
        "/api/v1/register", 
        json={
            "username": "passwordtest",
            "email": "<EMAIL>",
            "password": "correctpassword"
        }
    )
    
    response = client.post(
        "/api/v1/login",
        json={
            "username": "passwordtest",
            "password": "wrongpassword"
        }
    )
    assert response.status_code == 401
    assert "Invalid username or password" in response.json()["detail"]

def test_register_invalid_email(client):
    response = client.post(
        "/api/v1/register", 
        json={
            "username": "invalidemailuser",
            "email": "not-an-email",
            "password": "password123"
        }
    )
    assert response.status_code == 422