import requests
import pytest

BASE_URL = "http://127.0.0.1:7860/api/v1/agent/2f873a4e-a4f7-4eab-b552-c46a8ab6731f/session/52f5dc62-2ddc-4f4b-a710-1f6489134141/da/query"

AUTH_HEADER = {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************.dfWAKErXlDXFnCDXCLIRdy6zvQ08Svh6rFFDMoriQSE"
}


def test_api_post():
    """Test if the API handles a POST request correctly and returns expected response structure"""
    payload = {"query": "test query"}
    response = requests.post(BASE_URL, json=payload, headers=AUTH_HEADER)

    assert response.status_code in [
        200,
        201,
    ], f"Unexpected status code {response.status_code}"
    assert response.headers["Content-Type"].startswith(
        "application/json"
    ), "Response is not JSON"

    data = response.json()

    # Validate response structure
    assert "chat_name" in data, "Missing 'chat_name' in response"
    assert "content" in data, "Missing 'content' in response"
    assert "folder_id" in data, "Missing 'folder_id' in response"
    assert "plot_urls" in data, "Missing 'plot_urls' in response"

    # Validate content
    assert (
        isinstance(data["chat_name"], str) and data["chat_name"].strip()
    ), "Invalid 'chat_name'"
    assert (
        isinstance(data["content"], str) and data["content"].strip()
    ), "Invalid 'content'"
    assert (
        isinstance(data["folder_id"], str) and data["folder_id"].strip()
    ), "Invalid 'folder_id'"

    # Validate plot URLs structure
    assert isinstance(data["plot_urls"], list), "'plot_urls' should be a list"
    assert len(data["plot_urls"]) > 0, "'plot_urls' list is empty"

    for plot in data["plot_urls"]:
        assert isinstance(plot, dict), "Each plot entry should be a dictionary"
        assert (
            "title" in plot and isinstance(plot["title"], str) and plot["title"].strip()
        ), "Invalid 'title' in plot"
        assert (
            "url" in plot
            and isinstance(plot["url"], str)
            and plot["url"].startswith("http")
        ), "Invalid 'url' in plot"


if __name__ == "__main__":
    pytest.main()
