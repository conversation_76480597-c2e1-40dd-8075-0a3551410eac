import requests

# The presigned URL you received
presigned_url = "https://knowledgebasechat.s3.amazonaws.com/f1babb35-1c5f-45a4-bb81-6a3465f4df32.pdf?AWSAccessKeyId=AKIA4MTWLFETF7DF2ACL&Signature=1wJz%2Fzt8K7fd5v8MDoGaOlsA9Ks%3D&content-type=application%2Fpdf&Expires=1734013768"

# Open the file to upload
with open("../warc.pdf", 'rb') as file:
    # Ensure the Content-Type matches the file's type (e.g., PDF)
    headers = {"Content-Type": "application/pdf"}
    response = requests.put(presigned_url, data=file, headers=headers)

    if response.status_code == 200:
        print("File uploaded successfully!")
    else:
        print(f"File upload failed: {response.text}")
