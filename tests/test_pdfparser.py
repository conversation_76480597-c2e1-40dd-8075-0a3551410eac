import requests
import json

url = "http://127.0.0.1:7860/api/v1/folder/3e7ca7d7-ecc3-48cb-960a-8c4174cd70ae/file"

# Form-data fields
file_metadata = {
    "name": "example_file",
    "type": "text/plain",
    "size": 0,
    "meta_data": {},
    "is_deleted": False
}

# File upload
file_path = "./warc.pdf"

meta_data_dict = {
    "description": "APDF file",
    "author": "pagal Doe"
}

# Convert metadata to a JSON string
meta_data_str = json.dumps(meta_data_dict)


# Prepare form data for fields
data = {
    "name": "example_filename.pdf",
    "type": "application/pdf",
    "meta_data": meta_data_str,
    "is_deleted": "false",
}

# Prepare the file upload tuple: (filename, file_object, content_type)
files = {
    "uploaded_file": ("example_filename.pdf", open(file_path, "rb"), "application/pdf")
}

response = requests.post(url, data=data, files=files)

# Print the response from the server
print("Status Code:", response.status_code)
try:
    print("Response JSON:", response.json())
except ValueError:
    # In case the response is not JSON
    print("Response Text:", response.text)